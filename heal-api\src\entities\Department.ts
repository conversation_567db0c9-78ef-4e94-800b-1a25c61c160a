import { Field, ObjectType } from "type-graphql";
import {
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
} from "typeorm";
import { AuditBaseEntity } from "./AuditEntity";
import { Employee } from "./Employee";
import { Company } from "./Company";

@ObjectType()
@Entity()
export class Department extends AuditBaseEntity {
  @Field(() => String)
  @Column({ type: "text" })
  name!: string;

  @Field(() => Company)
  @ManyToOne(() => Company, (company) => company.departments)
  @JoinColumn({ name: "companyId" })
  company: Company;

  @Field(() => String)
  @Column({ type: "text" })
  description: string;

  @Field(() => String, { nullable: true })
  @Column({ type: "text", nullable: true })
  status: string;

  @Field(() => String, { nullable: true })
  @Column({ type: "uuid", nullable: true })
  parentId: string; // if the department has a parent then its id will be here

  @Field(() => String, { nullable: true })
  @Column({ type: "text", nullable: true })
  type: string;

  @Field(() => Employee, { nullable: true })
  @OneToOne(() => Employee, (employee) => employee.headingDepartment)
  headOfDepartment: Employee;

  @Field(() => [Employee], { nullable: true })
  @OneToMany(() => Employee, (employee) => employee.department)
  employees: Employee[];
}
