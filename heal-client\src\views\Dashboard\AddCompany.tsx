import React, { useState } from "react";
import { useForm } from "react-hook-form";
// Chakra imports
import {
  Flex,
  Button,
  FormControl,
  FormLabel,
  Input,
  Text,
  useColorModeValue,
  FormErrorMessage,
  useToast,
  Grid,
  GridItem,
} from "@chakra-ui/react";
import {
  useGetAllAddressQuery,
  useGetTypesQuery,
  useAddCompanyWithAddressMutation,
} from "../../generated/graphql";
import { useHistory } from "react-router-dom";
import { AsyncCreatableSelect } from "chakra-react-select";
import { HSeparator } from "../../components/Separator/Separator";

interface IAddCompanyProps {}

const AddCompany: React.FC<IAddCompanyProps> = (props: any) => {
  const toast = useToast({
    position: "top",
  });
  const [, addCompany] = useAddCompanyWithAddressMutation();
  const [error, seterror] = useState("");
  const history = useHistory();
  const [{ data, fetching }, getTypesAsync] = useGetTypesQuery();
  const [companyType, setCompanyType] = useState("");
  const [companyRegion, setCompanyRegion] = useState("");
  const [companyDistrict, setCompanyDistrict] = useState("");
  const [companyWard, setCompanyWard] = useState("");
  const [companyStreet, setCompanyStreet] = useState("");

  const [{ data: addresses, fetching: loadingAddresses }, getAyncAddress] =
    useGetAllAddressQuery();

  const fetchRegions: any = async (inputValue: string) => {
    if (!fetching && addresses) {
      console.log("inputValue is...", inputValue);

      if (inputValue) {
        return addresses.getAllAddress
          .filter(
            (t) => t.city.toLowerCase().match(inputValue.toLowerCase()) !== null
          )
          .map((c) => {
            return {
              label: c.city,
              value: c.city,
            };
          });
      } else if (
        inputValue === "" ||
        inputValue === undefined ||
        inputValue === null ||
        inputValue === " "
      ) {
        return addresses.getAllAddress.map((t) => {
          return {
            label: t.city,
            value: t.city,
          };
        });
      }
    }
    await getAyncAddress({ requestPolicy: "network-only" });
    if (addresses) {
      return addresses?.getAllAddress.map((c) => {
        return {
          label: c.city,
          value: c.city,
        };
      });
    } else return [];
  };

  const fetchDistricts: any = async (inputValue: string) => {
    if (!fetching && addresses) {
      console.log("inputValue is...", inputValue);

      if (inputValue) {
        return addresses.getAllAddress
          .filter(
            (t) =>
              t.district.toLowerCase().match(inputValue.toLowerCase()) !== null
          )
          .map((c) => {
            return {
              label: c.district,
              value: c.district,
            };
          });
      } else if (
        inputValue === "" ||
        inputValue === undefined ||
        inputValue === null ||
        inputValue === " "
      ) {
        return addresses.getAllAddress.map((t) => {
          return {
            label: t.district,
            value: t.district,
          };
        });
      }
    }
    await getAyncAddress({ requestPolicy: "network-only" });
    if (addresses) {
      return addresses?.getAllAddress.map((c) => {
        return {
          label: c.district,
          value: c.district,
        };
      });
    } else return [];
  };

  const fetchWards: any = async (inputValue: string) => {
    if (!fetching && addresses) {
      console.log("inputValue is...", inputValue);

      if (inputValue) {
        return addresses.getAllAddress
          .filter(
            (t) => t.ward.toLowerCase().match(inputValue.toLowerCase()) !== null
          )
          .map((c) => {
            return {
              label: c.ward,
              value: c.ward,
            };
          });
      } else if (
        inputValue === "" ||
        inputValue === undefined ||
        inputValue === null ||
        inputValue === " "
      ) {
        return addresses.getAllAddress.map((t) => {
          return {
            label: t.ward,
            value: t.ward,
          };
        });
      }
    }
    await getAyncAddress({ requestPolicy: "network-only" });
    if (addresses) {
      return addresses?.getAllAddress.map((c) => {
        return {
          label: c.ward,
          value: c.ward,
        };
      });
    } else return [];
  };

  const fetchStreets: any = async (inputValue: string) => {
    if (!fetching && addresses) {
      console.log("inputValue is...", inputValue);

      if (inputValue) {
        return addresses.getAllAddress
          .filter(
            (t) =>
              t.street.toLowerCase().match(inputValue.toLowerCase()) !== null
          )
          .map((c) => {
            return {
              label: c.street,
              value: c.street,
            };
          });
      } else if (
        inputValue === "" ||
        inputValue === undefined ||
        inputValue === null ||
        inputValue === " "
      ) {
        return addresses.getAllAddress.map((t) => {
          return {
            label: t.street,
            value: t.street,
          };
        });
      }
    }
    await getAyncAddress({ requestPolicy: "network-only" });
    if (addresses) {
      return addresses?.getAllAddress.map((c) => {
        return {
          label: c.street,
          value: c.street,
        };
      });
    } else return [];
  };

  const handleChange: any = (value: any, field: any) => {
    if (value) {
      if (field.name === "type") {
        setCompanyType(value.label);
      } else if (field.name === "region") {
        setCompanyRegion(value.label);
      } else if (field.name === "district") {
        setCompanyDistrict(value.label);
      } else if (field.name === "ward") {
        setCompanyWard(value.label);
      } else if (field.name === "street") {
        setCompanyStreet(value.label);
      }
    }
  };

  const handleCreateOptionType = (inputValue: string) => {
    history.push({
      pathname: "/admin/add-category",
      state: {
        value: inputValue,
      },
    });
  };

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm();

  const fetchTypes: any = async (inputValue: string) => {
    if (!fetching && data) {
      console.log("inputValue is...", inputValue);

      if (inputValue) {
        const type = data.getTypes.find((t) => {
          return t.name === "Company type";
        });
        return type?.category
          .filter(
            (t) => t.name.toLowerCase().match(inputValue.toLowerCase()) !== null
          )
          .map((t) => {
            return {
              label: t.name,
              value: t.id,
            };
          });
      } else if (
        inputValue === "" ||
        inputValue === undefined ||
        inputValue === null ||
        inputValue === " "
      ) {
        const type = data.getTypes.find((t) => {
          return t.name === "Company type";
        });
        return type?.category.map((t) => {
          return {
            label: t.name,
            value: t.id,
          };
        });
      }
    }
    await getTypesAsync({ requestPolicy: "network-only" });

    if (data) {
      const type = data?.getTypes.find((t) => {
        return t.name === "Company type";
      });
      return type?.category.map((c) => {
        return {
          label: c.name,
          value: c.id,
        };
      });
    } else return [];
  };

  async function onSubmit(values: any) {
    console.log("WELL WE HAVE: ", values);
    seterror("");
    const args = {
      name: values.name,
      tinNumber: values.tin,
      registrationNumber: values.registration,
      type: companyType,
      city: companyRegion,
      district: companyDistrict,
      ward: companyWard,
      street: companyStreet,
    };
    const { data } = await addCompany({ params: args });
    if (data?.addCompanyWithAddress.error) {
      console.log(
        "The error cam eback: ",
        data.addCompanyWithAddress.error.message
      );
      return seterror(data?.addCompanyWithAddress.error.message);
    } else if (!data?.addCompanyWithAddress.error && values.addCategories)
      return history.push({
        pathname: "/admin/categories",
        state: {
          typeName: values.name,
        },
      });
    else {
      reset();
      return toast({
        title: "Type added successful!",
        variant: "left-accent",
        status: "success",
        isClosable: true,
      });
      // return history.push({
      //   pathname: "/admin/add-address",
      //   state: {
      //     target: "company",
      //     companyId: data?.addCompanyWithAddress.id,
      //   },
      // });
    }
  }
  // Chakra color mode
  const textColor = useColorModeValue("gray.700", "white");
  const bgForm = useColorModeValue("white", "navy.800");
  // const titleColor = useColorModeValue("gray.700", "blue.500");
  // const colorIcons = useColorModeValue("gray.700", "white");
  // const bgIcons = useColorModeValue("trasnparent", "navy.700");
  // const bgIconsHover = useColorModeValue("gray.50", "whiteAlpha.100");
  return (
    <Flex position="relative" mb="90px">
      <Flex
        // minH={{ md: "1000px" }}
        h={{ sm: "initial", md: "75vh", lg: "85vh" }}
        w="100%"
        maxW="1044px"
        mx="auto"
        justifyContent="space-between"
        mb="60px"
        pt={{ md: "0px" }}
      >
        <Flex
          w="100%"
          h="100%"
          alignItems="center"
          justifyContent="center"
          mb="60px"
          mt={{ base: "50px", md: "50px" }}
        >
          <Flex
            zIndex="2"
            direction="column"
            w="690px"
            background="transparent"
            borderRadius="15px"
            p="40px"
            mx={{ base: "100px" }}
            m={{ base: "20px", md: "auto" }}
            bg={bgForm}
            boxShadow={useColorModeValue(
              "0px 5px 14px rgba(0, 0, 0, 0.05)",
              "unset"
            )}
          >
            <Text
              fontSize="xl"
              color={textColor}
              fontWeight="bold"
              textAlign="center"
              mb="12px"
            >
              Add Company
            </Text>

            <HSeparator mt="6px" mb="6px" />
            <Text textAlign="center" mt="0px" mb="0px">
              Company Details:
            </Text>
            <HSeparator mb="22px" mt="6px" />
            {error && (
              <Text mb="10px" color="red.500" textColor="red.300">
                {error}
              </Text>
            )}
            <form onSubmit={handleSubmit(onSubmit)}>
              <Grid templateColumns="repeat(6, 1fr)" gap={6}>
                <FormControl
                  as={GridItem}
                  colSpan={3}
                  mb={5}
                  isInvalid={errors.name as any}
                >
                  <FormLabel htmlFor="name" fontSize="xs">
                    Company name
                  </FormLabel>
                  <Input
                    variant="filled"
                    ms="4px"
                    type="text"
                    fontSize="xs"
                    placeholder="Company Name"
                    mb="4px"
                    size="lg"
                    id="name"
                    {...register("name", {
                      required: "This is required",

                      pattern: {
                        value: /^[a-zA-Z- -0-9]+$/,
                        message: "Company name can not have special characters",
                      },
                    })}
                    defaultValue={
                      props.location.state ? props.location.state.typeName : ""
                    }
                  />
                  <FormErrorMessage mb="10px" fontSize="xs">
                    {errors.name && (errors.name.message as any)}
                  </FormErrorMessage>
                </FormControl>
                <FormControl
                  as={GridItem}
                  colSpan={3}
                  mb={5}
                  isInvalid={errors.tin as any}
                >
                  <FormLabel htmlFor="tin" fontSize="xs">
                    Company Tin Number
                  </FormLabel>
                  <Input
                    variant="filled"
                    fontSize="xs"
                    ms="4px"
                    type="text"
                    placeholder="Company Tin Number"
                    mb="4px"
                    size="lg"
                    id="tin"
                    {...register("tin", {
                      required: "This is required",
                      pattern: {
                        value: /^[0-9---]+$/,
                        message:
                          "Tin number can not have special characters or letters",
                      },
                    })}
                  />
                  <FormErrorMessage fontSize="xs">
                    {errors.tin && (errors.tin.message as any)}
                  </FormErrorMessage>
                </FormControl>
                <FormControl
                  as={GridItem}
                  colSpan={3}
                  mb={5}
                  isInvalid={errors.registration as any}
                >
                  <FormLabel htmlFor="registration" fontSize="xs">
                    Company Registration Number
                  </FormLabel>
                  <Input
                    variant="filled"
                    fontSize="xs"
                    ms="4px"
                    type="text"
                    placeholder="Company Registration Number"
                    mb="4px"
                    size="lg"
                    id="registration"
                    {...register("registration", {
                      required: "This is required",
                      pattern: {
                        value: /^[a-zA-Z---0-9]+$/,
                        message:
                          "Reistration number can not have special characters",
                      },
                    })}
                  />
                  <FormErrorMessage fontSize="xs">
                    {errors.registration &&
                      (errors.registration.message as any)}
                  </FormErrorMessage>
                </FormControl>
                <FormControl
                  as={GridItem}
                  colSpan={3}
                  alignItems="center"
                  mt={10}
                  mb="20px"
                  fontSize="xs"
                >
                  <AsyncCreatableSelect
                    variant="flushed"
                    isClearable
                    {...register("type")}
                    name="type"
                    id="type"
                    defaultOptions={true}
                    cacheOptions={false}
                    allowCreateWhileLoading={false}
                    onCreateOption={handleCreateOptionType}
                    isLoading={fetching}
                    loadOptions={fetchTypes}
                    size="sm"
                    placeholder="Select or create company type..."
                    closeMenuOnSelect={true}
                    onChange={handleChange}
                    escapeClearsValue={true}
                    hideSelectedOptions={true}
                  />
                </FormControl>
              </Grid>

              <HSeparator mt="6px" mb="6px" />
              <Text textAlign="center" mt="6px" mb="6px">
                Company Location:
              </Text>
              <HSeparator mb="22px" mt="6px" />

              <Grid templateColumns="repeat(6, 1fr)" gap={6}>
                <FormControl
                  as={GridItem}
                  colSpan={3}
                  mt="10px"
                  alignItems="center"
                  mb="20px"
                >
                  <AsyncCreatableSelect
                    variant="flushed"
                    isClearable
                    {...register("region")}
                    name="region"
                    id="region"
                    size="sm"
                    defaultOptions={true}
                    cacheOptions={true}
                    allowCreateWhileLoading={false}
                    isLoading={loadingAddresses}
                    loadOptions={fetchRegions}
                    placeholder="Select or create a region ..."
                    closeMenuOnSelect={true}
                    onChange={handleChange}
                    escapeClearsValue={true}
                    hideSelectedOptions={true}
                  />
                </FormControl>
                <FormControl
                  as={GridItem}
                  colSpan={3}
                  mt="10px"
                  alignItems="center"
                  mb="20px"
                >
                  <AsyncCreatableSelect
                    variant="flushed"
                    isClearable
                    {...register("district")}
                    name="district"
                    id="district"
                    size="sm"
                    defaultOptions={true}
                    cacheOptions={true}
                    allowCreateWhileLoading={false}
                    isLoading={loadingAddresses}
                    loadOptions={fetchDistricts}
                    placeholder="Select or create a district ..."
                    closeMenuOnSelect={true}
                    onChange={handleChange}
                    escapeClearsValue={true}
                    hideSelectedOptions={true}
                  />
                </FormControl>
                <FormControl
                  as={GridItem}
                  colSpan={3}
                  mt="10px"
                  alignItems="center"
                  mb="20px"
                >
                  <AsyncCreatableSelect
                    variant="flushed"
                    isClearable
                    {...register("ward")}
                    name="ward"
                    id="ward"
                    size="sm"
                    defaultOptions={true}
                    cacheOptions={true}
                    allowCreateWhileLoading={false}
                    isLoading={loadingAddresses}
                    loadOptions={fetchWards}
                    placeholder="Select or create a ward ..."
                    closeMenuOnSelect={true}
                    onChange={handleChange}
                    escapeClearsValue={true}
                    hideSelectedOptions={true}
                  />
                </FormControl>
                <FormControl
                  as={GridItem}
                  colSpan={3}
                  mt="10px"
                  alignItems="center"
                  mb="20px"
                >
                  <AsyncCreatableSelect
                    variant="flushed"
                    isClearable
                    {...register("street")}
                    name="street"
                    id="street"
                    size="sm"
                    defaultOptions={true}
                    cacheOptions={true}
                    allowCreateWhileLoading={false}
                    isLoading={loadingAddresses}
                    loadOptions={fetchStreets}
                    placeholder="Select or create a street ..."
                    closeMenuOnSelect={true}
                    onChange={handleChange}
                    escapeClearsValue={true}
                    hideSelectedOptions={true}
                  />
                </FormControl>
              </Grid>

              <Button
                fontSize="14px"
                variant="dark"
                fontWeight="bold"
                w="100%"
                h="45"
                mb="24px"
                mt={4}
                colorScheme="teal"
                isLoading={isSubmitting}
                type="submit"
              >
                Add Company
              </Button>
            </form>
          </Flex>
        </Flex>
      </Flex>
    </Flex>
  );
};

export default AddCompany;
