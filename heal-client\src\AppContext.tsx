import * as React from "react";
import IAppState, { IAppContext } from "./interfaces/AppState";
import { IAction } from "./interfaces/Types";

export const initialState: IAppState = {
  pendingFile: undefined,
};

const AppContext = React.createContext<IAppContext>({
  state: {
    pendingFile: undefined,
  },
  dispatch: () => {},
});

/**
 * Reducer function should simply digest the action payload and return a new state object
 */
const appReducer = (state: IAppState, action: IAction): typeof initialState => {
  console.log(`Sample Reducer called: ${action.type}`);

  const pendingFile = action.pendingFile;

  switch (action.type) {
    case "SET_UPLOADING_FILE_URL": {
      if (pendingFile)
        return {
          ...state,
          pendingFile: pendingFile,
        };
      return {
        ...state,
      };
    }
    case "CLEAR_UPLOADING_FILE_URL": {
      return {
        ...state,
        pendingFile: undefined,
      };
    }
    default:
      throw new Error();
  }
};

const AppContextProvider: React.FunctionComponent<React.PropsWithChildren> = ({
  children,
}) => {
  const [state, dispatch] = React.useReducer(
    appReducer,
    initialState as IAppState
  );
  const value = { state, dispatch };
  return <AppContext.Provider value={value}>{children}</AppContext.Provider>;
};

export { AppContext, AppContextProvider };
