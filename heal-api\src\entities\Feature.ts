import { Column, <PERSON>ti<PERSON>, JoinTable, ManyToMany } from "typeorm";
import { AuditBaseEntity } from "./AuditEntity";
import { Field, ObjectType } from "type-graphql";
import { Company } from "./Company";

@ObjectType()
@Entity()
export class Feature extends AuditBaseEntity {
  @Field(() => String)
  @Column({ type: "text", unique: true })
  name!: string;

  @Field(() => [Company], { nullable: true })
  @ManyToMany(() => Company, (company) => company.features)
  @JoinTable({
    name: "company_features",
    joinColumns: [
      { name: "featureId", referencedColumnName: "id" },
      { name: "companyId", referencedColumnName: "companyId" },
    ],
    inverseJoinColumns: [{ name: "companyId", referencedColumnName: "id" }],
  })
  companies: Company[];
}
