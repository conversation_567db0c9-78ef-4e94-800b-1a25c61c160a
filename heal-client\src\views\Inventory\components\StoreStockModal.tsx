import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>nt,
  <PERSON><PERSON><PERSON>er,
  ModalCloseButton,
  ModalBody,
  Modal<PERSON>ooter,
  Button,
  Box,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  useColorModeValue,
} from "@chakra-ui/react";
import { BatchStock, Item, StoreItemStock } from "../../../generated/graphql";

interface StoreStockModalProps {
  isOpen: boolean;
  onClose: () => void;
  store: StoreItemStock | null;
  item: Item;
  batches: BatchStock[];
}

const StoreStockModal = ({
  isOpen,
  onClose,
  store,
  item,
  batches,
}: StoreStockModalProps) => {
  const borderColor = useColorModeValue("gray.200", "gray.600");
  const textColor = useColorModeValue("gray.700", "white");

  if (!store) return null;

  // Filter store item stocks within each batch to only include those for the specific store
  const storeBatches = batches
    .map((batch) => {
      const filteredStoreItemStocks = batch.storeItemStocks.filter(
        (stock) => stock.storeId === store.storeId
      );
      return { ...batch, storeItemStocks: filteredStoreItemStocks };
    })
    .filter((batch) => batch.storeItemStocks.length > 0);

  const totalStock = {
    stock: storeBatches.reduce(
      (acc, batch) =>
        acc +
        batch.storeItemStocks.reduce((sum, stock) => sum + stock.stock, 0),
      0
    ),
    pieceStock: storeBatches.reduce(
      (acc, batch) =>
        acc +
        batch.storeItemStocks.reduce((sum, stock) => sum + stock.pieceStock, 0),
      0
    ),
    subPieceStock: storeBatches.reduce(
      (acc, batch) =>
        acc +
        batch.storeItemStocks.reduce(
          (sum, stock) => sum + stock.subPieceStock,
          0
        ),
      0
    ),
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>Store Stock Details</ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <Box>
            <Table variant="simple" color={textColor}>
              <Thead>
                <Tr>
                  <Th borderColor={borderColor}>Batch Identifier</Th>
                  <Th borderColor={borderColor}>Stock in {item.unit}</Th>
                  {item.pieceUnit && (
                    <Th borderColor={borderColor}>Stock in {item.pieceUnit}</Th>
                  )}
                  {item.subPieceUnit && (
                    <Th borderColor={borderColor}>
                      Stock in {item.subPieceUnit}
                    </Th>
                  )}
                </Tr>
              </Thead>
              <Tbody>
                {storeBatches.map((batch) => (
                  <Tr key={batch.id}>
                    <Td borderColor={borderColor}>{batch.batch}</Td>
                    <Td borderColor={borderColor}>
                      {batch.storeItemStocks.reduce(
                        (acc, stock) => acc + stock.stock,
                        0
                      )}
                    </Td>
                    {item.pieceUnit && (
                      <Td borderColor={borderColor}>
                        {batch.storeItemStocks.reduce(
                          (acc, stock) => acc + stock.pieceStock,
                          0
                        )}
                      </Td>
                    )}
                    {item.subPieceUnit && (
                      <Td borderColor={borderColor}>
                        {batch.storeItemStocks.reduce(
                          (acc, stock) => acc + stock.subPieceStock,
                          0
                        )}
                      </Td>
                    )}
                  </Tr>
                ))}
                <Tr>
                  <Td borderColor={borderColor} fontWeight="bold">
                    Total
                  </Td>
                  <Td borderColor={borderColor} fontWeight="bold">
                    {totalStock.stock}
                  </Td>
                  {item.pieceUnit && (
                    <Td fontWeight="bold" borderColor={borderColor}>
                      {totalStock.pieceStock}
                    </Td>
                  )}
                  {item.subPieceUnit && (
                    <Td fontWeight="bold" borderColor={borderColor}>
                      {totalStock.subPieceStock}
                    </Td>
                  )}
                </Tr>
              </Tbody>
            </Table>
          </Box>
        </ModalBody>
        <ModalFooter>
          <Button colorScheme="blue" mr={3} onClick={onClose}>
            Close
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default StoreStockModal;
