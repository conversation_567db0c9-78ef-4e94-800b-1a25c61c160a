        <Router>
          <Switch>
            {/* protected routes  */}
            <AuthRoute exact path="/admin/dashboard" component={AdminLayout} />
            <AuthRoute
              exact
              path="/employee/dashboard"
              component={EmployeeLayout}
            />
            <AuthRoute
              exact
              path="/employee/users"
              component={EmployeeLayout}
            />
            <AuthRoute exact path="/admin/users" component={EmployeeLayout} />
            <AuthRoute
              exact
              path="/employee/internal/stock"
              component={EmployeeLayout}
            />
            <AuthRoute
              exact
              path="/admin/internal/stock"
              component={AdminLayout}
            />
            <AuthRoute
              exact
              path="/employee/internal/add-item"
              component={EmployeeLayout}
            />
            <AuthRoute
              exact
              path="/admin/internal/add-item"
              component={EmployeeLayout}
            />
            <AuthRoute
              exact
              path="/employee/merch/add-item"
              component={EmployeeLayout}
            />
            <AuthRoute
              exact
              path="/admin/merch/add-item"
              component={EmployeeLayout}
            />
            <AuthRoute
              exact
              path="/employee/merch/stock"
              component={EmployeeLayout}
            />
            <AuthRoute
              exact
              path="/admin/merch/stock"
              component={EmployeeLayout}
            />
            <AuthRoute exact path="/admin/tables" component={AdminLayout} />
            <AuthRoute exact path="/admin/billing" component={AdminLayout} />
            <AuthRoute exact path="/admin/profile" component={AdminLayout} />
            <AuthRoute exact path="/admin/roles" component={AdminLayout} />
            <AuthRoute
              exact
              path="/admin/edit-country"
              component={AdminLayout}
            />
            <AuthRoute exact path="/admin/employee" component={AdminLayout} />
            <AuthRoute exact path="/admin/types" component={AdminLayout} />
            <AuthRoute exact path="/admin/add-type" component={AdminLayout} />
            <AuthRoute
              exact
              path="/admin/add-address"
              component={AdminLayout}
            />
            <AuthRoute exact path="/admin/edit-type" component={AdminLayout} />
            <AuthRoute exact path="/admin/categories" component={AdminLayout} />
            <AuthRoute
              exact
              path="/admin/permissions"
              component={AdminLayout}
            />
            <AuthRoute exact path="/admin/features" component={AdminLayout} />
            <AuthRoute exact path="/admin/companies" component={AdminLayout} />
            <AuthRoute
              exact
              path="/admin/add-company"
              component={AdminLayout}
            />
            <AuthRoute exact path="/admin/roles" component={AdminLayout} />
            <AuthRoute
              exact
              path="/employee/add-user"
              component={EmployeeLayout}
            />
            <AuthRoute exact path="/admin/add-user" component={AdminLayout} />
            <AuthRoute
              exact
              path="/admin/add-category"
              component={AdminLayout}
            />
            <AuthRoute
              exact
              path="/admin/edit-category"
              component={AdminLayout}
            />
            <AuthRoute
              exact
              path="/admin/add-category"
              component={AdminLayout}
            />
            <AuthRoute
              exact
              path="/admin/edit-category"
              component={AdminLayout}
            />

            {/* Unprotected routes */}
            <UnAuthRoute exact path="/login" component={Login} user="user" />
            <UnAuthRoute
              exact
              path="/forgot-password"
              component={Forgot_password}
            />
            <UnAuthRoute
              exact
              path="/reset-password"
              component={Reset_password}
            />
          </Switch>
        </Router>