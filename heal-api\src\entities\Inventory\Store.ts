import { Field, ObjectType } from "type-graphql";
import {
  Column,
  Entity,
  OneToMany,
  Unique,
  BeforeInsert,
  BeforeUpdate,
} from "typeorm";
import { AuditBaseEntity } from "../AuditEntity";
import { Inventory } from "./Inventory";
import { getRepository } from "typeorm";
import { Employee } from "../Employee";
import { StoreItemStock } from "./StoreItemStock";

@ObjectType()
@Entity()
@Unique(["companyId", "name"])
export class Store extends AuditBaseEntity {
  @Field(() => String)
  @Column({ type: "text" })
  name!: string;

  @Field(() => Boolean)
  @Column({
    type: "boolean",
    default: false,
  })
  primary!: boolean; // inventory action type, what is done to our inventory

  @Field(() => String)
  @Column({ type: "text" })
  address!: string;

  @Field(() => [Inventory])
  @OneToMany(() => Inventory, (inventory) => inventory.sourceStoreId)
  stockOut: Inventory[];

  @Field(() => [Inventory])
  @OneToMany(() => Inventory, (inventory) => inventory.destinationStoreId)
  stockIn: Inventory[];

  @Field(() => [Employee], { nullable: true })
  @OneToMany(() => Employee, (employee) => employee.store)
  storeKeepers: Employee[];

  @Field(() => [StoreItemStock], { nullable: true })
  @OneToMany(() => StoreItemStock, (storeItemStock) => storeItemStock.store)
  storeItemStocks: StoreItemStock[];

  @BeforeInsert()
  @BeforeUpdate()
  async checkPrimaryStore() {
    if (this.primary) {
      const existingPrimaryStore = await getRepository(Store).findOne({
        where: { companyId: this.companyId, primary: true },
      });

      if (existingPrimaryStore && existingPrimaryStore.id !== this.id) {
        throw new Error("There can only be one primary store per company.");
      }
    }
  }
}
