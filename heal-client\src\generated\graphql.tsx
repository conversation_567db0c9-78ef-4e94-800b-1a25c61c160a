import gql from 'graphql-tag';
import * as Urql from 'urql';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type Omit<T, K extends keyof T> = Pick<T, Exclude<keyof T, K>>;
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: string;
  String: string;
  Boolean: boolean;
  Int: number;
  Float: number;
  DateTime: any;
};

export type Address = {
  __typename?: 'Address';
  city: Scalars['String'];
  companies: Array<Company>;
  country: Scalars['String'];
  createdAt: Scalars['String'];
  deleted: Scalars['Boolean'];
  district: Scalars['String'];
  id: Scalars['Float'];
  patients: Array<Patient>;
  street: Scalars['String'];
  updatedAt: Scalars['String'];
  users: Array<User>;
  ward: Scalars['String'];
  zip: Scalars['String'];
};

export type AddressType = {
  city: Scalars['String'];
  country: Scalars['String'];
  district: Scalars['String'];
  street: Scalars['String'];
  ward: Scalars['String'];
  zip: Scalars['String'];
};

export type Approval = {
  __typename?: 'Approval';
  approvalDate?: Maybe<Scalars['DateTime']>;
  approver?: Maybe<Employee>;
  approverId?: Maybe<Scalars['Float']>;
  createdAt: Scalars['String'];
  deleted: Scalars['Boolean'];
  feature: Scalars['String'];
  id: Scalars['Float'];
  requestId: Scalars['Float'];
  requester?: Maybe<Employee>;
  requesterId?: Maybe<Scalars['Float']>;
  status: Scalars['Boolean'];
  type: Scalars['String'];
  updatedAt: Scalars['String'];
};

export type BatchStock = {
  __typename?: 'BatchStock';
  batch: Scalars['String'];
  createdAt: Scalars['String'];
  deleted: Scalars['Boolean'];
  expireDate: Scalars['String'];
  id: Scalars['Float'];
  item: Item;
  itemId: Scalars['Float'];
  pieceStock: Scalars['Float'];
  stock: Scalars['Float'];
  storeItemStocks: Array<StoreItemStock>;
  subPieceStock: Scalars['Float'];
  updatedAt: Scalars['String'];
};

export type Bill = {
  __typename?: 'Bill';
  amount: Scalars['Float'];
  cleared: Scalars['Boolean'];
  client?: Maybe<Patient>;
  clientId?: Maybe<Scalars['Float']>;
  companyId: Scalars['Float'];
  createdAt: Scalars['String'];
  deleted: Scalars['Boolean'];
  id: Scalars['Float'];
  inventoryId: Scalars['Float'];
  inventoryTransfer: Inventory;
  paymentType: Scalars['String'];
  updatedAt: Scalars['String'];
  visit?: Maybe<Visit>;
  visitId?: Maybe<Scalars['Float']>;
};

export type BooleanResponse = {
  __typename?: 'BooleanResponse';
  error?: Maybe<FieldError>;
  status: Scalars['Boolean'];
};

export type BooleanResponseId = {
  __typename?: 'BooleanResponseId';
  error?: Maybe<FieldError>;
  id?: Maybe<Scalars['Float']>;
  status: Scalars['Boolean'];
};

export type BooleanResponseWithType = {
  __typename?: 'BooleanResponseWithType';
  data?: Maybe<Type>;
  error?: Maybe<FieldError>;
  status: Scalars['Boolean'];
};

export type BulkItemInput = {
  items: Array<ItemInput>;
};

export type BulkScheduleResponse = {
  __typename?: 'BulkScheduleResponse';
  error?: Maybe<FieldError>;
  schedules?: Maybe<Array<Schedule>>;
  status: Scalars['Boolean'];
};

export type Category = {
  __typename?: 'Category';
  createdAt: Scalars['String'];
  deleted: Scalars['Boolean'];
  id: Scalars['Float'];
  name: Scalars['String'];
  parentCategoryId?: Maybe<Scalars['Float']>;
  type: Type;
  updatedAt: Scalars['String'];
  user: Array<User>;
};

export type CategoryArgs = {
  name: Scalars['String'];
  type: Scalars['Float'];
};

export type Clinic = {
  __typename?: 'Clinic';
  clinicType: Scalars['String'];
  createdAt: Scalars['String'];
  deleted: Scalars['Boolean'];
  department: Department;
  departmentId: Scalars['Float'];
  description: Scalars['String'];
  id: Scalars['Float'];
  leader?: Maybe<Employee>;
  leaderId?: Maybe<Scalars['Float']>;
  name: Scalars['String'];
  schedules: Array<Schedule>;
  size: Scalars['Float'];
  status: Scalars['String'];
  updatedAt: Scalars['String'];
  visitsToClinic: Array<VisitToClinic>;
};

export type ClinicEditArgs = {
  clinicType: Scalars['String'];
  description: Scalars['String'];
  leaderId?: InputMaybe<Scalars['Int']>;
  name: Scalars['String'];
  size: Scalars['Int'];
  status: Scalars['String'];
};

export type ClinicInputArgs = {
  clinicType: Scalars['String'];
  departmentId: Scalars['Int'];
  description: Scalars['String'];
  leaderId?: InputMaybe<Scalars['Int']>;
  name: Scalars['String'];
  size: Scalars['Int'];
  status: Scalars['String'];
};

export type ClinicResponse = {
  __typename?: 'ClinicResponse';
  clinic?: Maybe<Clinic>;
  error?: Maybe<FieldError>;
  status: Scalars['Boolean'];
};

export type Company = {
  __typename?: 'Company';
  branches: Array<Scalars['Float']>;
  createdAt: Scalars['String'];
  deleted: Scalars['Boolean'];
  departments: Array<Department>;
  email: Scalars['String'];
  employees: Array<Employee>;
  features: Array<Feature>;
  id: Scalars['Float'];
  isBranch: Scalars['Boolean'];
  isParent: Scalars['Boolean'];
  location: Address;
  logo: Scalars['String'];
  name: Scalars['String'];
  parentId: Scalars['Float'];
  phone: Scalars['String'];
  poBox: Scalars['String'];
  registrationNumber: Scalars['String'];
  tinNumber: Scalars['String'];
  type: Scalars['String'];
  updatedAt: Scalars['String'];
  users: Array<User>;
  website: Scalars['String'];
};

/** The days of the week */
export enum DayOfWeek {
  Friday = 'FRIDAY',
  Monday = 'MONDAY',
  Saturday = 'SATURDAY',
  Sunday = 'SUNDAY',
  Thursday = 'THURSDAY',
  Tuesday = 'TUESDAY',
  Wednesday = 'WEDNESDAY'
}

export type Department = {
  __typename?: 'Department';
  clinics?: Maybe<Array<Clinic>>;
  company: Company;
  companyId: Scalars['Float'];
  createdAt: Scalars['String'];
  deleted: Scalars['Boolean'];
  description: Scalars['String'];
  employees: Array<Employee>;
  headOfDepartment?: Maybe<Employee>;
  id: Scalars['Float'];
  name: Scalars['String'];
  parentId?: Maybe<Scalars['Float']>;
  status?: Maybe<Scalars['String']>;
  type?: Maybe<Scalars['String']>;
  updatedAt: Scalars['String'];
};

export type DepartmentInputArgs = {
  description?: InputMaybe<Scalars['String']>;
  headOfDepartmentId?: InputMaybe<Scalars['Float']>;
  name: Scalars['String'];
  parentId?: InputMaybe<Scalars['Float']>;
  status?: InputMaybe<Scalars['String']>;
  type?: InputMaybe<Scalars['String']>;
};

export type DispatchInput = {
  batch?: InputMaybe<Scalars['String']>;
  itemId: Scalars['Float'];
  locationId: Scalars['Float'];
  quantity: Scalars['Float'];
  remarks?: InputMaybe<Scalars['String']>;
  unit: Scalars['String'];
};

export type EditUserArgs = {
  email: Scalars['String'];
  firstname: Scalars['String'];
  lastname: Scalars['String'];
  middlename: Scalars['String'];
  phone: Scalars['String'];
};

export type EmailPasswordArgs = {
  email: Scalars['String'];
  password: Scalars['String'];
};

export type Employee = {
  __typename?: 'Employee';
  approvedApprovals: Array<Approval>;
  approved_stock: Array<Inventory>;
  attended: Array<VisitToClinic>;
  authorizedExpenses: Array<Expense>;
  company: Company;
  companyId: Scalars['Float'];
  createdAt: Scalars['String'];
  deleted: Scalars['Boolean'];
  department: Department;
  departmentId: Scalars['Float'];
  designation: Scalars['String'];
  headingDepartment?: Maybe<Department>;
  headingDepartmentId?: Maybe<Scalars['Float']>;
  id: Scalars['Float'];
  image: Scalars['String'];
  leadClinic: Clinic;
  licenceNumber: Scalars['String'];
  received_stock: Array<Inventory>;
  registeredPatients: Array<Patient>;
  requestedApprovals: Array<Approval>;
  requestedExpenses: Array<Expense>;
  role: Role;
  roleId: Scalars['Float'];
  schedules: Array<Schedule>;
  served_stock: Array<Inventory>;
  status: Scalars['String'];
  store: Store;
  storeId: Scalars['Float'];
  updatedAt: Scalars['String'];
  user: User;
  userId: Scalars['Float'];
};

export type Expense = {
  __typename?: 'Expense';
  amount: Scalars['Float'];
  assetId: Scalars['Float'];
  assetType: Scalars['String'];
  authorizer: Employee;
  authorizerId: Scalars['Float'];
  createdAt: Scalars['String'];
  deleted: Scalars['Boolean'];
  details: Scalars['String'];
  expenseDate: Scalars['String'];
  id: Scalars['Float'];
  requester: Employee;
  requesterId: Scalars['Float'];
  status: Scalars['String'];
  title: Scalars['String'];
  type: Scalars['String'];
  updatedAt: Scalars['String'];
};

export type ExpenseInput = {
  amount: Scalars['Float'];
  assetId?: InputMaybe<Scalars['Float']>;
  assetType?: InputMaybe<Scalars['String']>;
  details: Scalars['String'];
  expenseDate: Scalars['String'];
  title: Scalars['String'];
  type: Scalars['String'];
};

export type Feature = {
  __typename?: 'Feature';
  companies: Array<Company>;
  createdAt: Scalars['String'];
  deleted: Scalars['Boolean'];
  id: Scalars['Float'];
  name: Scalars['String'];
  updatedAt: Scalars['String'];
};

export type FieldError = {
  __typename?: 'FieldError';
  message: Scalars['String'];
  target: Scalars['String'];
};

export type Import = {
  __typename?: 'Import';
  batch: Scalars['String'];
  companyId: Scalars['Float'];
  createdAt: Scalars['String'];
  deleted: Scalars['Boolean'];
  expireDate: Scalars['String'];
  id: Scalars['Float'];
  importDate: Scalars['String'];
  importPrice: Scalars['Float'];
  item: Item;
  itemId: Scalars['Float'];
  pieceSellingPrice: Scalars['Float'];
  quantity: Scalars['Float'];
  receipt: Scalars['String'];
  sellingPrice: Scalars['Float'];
  subPieceSellingPrice: Scalars['Float'];
  supplier: Scalars['String'];
  unit: Scalars['String'];
  updatedAt: Scalars['String'];
};

export type ImportInput = {
  batch: Scalars['String'];
  expireDate: Scalars['String'];
  importDate: Scalars['String'];
  importPrice: Scalars['Float'];
  itemId: Scalars['Float'];
  pieceSellingPrice?: InputMaybe<Scalars['Float']>;
  quantity: Scalars['Float'];
  receipt?: InputMaybe<Scalars['String']>;
  sellingPrice?: InputMaybe<Scalars['Float']>;
  subPieceSellingPrice?: InputMaybe<Scalars['Float']>;
  supplier: Scalars['String'];
  unit: Scalars['String'];
};

export type Inventory = {
  __typename?: 'Inventory';
  approver?: Maybe<Employee>;
  approverId?: Maybe<Scalars['Float']>;
  bill?: Maybe<Bill>;
  companyId: Scalars['Float'];
  consumer?: Maybe<Employee>;
  consumerId?: Maybe<Scalars['Float']>;
  createdAt: Scalars['String'];
  deleted: Scalars['Boolean'];
  destinationStore?: Maybe<Store>;
  destinationStoreId?: Maybe<Scalars['Float']>;
  details?: Maybe<Scalars['String']>;
  dispatched: Scalars['Boolean'];
  granted: Scalars['Boolean'];
  id: Scalars['Float'];
  items: Array<Item>;
  keeper?: Maybe<Employee>;
  keeperId?: Maybe<Scalars['Float']>;
  received: Scalars['Boolean'];
  returnDate?: Maybe<Scalars['String']>;
  sourceStore?: Maybe<Store>;
  sourceStoreId?: Maybe<Scalars['Float']>;
  transferDate: Scalars['String'];
  transfers: Array<Transfer>;
  type: Scalars['String'];
  updatedAt: Scalars['String'];
};

export type Item = {
  __typename?: 'Item';
  batchStocks: Array<BatchStock>;
  companyId: Scalars['Float'];
  createdAt: Scalars['String'];
  deleted: Scalars['Boolean'];
  description: Scalars['String'];
  id: Scalars['Float'];
  imports: Array<Import>;
  internal: Scalars['Boolean'];
  inventoryTransfers: Array<Inventory>;
  name: Scalars['String'];
  pieceSellingPrice: Scalars['Float'];
  pieceStock: Scalars['Float'];
  pieceUnit?: Maybe<Scalars['String']>;
  pieces?: Maybe<Scalars['Float']>;
  reference: Scalars['String'];
  reorder: Scalars['Float'];
  sellingPrice: Scalars['Float'];
  stock: Scalars['Float'];
  storeItemStocks: Array<StoreItemStock>;
  subPieceSellingPrice: Scalars['Float'];
  subPieceStock: Scalars['Float'];
  subPieceUnit?: Maybe<Scalars['String']>;
  subPieces?: Maybe<Scalars['Float']>;
  transfers: Array<Transfer>;
  type: Scalars['String'];
  unit: Scalars['String'];
  updatedAt: Scalars['String'];
};

export type ItemInput = {
  description?: InputMaybe<Scalars['String']>;
  internal?: InputMaybe<Scalars['Boolean']>;
  name: Scalars['String'];
  pieceSellingPrice?: InputMaybe<Scalars['Float']>;
  pieceUnit?: InputMaybe<Scalars['String']>;
  pieces?: InputMaybe<Scalars['Float']>;
  reference?: InputMaybe<Scalars['String']>;
  reorder: Scalars['Float'];
  sellingPrice?: InputMaybe<Scalars['Float']>;
  subPieceSellingPrice?: InputMaybe<Scalars['Float']>;
  subPieceUnit?: InputMaybe<Scalars['String']>;
  subPieces?: InputMaybe<Scalars['Float']>;
  type: Scalars['String'];
  unit: Scalars['String'];
};

export type Mutation = {
  __typename?: 'Mutation';
  addAddress: BooleanResponse;
  addCategory: BooleanResponse;
  addClinic: ClinicResponse;
  addCompanyWithAddress: BooleanResponse;
  addDepartment: BooleanResponse;
  addExpense: BooleanResponse;
  addFeature: BooleanResponse;
  addItem: BooleanResponse;
  addItemsFromExcel: BooleanResponse;
  addPermission: BooleanResponse;
  addRole: BooleanResponse;
  addSchedules: BulkScheduleResponse;
  addService: BooleanResponse;
  addStore: BooleanResponse;
  addType: BooleanResponseWithType;
  addVisit: VisitResponse;
  addVitals: VisitResponse;
  changeInventoryApprovalStatus: BooleanResponse;
  changeInventoryDispatchedStatus: BooleanResponse;
  changeInventoryReceivedStatus: BooleanResponse;
  changeInventorySoldStatus: BooleanResponse;
  clearBill: BooleanResponse;
  deleteCategory: BooleanResponse;
  deleteClinic: BooleanResponse;
  deleteFeature: BooleanResponse;
  deleteItem: BooleanResponse;
  deletePermission: BooleanResponse;
  deleteRole: BooleanResponse;
  deleteSchedule: BooleanResponse;
  deleteType: BooleanResponse;
  dispatchItems: BooleanResponse;
  editCategory: BooleanResponse;
  editCategoryByName: BooleanResponse;
  editClinic: ClinicResponse;
  editDepartment: BooleanResponse;
  editExpense: BooleanResponse;
  editFeature: BooleanResponse;
  editItem: BooleanResponse;
  editPatient: PatientResponse;
  editPermission: BooleanResponse;
  editRole: BooleanResponse;
  editSchedules: BulkScheduleResponse;
  editService: BooleanResponse;
  editStore: BooleanResponse;
  editType: BooleanResponse;
  editUser: BooleanResponse;
  forgotPassword: BooleanResponse;
  importItem: BooleanResponse;
  login: UserResponse;
  logout: Scalars['Boolean'];
  manageUserPermissions: BooleanResponse;
  quickSale: BooleanResponse;
  register: BooleanResponse;
  registerCompany: BooleanResponseId;
  registerPatient: PatientResponse;
  resetPassword: UserResponse;
  setHeadOfDepartment: BooleanResponse;
  transferItems: BooleanResponse;
  writeOffItems: BooleanResponse;
};


export type MutationAddAddressArgs = {
  id: Scalars['Float'];
  params: AddressType;
  target: Scalars['String'];
};


export type MutationAddCategoryArgs = {
  args: CategoryArgs;
};


export type MutationAddClinicArgs = {
  params: ClinicInputArgs;
};


export type MutationAddCompanyWithAddressArgs = {
  params: RegisterCompanyAddressedArgs;
};


export type MutationAddDepartmentArgs = {
  params: DepartmentInputArgs;
};


export type MutationAddExpenseArgs = {
  args: ExpenseInput;
};


export type MutationAddFeatureArgs = {
  companyId: Scalars['Float'];
  name: Scalars['String'];
};


export type MutationAddItemArgs = {
  args: ItemInput;
};


export type MutationAddItemsFromExcelArgs = {
  args: BulkItemInput;
};


export type MutationAddPermissionArgs = {
  name: Scalars['String'];
  roleId?: InputMaybe<Scalars['Float']>;
  userId?: InputMaybe<Scalars['Float']>;
};


export type MutationAddRoleArgs = {
  name: Scalars['String'];
};


export type MutationAddSchedulesArgs = {
  args: ScheduleBulkArgs;
};


export type MutationAddServiceArgs = {
  args: ServiceInput;
};


export type MutationAddStoreArgs = {
  args: StoreInput;
};


export type MutationAddTypeArgs = {
  args: TypeArgs;
};


export type MutationAddVisitArgs = {
  params: VisitInputArgs;
};


export type MutationAddVitalsArgs = {
  params: VitalsInputArgs;
};


export type MutationChangeInventoryApprovalStatusArgs = {
  inventoryId: Scalars['Float'];
};


export type MutationChangeInventoryDispatchedStatusArgs = {
  inventoryId: Scalars['Float'];
};


export type MutationChangeInventoryReceivedStatusArgs = {
  inventoryId: Scalars['Float'];
};


export type MutationChangeInventorySoldStatusArgs = {
  inventoryId: Scalars['Float'];
};


export type MutationClearBillArgs = {
  saleId: Scalars['Float'];
};


export type MutationDeleteCategoryArgs = {
  id: Scalars['Float'];
};


export type MutationDeleteClinicArgs = {
  id: Scalars['Int'];
};


export type MutationDeleteFeatureArgs = {
  id: Scalars['Float'];
};


export type MutationDeleteItemArgs = {
  id: Scalars['Float'];
};


export type MutationDeletePermissionArgs = {
  id: Scalars['Float'];
};


export type MutationDeleteRoleArgs = {
  id: Scalars['Float'];
};


export type MutationDeleteScheduleArgs = {
  id: Scalars['Float'];
};


export type MutationDeleteTypeArgs = {
  id: Scalars['Float'];
};


export type MutationDispatchItemsArgs = {
  args: Array<DispatchInput>;
};


export type MutationEditCategoryArgs = {
  args: CategoryArgs;
  id: Scalars['Float'];
};


export type MutationEditCategoryByNameArgs = {
  args: CategoryArgs;
  name: Scalars['String'];
};


export type MutationEditClinicArgs = {
  id: Scalars['Int'];
  params: ClinicEditArgs;
};


export type MutationEditDepartmentArgs = {
  id: Scalars['Float'];
  params: DepartmentInputArgs;
};


export type MutationEditExpenseArgs = {
  args: ExpenseInput;
  id: Scalars['Float'];
};


export type MutationEditFeatureArgs = {
  id: Scalars['Float'];
  name: Scalars['String'];
};


export type MutationEditItemArgs = {
  args: ItemInput;
  id: Scalars['Float'];
};


export type MutationEditPatientArgs = {
  id: Scalars['Float'];
  params: RegisterPatientArgs;
};


export type MutationEditPermissionArgs = {
  id: Scalars['Float'];
  name: Scalars['String'];
};


export type MutationEditRoleArgs = {
  args: RoleArgs;
  id: Scalars['Float'];
};


export type MutationEditSchedulesArgs = {
  args: ScheduleEditBulkArgs;
};


export type MutationEditServiceArgs = {
  args: ServiceInput;
  id: Scalars['Float'];
};


export type MutationEditStoreArgs = {
  args: StoreEditInput;
};


export type MutationEditTypeArgs = {
  args: TypeEditArgs;
  id: Scalars['Float'];
};


export type MutationEditUserArgs = {
  id: Scalars['Float'];
  params: EditUserArgs;
};


export type MutationForgotPasswordArgs = {
  email: Scalars['String'];
};


export type MutationImportItemArgs = {
  args: ImportInput;
};


export type MutationLoginArgs = {
  params: EmailPasswordArgs;
};


export type MutationManageUserPermissionsArgs = {
  id: Scalars['Float'];
  permissions: Array<Scalars['Float']>;
};


export type MutationQuickSaleArgs = {
  args: Array<SaleInput>;
};


export type MutationRegisterArgs = {
  params: RegisterUserArgs;
};


export type MutationRegisterCompanyArgs = {
  params: RegisterCompanyArgs;
};


export type MutationRegisterPatientArgs = {
  params: RegisterPatientArgs;
};


export type MutationResetPasswordArgs = {
  newPassword: Scalars['String'];
  token: Scalars['String'];
};


export type MutationSetHeadOfDepartmentArgs = {
  departmentId: Scalars['Int'];
  employeeId: Scalars['Int'];
};


export type MutationTransferItemsArgs = {
  args: Array<DispatchInput>;
};


export type MutationWriteOffItemsArgs = {
  args: Array<WriteOffInput>;
};

export type Patient = {
  __typename?: 'Patient';
  address: Address;
  alergies?: Maybe<Array<Scalars['String']>>;
  bills: Array<Bill>;
  bloodGroup?: Maybe<Scalars['String']>;
  companyId: Scalars['Float'];
  createdAt: Scalars['String'];
  dateOfBirth: Scalars['String'];
  deleted: Scalars['Boolean'];
  email?: Maybe<Scalars['String']>;
  fileNumber?: Maybe<Scalars['String']>;
  firstname: Scalars['String'];
  gender: Scalars['String'];
  id: Scalars['Float'];
  image?: Maybe<Scalars['String']>;
  insuranceCardNumber?: Maybe<Scalars['String']>;
  insuranceId?: Maybe<Scalars['String']>;
  insuranceProvider: Scalars['String'];
  insuranceSchemeId?: Maybe<Scalars['String']>;
  insuranceStatus: Scalars['String'];
  insuranceUserId: Scalars['String'];
  lastname: Scalars['String'];
  middlename: Scalars['String'];
  nationalId?: Maybe<Scalars['String']>;
  nextOfKinName: Scalars['String'];
  nextOfKinPhone: Scalars['String'];
  nextOfKinRelationship: Scalars['String'];
  otherId?: Maybe<Scalars['String']>;
  phone: Scalars['String'];
  registerer: Employee;
  registererId?: Maybe<Scalars['Float']>;
  religion: Scalars['String'];
  status: Scalars['String'];
  updatedAt: Scalars['String'];
  visits: Array<Visit>;
};

export type PatientResponse = {
  __typename?: 'PatientResponse';
  error?: Maybe<FieldError>;
  patient?: Maybe<Patient>;
};

export type Permission = {
  __typename?: 'Permission';
  createdAt: Scalars['String'];
  deleted: Scalars['Boolean'];
  id: Scalars['Float'];
  name: Scalars['String'];
  roles: Array<Role>;
  updatedAt: Scalars['String'];
  users: Array<User>;
};

export type Query = {
  __typename?: 'Query';
  getAllAddress: Array<Address>;
  getAllCategories: Array<Category>;
  getAllItems: Array<Item>;
  getAllServices: Array<Item>;
  getBatchStockForStore: Array<BatchStock>;
  getCategories: Array<Category>;
  getClinic?: Maybe<Clinic>;
  getClinics: Array<Clinic>;
  getCompanies: Array<Company>;
  getCompany?: Maybe<Company>;
  getDepartments: Array<Department>;
  getDispatches: Array<Inventory>;
  getEmployees: Array<User>;
  getExpense?: Maybe<Expense>;
  getExpenses: Array<Expense>;
  getFeatures: Array<Feature>;
  getInternalItems: Array<Item>;
  getInventoryTransfer?: Maybe<Inventory>;
  getInventoryTransfers: Array<Inventory>;
  getItem: Item;
  getItemBatchImports: Array<Import>;
  getItemBatchStocks: Array<BatchStock>;
  getItemStoreStocks: Array<StoreItemStock>;
  getItemTransfers: Array<Transfer>;
  getMerchandiseItems: Array<Item>;
  getPatient?: Maybe<Patient>;
  getPatients: Array<Patient>;
  getPermissions: Array<Permission>;
  getRole?: Maybe<Role>;
  getRoles: Array<Role>;
  getSales: Array<Inventory>;
  getSchedules: Array<Schedule>;
  getStoreItems: Array<Item>;
  getStores: Array<Store>;
  getTransfers: Array<Inventory>;
  getType?: Maybe<Type>;
  getTypes: Array<Type>;
  getUser?: Maybe<User>;
  getUsers: Array<User>;
  getVisits: Array<Visit>;
  getWriteOffsByCompany: Array<Transfer>;
  me?: Maybe<User>;
};


export type QueryGetBatchStockForStoreArgs = {
  itemId?: InputMaybe<Scalars['Float']>;
  storeId?: InputMaybe<Scalars['Float']>;
};


export type QueryGetCategoriesArgs = {
  type: Scalars['String'];
};


export type QueryGetClinicArgs = {
  id: Scalars['Int'];
};


export type QueryGetCompaniesArgs = {
  types?: InputMaybe<Array<Scalars['String']>>;
};


export type QueryGetCompanyArgs = {
  id: Scalars['Float'];
};


export type QueryGetExpenseArgs = {
  id: Scalars['Float'];
};


export type QueryGetInventoryTransferArgs = {
  id: Scalars['Float'];
};


export type QueryGetInventoryTransfersArgs = {
  type?: InputMaybe<Scalars['String']>;
};


export type QueryGetItemArgs = {
  id: Scalars['Float'];
};


export type QueryGetItemBatchImportsArgs = {
  itemId: Scalars['Float'];
};


export type QueryGetItemBatchStocksArgs = {
  itemId: Scalars['Float'];
};


export type QueryGetItemStoreStocksArgs = {
  itemId: Scalars['Float'];
};


export type QueryGetItemTransfersArgs = {
  itemId: Scalars['Float'];
  type?: InputMaybe<Scalars['String']>;
};


export type QueryGetPatientArgs = {
  id: Scalars['Float'];
};


export type QueryGetRoleArgs = {
  name: Scalars['String'];
};


export type QueryGetRolesArgs = {
  sys?: InputMaybe<Scalars['Boolean']>;
};


export type QueryGetSchedulesArgs = {
  owner: Scalars['String'];
  ownerId: Scalars['Int'];
};


export type QueryGetStoreItemsArgs = {
  storeId: Scalars['Float'];
};


export type QueryGetTypeArgs = {
  id: Scalars['Float'];
};


export type QueryGetUserArgs = {
  id: Scalars['Float'];
};


export type QueryGetUsersArgs = {
  roles?: InputMaybe<Array<Scalars['Float']>>;
};

export type RegisterCompanyAddressedArgs = {
  city: Scalars['String'];
  district: Scalars['String'];
  name: Scalars['String'];
  registrationNumber: Scalars['String'];
  street: Scalars['String'];
  tinNumber: Scalars['String'];
  type: Scalars['String'];
  ward: Scalars['String'];
};

export type RegisterCompanyArgs = {
  name: Scalars['String'];
  registrationNumber: Scalars['String'];
  tinNumber: Scalars['String'];
  type: Scalars['String'];
};

export type RegisterPatientArgs = {
  DOB: Scalars['String'];
  city: Scalars['String'];
  country: Scalars['String'];
  district: Scalars['String'];
  email: Scalars['String'];
  firstname: Scalars['String'];
  gender: Scalars['String'];
  insuranceProvider: Scalars['String'];
  insuranceStatus: Scalars['String'];
  insuranceUserId: Scalars['String'];
  lastname: Scalars['String'];
  middlename: Scalars['String'];
  nationalId: Scalars['String'];
  nextOfKinName: Scalars['String'];
  nextOfKinPhone: Scalars['String'];
  nextOfKinRelationship: Scalars['String'];
  phone: Scalars['String'];
  religion: Scalars['String'];
  status: Scalars['String'];
  street: Scalars['String'];
  ward: Scalars['String'];
};

export type RegisterUserArgs = {
  companyId: Scalars['Float'];
  companyRole?: InputMaybe<Scalars['Float']>;
  email: Scalars['String'];
  firstname: Scalars['String'];
  lastname: Scalars['String'];
  middlename: Scalars['String'];
  password: Scalars['String'];
  phone: Scalars['String'];
  role?: InputMaybe<Scalars['Float']>;
};

export type Role = {
  __typename?: 'Role';
  createdAt: Scalars['String'];
  deleted: Scalars['Boolean'];
  employees: Array<Employee>;
  id: Scalars['Float'];
  name: Scalars['String'];
  permissions: Array<Permission>;
  sys: Scalars['Boolean'];
  updatedAt: Scalars['String'];
  users: Array<User>;
};

export type RoleArgs = {
  name: Scalars['String'];
  permissions?: InputMaybe<Array<Scalars['Float']>>;
};

export type SaleInput = {
  batch?: InputMaybe<Scalars['String']>;
  itemId: Scalars['Float'];
  quantity: Scalars['Float'];
  remarks?: InputMaybe<Scalars['String']>;
  unit: Scalars['String'];
};

export type Schedule = {
  __typename?: 'Schedule';
  clinic?: Maybe<Clinic>;
  clinicId?: Maybe<Scalars['Float']>;
  createdAt: Scalars['String'];
  day: DayOfWeek;
  deleted: Scalars['Boolean'];
  description?: Maybe<Scalars['String']>;
  employee?: Maybe<Employee>;
  employeeId?: Maybe<Scalars['Float']>;
  id: Scalars['Float'];
  offTime: Scalars['String'];
  onTime: Scalars['String'];
  updatedAt: Scalars['String'];
};

export type ScheduleBulkArgs = {
  clinicId?: InputMaybe<Scalars['Float']>;
  employeeId?: InputMaybe<Scalars['Float']>;
  schedules: Array<ScheduleDetailsArgs>;
};

export type ScheduleDetailsArgs = {
  day: Scalars['String'];
  description?: InputMaybe<Scalars['String']>;
  offTime: Scalars['String'];
  onTime: Scalars['String'];
};

export type ScheduleEditBulkArgs = {
  schedules: Array<ScheduleEditDetailsArgs>;
};

export type ScheduleEditDetailsArgs = {
  description: Scalars['String'];
  offTime: Scalars['String'];
  onTime: Scalars['String'];
  scheduleId: Scalars['Float'];
};

export type ServiceInput = {
  description?: InputMaybe<Scalars['String']>;
  name: Scalars['String'];
  reference?: InputMaybe<Scalars['String']>;
  sellingPrice: Scalars['Float'];
};

export type Store = {
  __typename?: 'Store';
  address: Scalars['String'];
  companyId: Scalars['Float'];
  createdAt: Scalars['String'];
  deleted: Scalars['Boolean'];
  id: Scalars['Float'];
  name: Scalars['String'];
  primary: Scalars['Boolean'];
  stockIn: Array<Inventory>;
  stockOut: Array<Inventory>;
  storeItemStocks: Array<StoreItemStock>;
  storeKeepers: Array<Employee>;
  updatedAt: Scalars['String'];
};

export type StoreEditInput = {
  address?: InputMaybe<Scalars['String']>;
  companyId?: InputMaybe<Scalars['Float']>;
  id: Scalars['Float'];
  name?: InputMaybe<Scalars['String']>;
  primary?: InputMaybe<Scalars['Boolean']>;
};

export type StoreInput = {
  address: Scalars['String'];
  name: Scalars['String'];
  primary?: InputMaybe<Scalars['Boolean']>;
};

export type StoreItemStock = {
  __typename?: 'StoreItemStock';
  batchId: Scalars['Float'];
  batchStock: BatchStock;
  createdAt: Scalars['String'];
  deleted: Scalars['Boolean'];
  id: Scalars['Float'];
  item: Item;
  itemId: Scalars['Float'];
  pieceStock: Scalars['Float'];
  stock: Scalars['Float'];
  store: Store;
  storeId: Scalars['Float'];
  subPieceStock: Scalars['Float'];
  updatedAt: Scalars['String'];
};

export type Transfer = {
  __typename?: 'Transfer';
  batch: Scalars['String'];
  createdAt: Scalars['String'];
  deleted: Scalars['Boolean'];
  details?: Maybe<Scalars['String']>;
  id: Scalars['Float'];
  inventoryId: Scalars['Float'];
  inventoryTransfer: Inventory;
  item: Item;
  itemId: Scalars['Float'];
  pieceQuantity: Scalars['Float'];
  quantity: Scalars['Float'];
  subPieceQuantity: Scalars['Float'];
  updatedAt: Scalars['String'];
};

export type Type = {
  __typename?: 'Type';
  category: Array<Category>;
  createdAt: Scalars['String'];
  deleted: Scalars['Boolean'];
  description: Scalars['String'];
  id: Scalars['Float'];
  name: Scalars['String'];
  updatedAt: Scalars['String'];
};

export type TypeArgs = {
  description: Scalars['String'];
  name: Scalars['String'];
};

export type TypeEditArgs = {
  categories: Array<Scalars['Float']>;
  description: Scalars['String'];
  name: Scalars['String'];
};

export type User = {
  __typename?: 'User';
  address: Address;
  company: Company;
  companyId: Scalars['Float'];
  createdAt: Scalars['String'];
  dateOfBirth: Scalars['String'];
  deleted: Scalars['Boolean'];
  email?: Maybe<Scalars['String']>;
  employee?: Maybe<Employee>;
  firstname: Scalars['String'];
  gender: Scalars['String'];
  id: Scalars['Float'];
  lastname: Scalars['String'];
  middlename: Scalars['String'];
  permissions: Array<Permission>;
  phone: Scalars['String'];
  role: Role;
  roleId: Scalars['Float'];
  status: Array<Category>;
  updatedAt: Scalars['String'];
};

export type UserResponse = {
  __typename?: 'UserResponse';
  error?: Maybe<FieldError>;
  user?: Maybe<User>;
};

export type Visit = {
  __typename?: 'Visit';
  active: Scalars['Boolean'];
  bills: Array<Bill>;
  careTakerName?: Maybe<Scalars['String']>;
  careTakerPhone?: Maybe<Scalars['String']>;
  careTakerRelationship?: Maybe<Scalars['String']>;
  checkInStatus: Scalars['String'];
  checkInTime: Scalars['DateTime'];
  checkInType: Scalars['String'];
  checkOutStatus?: Maybe<Scalars['String']>;
  checkOutTime: Scalars['DateTime'];
  checkOutType?: Maybe<Scalars['String']>;
  client: Patient;
  clientId: Scalars['Float'];
  companyId: Scalars['Float'];
  confirmedDx?: Maybe<Scalars['String']>;
  consultation?: Maybe<Scalars['String']>;
  createdAt: Scalars['String'];
  currentLocation?: Maybe<Scalars['String']>;
  deleted: Scalars['Boolean'];
  folioId?: Maybe<Scalars['String']>;
  folioNumber?: Maybe<Scalars['String']>;
  id: Scalars['Float'];
  insuranceAuthNumber?: Maybe<Scalars['String']>;
  insuranceCardNumber?: Maybe<Scalars['String']>;
  insuranceId?: Maybe<Scalars['String']>;
  insuranceProvider?: Maybe<Scalars['String']>;
  insuranceSchemeId?: Maybe<Scalars['String']>;
  insuranceStatus?: Maybe<Scalars['String']>;
  insuranceUserId?: Maybe<Scalars['String']>;
  preliminaryDx?: Maybe<Scalars['String']>;
  productCode?: Maybe<Scalars['String']>;
  reason: Scalars['String'];
  referralType: Scalars['String'];
  status: Scalars['String'];
  supportingFile?: Maybe<Scalars['String']>;
  ticket: Scalars['String'];
  type: Scalars['String'];
  updatedAt: Scalars['String'];
  visitToClinics: Array<VisitToClinic>;
  vitals?: Maybe<Vitals>;
};

export type VisitInputArgs = {
  careTakerName: Scalars['String'];
  careTakerPhone: Scalars['String'];
  careTakerRelationship: Scalars['String'];
  checkInType: Scalars['String'];
  clientId: Scalars['Float'];
  clinicId: Scalars['Float'];
  doctorId?: InputMaybe<Scalars['Float']>;
  insuranceAuthNumber?: InputMaybe<Scalars['String']>;
  insuranceCardNumber?: InputMaybe<Scalars['String']>;
  insuranceId?: InputMaybe<Scalars['String']>;
  insuranceProvider?: InputMaybe<Scalars['String']>;
  insuranceSchemeId?: InputMaybe<Scalars['String']>;
  insuranceStatus?: InputMaybe<Scalars['String']>;
  insuranceUserId?: InputMaybe<Scalars['String']>;
  reason: Scalars['String'];
  referralType: Scalars['String'];
  status: Scalars['String'];
  ticket: Scalars['String'];
  type: Scalars['String'];
  uploadedFile?: InputMaybe<Scalars['String']>;
};

export type VisitResponse = {
  __typename?: 'VisitResponse';
  error?: Maybe<FieldError>;
  visit?: Maybe<Visit>;
};

export type VisitToClinic = {
  __typename?: 'VisitToClinic';
  attendingEmployee: Employee;
  checkInTime: Scalars['DateTime'];
  checkOutTime: Scalars['DateTime'];
  clinic: Clinic;
  clinicId: Scalars['Float'];
  createdAt: Scalars['String'];
  deleted: Scalars['Boolean'];
  employeeId?: Maybe<Scalars['Float']>;
  id: Scalars['Float'];
  updatedAt: Scalars['String'];
  visit: Visit;
  visitId: Scalars['Float'];
};

export type Vitals = {
  __typename?: 'Vitals';
  bloodGlucose: Scalars['String'];
  bodyTemperature: Scalars['String'];
  createdAt: Scalars['String'];
  deleted: Scalars['Boolean'];
  diastolicPressure: Scalars['String'];
  height: Scalars['String'];
  id: Scalars['Float'];
  oxygenSaturation: Scalars['String'];
  pulseRate: Scalars['String'];
  respirationRate: Scalars['String'];
  systolicPressure: Scalars['String'];
  updatedAt: Scalars['String'];
  visit: Visit;
  visitId: Scalars['Float'];
  weight: Scalars['String'];
};

export type VitalsInputArgs = {
  bloodGlucose: Scalars['Float'];
  bodyTemperature: Scalars['Float'];
  diastolicPressure: Scalars['Float'];
  height: Scalars['Float'];
  oxygenSaturation: Scalars['Float'];
  pulseRate: Scalars['Float'];
  respirationRate: Scalars['Float'];
  systolicPressure: Scalars['Float'];
  visitId: Scalars['Float'];
  weight: Scalars['Float'];
};

export type WriteOffInput = {
  batch?: InputMaybe<Scalars['String']>;
  itemId: Scalars['Float'];
  locationId: Scalars['Float'];
  quantity: Scalars['Float'];
  reason?: InputMaybe<Scalars['String']>;
  unit: Scalars['String'];
};

export type ErrorFragment = { __typename?: 'FieldError', target: string, message: string };

export type MeFragment = { __typename?: 'User', id: number, firstname: string, middlename: string, lastname: string, email?: string | null, phone: string, role: { __typename?: 'Role', id: number, name: string }, employee?: { __typename?: 'Employee', id: number } | null };

export type BooleanResponseFragment = { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null };

export type ClientFragment = { __typename?: 'Patient', id: number, firstname: string, lastname: string, middlename: string, phone: string, email?: string | null, status: string, dateOfBirth: string, nationalId?: string | null, religion: string, gender: string, fileNumber?: string | null, insuranceId?: string | null, insuranceUserId: string, insuranceStatus: string, insuranceProvider: string, insuranceSchemeId?: string | null, insuranceCardNumber?: string | null, nextOfKinName: string, nextOfKinPhone: string, nextOfKinRelationship: string, address: { __typename?: 'Address', country: string, city: string, street: string, ward: string, district: string } };

export type RegisterCompanyMutationVariables = Exact<{
  params: RegisterCompanyArgs;
}>;


export type RegisterCompanyMutation = { __typename?: 'Mutation', registerCompany: { __typename?: 'BooleanResponseId', id?: number | null, status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type AddCompanyWithAddressMutationVariables = Exact<{
  params: RegisterCompanyAddressedArgs;
}>;


export type AddCompanyWithAddressMutation = { __typename?: 'Mutation', addCompanyWithAddress: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type AddStoreMutationVariables = Exact<{
  args: StoreInput;
}>;


export type AddStoreMutation = { __typename?: 'Mutation', addStore: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type EditStoreMutationVariables = Exact<{
  args: StoreEditInput;
}>;


export type EditStoreMutation = { __typename?: 'Mutation', editStore: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type AddSchedulesMutationVariables = Exact<{
  args: ScheduleBulkArgs;
}>;


export type AddSchedulesMutation = { __typename?: 'Mutation', addSchedules: { __typename?: 'BulkScheduleResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null, schedules?: Array<{ __typename?: 'Schedule', id: number, day: DayOfWeek, onTime: string, offTime: string, description?: string | null, clinicId?: number | null, employeeId?: number | null }> | null } };

export type EditSchedulesMutationVariables = Exact<{
  args: ScheduleEditBulkArgs;
}>;


export type EditSchedulesMutation = { __typename?: 'Mutation', editSchedules: { __typename?: 'BulkScheduleResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null, schedules?: Array<{ __typename?: 'Schedule', id: number, day: DayOfWeek, onTime: string, offTime: string, description?: string | null, clinicId?: number | null, employeeId?: number | null }> | null } };

export type AddDepartmentMutationVariables = Exact<{
  params: DepartmentInputArgs;
}>;


export type AddDepartmentMutation = { __typename?: 'Mutation', addDepartment: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type EditDepartmentMutationVariables = Exact<{
  id: Scalars['Float'];
  params: DepartmentInputArgs;
}>;


export type EditDepartmentMutation = { __typename?: 'Mutation', editDepartment: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type AddClinicMutationVariables = Exact<{
  params: ClinicInputArgs;
}>;


export type AddClinicMutation = { __typename?: 'Mutation', addClinic: { __typename?: 'ClinicResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null, clinic?: { __typename?: 'Clinic', id: number, name: string, description: string, status: string, clinicType: string, size: number } | null } };

export type EditClinicMutationVariables = Exact<{
  id: Scalars['Int'];
  params: ClinicEditArgs;
}>;


export type EditClinicMutation = { __typename?: 'Mutation', editClinic: { __typename?: 'ClinicResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null, clinic?: { __typename?: 'Clinic', id: number, name: string, description: string, status: string, clinicType: string, size: number } | null } };

export type DeleteClinicMutationVariables = Exact<{
  id: Scalars['Int'];
}>;


export type DeleteClinicMutation = { __typename?: 'Mutation', deleteClinic: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type SetHeadOfDepartmentMutationVariables = Exact<{
  departmentId: Scalars['Int'];
  employeeId: Scalars['Int'];
}>;


export type SetHeadOfDepartmentMutation = { __typename?: 'Mutation', setHeadOfDepartment: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type ImportItemMutationVariables = Exact<{
  args: ImportInput;
}>;


export type ImportItemMutation = { __typename?: 'Mutation', importItem: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type AddItemMutationVariables = Exact<{
  args: ItemInput;
}>;


export type AddItemMutation = { __typename?: 'Mutation', addItem: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type AddServiceMutationVariables = Exact<{
  args: ServiceInput;
}>;


export type AddServiceMutation = { __typename?: 'Mutation', addService: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type WriteOffItemsMutationVariables = Exact<{
  args: Array<WriteOffInput> | WriteOffInput;
}>;


export type WriteOffItemsMutation = { __typename?: 'Mutation', writeOffItems: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type DispatchItemsMutationVariables = Exact<{
  args: Array<DispatchInput> | DispatchInput;
}>;


export type DispatchItemsMutation = { __typename?: 'Mutation', dispatchItems: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type QuickSaleMutationVariables = Exact<{
  args: Array<SaleInput> | SaleInput;
}>;


export type QuickSaleMutation = { __typename?: 'Mutation', quickSale: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type ClearBillMutationVariables = Exact<{
  saleId: Scalars['Float'];
}>;


export type ClearBillMutation = { __typename?: 'Mutation', clearBill: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type TransferItemsMutationVariables = Exact<{
  args: Array<DispatchInput> | DispatchInput;
}>;


export type TransferItemsMutation = { __typename?: 'Mutation', transferItems: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type AddItemsFromExcelMutationVariables = Exact<{
  args: BulkItemInput;
}>;


export type AddItemsFromExcelMutation = { __typename?: 'Mutation', addItemsFromExcel: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type EditItemMutationVariables = Exact<{
  args: ItemInput;
  id: Scalars['Float'];
}>;


export type EditItemMutation = { __typename?: 'Mutation', editItem: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type EditServiceMutationVariables = Exact<{
  args: ServiceInput;
  id: Scalars['Float'];
}>;


export type EditServiceMutation = { __typename?: 'Mutation', editService: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type DeleteItemMutationVariables = Exact<{
  id: Scalars['Float'];
}>;


export type DeleteItemMutation = { __typename?: 'Mutation', deleteItem: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type ChangeInventoryApprovalStatusMutationVariables = Exact<{
  inventoryId: Scalars['Float'];
}>;


export type ChangeInventoryApprovalStatusMutation = { __typename?: 'Mutation', changeInventoryApprovalStatus: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type ChangeInventoryDispatchedStatusMutationVariables = Exact<{
  inventoryId: Scalars['Float'];
}>;


export type ChangeInventoryDispatchedStatusMutation = { __typename?: 'Mutation', changeInventoryDispatchedStatus: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type ChangeInventorySoldStatusMutationVariables = Exact<{
  inventoryId: Scalars['Float'];
}>;


export type ChangeInventorySoldStatusMutation = { __typename?: 'Mutation', changeInventorySoldStatus: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type ChangeInventoryReceivedStatusMutationVariables = Exact<{
  inventoryId: Scalars['Float'];
}>;


export type ChangeInventoryReceivedStatusMutation = { __typename?: 'Mutation', changeInventoryReceivedStatus: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type RegisterPatientMutationVariables = Exact<{
  params: RegisterPatientArgs;
}>;


export type RegisterPatientMutation = { __typename?: 'Mutation', registerPatient: { __typename?: 'PatientResponse', error?: { __typename?: 'FieldError', target: string, message: string } | null, patient?: { __typename?: 'Patient', id: number, firstname: string, lastname: string, middlename: string, phone: string, email?: string | null, status: string, dateOfBirth: string, nationalId?: string | null, religion: string, gender: string, fileNumber?: string | null, insuranceId?: string | null, insuranceUserId: string, insuranceStatus: string, insuranceProvider: string, insuranceSchemeId?: string | null, insuranceCardNumber?: string | null, nextOfKinName: string, nextOfKinPhone: string, nextOfKinRelationship: string, address: { __typename?: 'Address', country: string, city: string, street: string, ward: string, district: string } } | null } };

export type EditPatientMutationVariables = Exact<{
  id: Scalars['Float'];
  params: RegisterPatientArgs;
}>;


export type EditPatientMutation = { __typename?: 'Mutation', editPatient: { __typename?: 'PatientResponse', error?: { __typename?: 'FieldError', target: string, message: string } | null, patient?: { __typename?: 'Patient', id: number, firstname: string, lastname: string, middlename: string, phone: string, email?: string | null, status: string, dateOfBirth: string, nationalId?: string | null, religion: string, gender: string, fileNumber?: string | null, insuranceId?: string | null, insuranceUserId: string, insuranceStatus: string, insuranceProvider: string, insuranceSchemeId?: string | null, insuranceCardNumber?: string | null, nextOfKinName: string, nextOfKinPhone: string, nextOfKinRelationship: string, address: { __typename?: 'Address', country: string, city: string, street: string, ward: string, district: string } } | null } };

export type AddVisitMutationVariables = Exact<{
  params: VisitInputArgs;
}>;


export type AddVisitMutation = { __typename?: 'Mutation', addVisit: { __typename?: 'VisitResponse', error?: { __typename?: 'FieldError', target: string, message: string } | null, visit?: { __typename?: 'Visit', id: number, status: string, type: string, reason: string, consultation?: string | null, currentLocation?: string | null, clientId: number, client: { __typename?: 'Patient', id: number, firstname: string, middlename: string, lastname: string, status: string, email?: string | null, phone: string, insuranceProvider: string, insuranceId?: string | null, insuranceUserId: string, insuranceStatus: string, insuranceSchemeId?: string | null, insuranceCardNumber?: string | null, gender: string, dateOfBirth: string, fileNumber?: string | null, nationalId?: string | null, bloodGroup?: string | null, registererId?: number | null } } | null } };

export type AddVitalsMutationVariables = Exact<{
  params: VitalsInputArgs;
}>;


export type AddVitalsMutation = { __typename?: 'Mutation', addVitals: { __typename?: 'VisitResponse', error?: { __typename?: 'FieldError', target: string, message: string } | null, visit?: { __typename?: 'Visit', id: number, status: string, type: string, reason: string, consultation?: string | null, currentLocation?: string | null, clientId: number, client: { __typename?: 'Patient', id: number, firstname: string, middlename: string, lastname: string, status: string, email?: string | null, phone: string, insuranceProvider: string, insuranceId?: string | null, insuranceUserId: string, insuranceStatus: string, insuranceSchemeId?: string | null, insuranceCardNumber?: string | null, gender: string, dateOfBirth: string, fileNumber?: string | null, nationalId?: string | null, bloodGroup?: string | null, registererId?: number | null }, vitals?: { __typename?: 'Vitals', id: number, height: string, weight: string, pulseRate: string, bodyTemperature: string, respirationRate: string, oxygenSaturation: string, systolicPressure: string, diastolicPressure: string, bloodGlucose: string } | null } | null } };

export type DeleteTypeMutationVariables = Exact<{
  id: Scalars['Float'];
}>;


export type DeleteTypeMutation = { __typename?: 'Mutation', deleteType: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type AddTypeMutationVariables = Exact<{
  args: TypeArgs;
}>;


export type AddTypeMutation = { __typename?: 'Mutation', addType: { __typename?: 'BooleanResponseWithType', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null, data?: { __typename?: 'Type', id: number, name: string, description: string } | null } };

export type EditTypeMutationVariables = Exact<{
  id: Scalars['Float'];
  args: TypeEditArgs;
}>;


export type EditTypeMutation = { __typename?: 'Mutation', editType: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type AddPermissionMutationVariables = Exact<{
  name: Scalars['String'];
  userId?: InputMaybe<Scalars['Float']>;
  roleId?: InputMaybe<Scalars['Float']>;
}>;


export type AddPermissionMutation = { __typename?: 'Mutation', addPermission: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type EditPermissionMutationVariables = Exact<{
  id: Scalars['Float'];
  name: Scalars['String'];
}>;


export type EditPermissionMutation = { __typename?: 'Mutation', editPermission: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type DeletePermissionMutationVariables = Exact<{
  id: Scalars['Float'];
}>;


export type DeletePermissionMutation = { __typename?: 'Mutation', deletePermission: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type AddFeatureMutationVariables = Exact<{
  name: Scalars['String'];
  companyId: Scalars['Float'];
}>;


export type AddFeatureMutation = { __typename?: 'Mutation', addFeature: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type EditFeatureMutationVariables = Exact<{
  id: Scalars['Float'];
  name: Scalars['String'];
}>;


export type EditFeatureMutation = { __typename?: 'Mutation', editFeature: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type DeleteFeatureMutationVariables = Exact<{
  id: Scalars['Float'];
}>;


export type DeleteFeatureMutation = { __typename?: 'Mutation', deleteFeature: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type AddRoleMutationVariables = Exact<{
  name: Scalars['String'];
}>;


export type AddRoleMutation = { __typename?: 'Mutation', addRole: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type EditRoleMutationVariables = Exact<{
  id: Scalars['Float'];
  args: RoleArgs;
}>;


export type EditRoleMutation = { __typename?: 'Mutation', editRole: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type DeleteRoleMutationVariables = Exact<{
  id: Scalars['Float'];
}>;


export type DeleteRoleMutation = { __typename?: 'Mutation', deleteRole: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type AddCategoryMutationVariables = Exact<{
  args: CategoryArgs;
}>;


export type AddCategoryMutation = { __typename?: 'Mutation', addCategory: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type EditCategoryMutationVariables = Exact<{
  id: Scalars['Float'];
  args: CategoryArgs;
}>;


export type EditCategoryMutation = { __typename?: 'Mutation', editCategory: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type EditCategoryByNameMutationVariables = Exact<{
  name: Scalars['String'];
  args: CategoryArgs;
}>;


export type EditCategoryByNameMutation = { __typename?: 'Mutation', editCategoryByName: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type DeleteCategoryMutationVariables = Exact<{
  id: Scalars['Float'];
}>;


export type DeleteCategoryMutation = { __typename?: 'Mutation', deleteCategory: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type AddAddressMutationVariables = Exact<{
  id: Scalars['Float'];
  target: Scalars['String'];
  params: AddressType;
}>;


export type AddAddressMutation = { __typename?: 'Mutation', addAddress: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type ForgotPasswordMutationVariables = Exact<{
  email: Scalars['String'];
}>;


export type ForgotPasswordMutation = { __typename?: 'Mutation', forgotPassword: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type LoginMutationVariables = Exact<{
  params: EmailPasswordArgs;
}>;


export type LoginMutation = { __typename?: 'Mutation', login: { __typename?: 'UserResponse', user?: { __typename?: 'User', id: number, firstname: string, middlename: string, lastname: string, email?: string | null, phone: string, role: { __typename?: 'Role', id: number, name: string }, employee?: { __typename?: 'Employee', id: number } | null } | null, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type LogoutMutationVariables = Exact<{ [key: string]: never; }>;


export type LogoutMutation = { __typename?: 'Mutation', logout: boolean };

export type ResetPasswordMutationVariables = Exact<{
  token: Scalars['String'];
  newPassword: Scalars['String'];
}>;


export type ResetPasswordMutation = { __typename?: 'Mutation', resetPassword: { __typename?: 'UserResponse', user?: { __typename?: 'User', id: number, firstname: string, middlename: string, lastname: string, email?: string | null, phone: string, role: { __typename?: 'Role', id: number, name: string }, employee?: { __typename?: 'Employee', id: number } | null } | null, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type RegisterMutationVariables = Exact<{
  params: RegisterUserArgs;
}>;


export type RegisterMutation = { __typename?: 'Mutation', register: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type EditUserMutationVariables = Exact<{
  id: Scalars['Float'];
  params: EditUserArgs;
}>;


export type EditUserMutation = { __typename?: 'Mutation', editUser: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type GetCompanyQueryVariables = Exact<{
  id: Scalars['Float'];
}>;


export type GetCompanyQuery = { __typename?: 'Query', getCompany?: { __typename?: 'Company', id: number, name: string, tinNumber: string, registrationNumber: string, type: string, employees: Array<{ __typename?: 'Employee', id: number, role: { __typename?: 'Role', name: string } }>, features: Array<{ __typename?: 'Feature', id: number, name: string }>, location: { __typename?: 'Address', id: number, city: string, district: string, ward: string, street: string, zip: string } } | null };

export type GetCompaniesQueryVariables = Exact<{
  types?: InputMaybe<Array<Scalars['String']> | Scalars['String']>;
}>;


export type GetCompaniesQuery = { __typename?: 'Query', getCompanies: Array<{ __typename?: 'Company', id: number, name: string, tinNumber: string, registrationNumber: string, type: string, location: { __typename?: 'Address', id: number, city: string, district: string, ward: string, street: string, zip: string } }> };

export type GetSchedulesQueryVariables = Exact<{
  ownerId: Scalars['Int'];
  owner: Scalars['String'];
}>;


export type GetSchedulesQuery = { __typename?: 'Query', getSchedules: Array<{ __typename?: 'Schedule', id: number, onTime: string, offTime: string, day: DayOfWeek, description?: string | null, clinicId?: number | null, employeeId?: number | null }> };

export type GetEmployeesQueryVariables = Exact<{ [key: string]: never; }>;


export type GetEmployeesQuery = { __typename?: 'Query', getEmployees: Array<{ __typename?: 'User', id: number, firstname: string, middlename: string, lastname: string, email?: string | null, phone: string, role: { __typename?: 'Role', id: number, name: string }, employee?: { __typename?: 'Employee', id: number } | null }> };

export type GetDepartmentsQueryVariables = Exact<{ [key: string]: never; }>;


export type GetDepartmentsQuery = { __typename?: 'Query', getDepartments: Array<{ __typename?: 'Department', id: number, name: string, type?: string | null, description: string, status?: string | null, parentId?: number | null, headOfDepartment?: { __typename?: 'Employee', id: number } | null, employees: Array<{ __typename?: 'Employee', id: number }>, clinics?: Array<{ __typename?: 'Clinic', id: number, name: string, clinicType: string, size: number, status: string, description: string }> | null }> };

export type GetClinicsQueryVariables = Exact<{ [key: string]: never; }>;


export type GetClinicsQuery = { __typename?: 'Query', getClinics: Array<{ __typename?: 'Clinic', id: number, name: string, description: string, status: string, clinicType: string, size: number, department: { __typename?: 'Department', id: number, name: string }, leader?: { __typename?: 'Employee', id: number } | null }> };

export type GetClinicQueryVariables = Exact<{
  id: Scalars['Int'];
}>;


export type GetClinicQuery = { __typename?: 'Query', getClinic?: { __typename?: 'Clinic', id: number, name: string, description: string, status: string, clinicType: string, size: number, department: { __typename?: 'Department', id: number, name: string }, leader?: { __typename?: 'Employee', id: number } | null } | null };

export type GetUsersQueryVariables = Exact<{
  roles?: InputMaybe<Array<Scalars['Float']> | Scalars['Float']>;
}>;


export type GetUsersQuery = { __typename?: 'Query', getUsers: Array<{ __typename?: 'User', id: number, firstname: string, middlename: string, lastname: string, email?: string | null, phone: string, role: { __typename?: 'Role', id: number, name: string }, employee?: { __typename?: 'Employee', id: number } | null }> };

export type GetUserQueryVariables = Exact<{
  id: Scalars['Float'];
}>;


export type GetUserQuery = { __typename?: 'Query', getUser?: { __typename?: 'User', id: number, firstname: string, middlename: string, lastname: string, email?: string | null, phone: string, permissions: Array<{ __typename?: 'Permission', id: number, name: string }>, role: { __typename?: 'Role', id: number, name: string }, employee?: { __typename?: 'Employee', id: number } | null } | null };

export type GetAllItemsQueryVariables = Exact<{ [key: string]: never; }>;


export type GetAllItemsQuery = { __typename?: 'Query', getAllItems: Array<{ __typename?: 'Item', id: number, name: string, type: string, description: string, sellingPrice: number, pieceSellingPrice: number, subPieceSellingPrice: number, reorder: number, reference: string, internal: boolean, unit: string, pieceUnit?: string | null, subPieceUnit?: string | null, stock: number, pieceStock: number, subPieceStock: number, pieces?: number | null, subPieces?: number | null }> };

export type GetAllServicesQueryVariables = Exact<{ [key: string]: never; }>;


export type GetAllServicesQuery = { __typename?: 'Query', getAllServices: Array<{ __typename?: 'Item', id: number, name: string, description: string, sellingPrice: number, reference: string }> };

export type GetStoreItemsQueryVariables = Exact<{
  storeId: Scalars['Float'];
}>;


export type GetStoreItemsQuery = { __typename?: 'Query', getStoreItems: Array<{ __typename?: 'Item', id: number, name: string, type: string, description: string, sellingPrice: number, reorder: number, reference: string, internal: boolean, unit: string, pieceUnit?: string | null, subPieceUnit?: string | null, pieces?: number | null, subPieces?: number | null, stock: number, pieceStock: number, subPieceStock: number }> };

export type GetInternalItemsQueryVariables = Exact<{ [key: string]: never; }>;


export type GetInternalItemsQuery = { __typename?: 'Query', getInternalItems: Array<{ __typename?: 'Item', id: number, name: string, type: string, description: string, reorder: number, reference: string, internal: boolean, unit: string, stock: number, pieceUnit?: string | null, pieceStock: number, subPieceUnit?: string | null, subPieceStock: number, pieces?: number | null, subPieces?: number | null }> };

export type GetMerchandiseItemsQueryVariables = Exact<{ [key: string]: never; }>;


export type GetMerchandiseItemsQuery = { __typename?: 'Query', getMerchandiseItems: Array<{ __typename?: 'Item', id: number, name: string, type: string, description: string, sellingPrice: number, pieceSellingPrice: number, subPieceSellingPrice: number, reorder: number, reference: string, internal: boolean, unit: string, stock: number, pieceUnit?: string | null, pieceStock: number, subPieceUnit?: string | null, subPieceStock: number, pieces?: number | null, subPieces?: number | null }> };

export type GetItemQueryVariables = Exact<{
  id: Scalars['Float'];
}>;


export type GetItemQuery = { __typename?: 'Query', getItem: { __typename?: 'Item', id: number, name: string, type: string, description: string, internal: boolean, reorder: number, reference: string, unit: string, stock: number, sellingPrice: number, pieceSellingPrice: number, subPieceSellingPrice: number, pieceStock: number, pieceUnit?: string | null, subPieceStock: number, subPieceUnit?: string | null, pieces?: number | null, subPieces?: number | null, imports: Array<{ __typename?: 'Import', id: number, importDate: string, supplier: string, quantity: number, importPrice: number, sellingPrice: number }>, inventoryTransfers: Array<{ __typename?: 'Inventory', id: number, type: string, details?: string | null, transferDate: string, sourceStoreId?: number | null, destinationStoreId?: number | null }>, transfers: Array<{ __typename?: 'Transfer', id: number, inventoryId: number, itemId: number, quantity: number, pieceQuantity: number, subPieceQuantity: number }> } };

export type GetSalesQueryVariables = Exact<{ [key: string]: never; }>;


export type GetSalesQuery = { __typename?: 'Query', getSales: Array<{ __typename?: 'Inventory', id: number, createdAt: string, details?: string | null, type: string, granted: boolean, dispatched: boolean, sourceStoreId?: number | null, keeper?: { __typename?: 'Employee', id: number, userId: number } | null, approver?: { __typename?: 'Employee', id: number, userId: number } | null, bill?: { __typename?: 'Bill', amount: number, cleared: boolean, paymentType: string } | null, transfers: Array<{ __typename?: 'Transfer', id: number, itemId: number, quantity: number, pieceQuantity: number, subPieceQuantity: number, details?: string | null, batch: string }>, items: Array<{ __typename?: 'Item', id: number, name: string, unit: string, pieceUnit?: string | null, subPieceUnit?: string | null, pieces?: number | null, subPieces?: number | null, sellingPrice: number, pieceSellingPrice: number, subPieceSellingPrice: number, type: string, reference: string, description: string }> }> };

export type GetStoresQueryVariables = Exact<{ [key: string]: never; }>;


export type GetStoresQuery = { __typename?: 'Query', getStores: Array<{ __typename?: 'Store', id: number, name: string, primary: boolean, address: string, storeKeepers: Array<{ __typename?: 'Employee', id: number }> }> };

export type GetInventoryTransfersQueryVariables = Exact<{
  type?: InputMaybe<Scalars['String']>;
}>;


export type GetInventoryTransfersQuery = { __typename?: 'Query', getInventoryTransfers: Array<{ __typename?: 'Inventory', id: number, details?: string | null, type: string, granted: boolean, received: boolean, transferDate: string, sourceStore?: { __typename?: 'Store', id: number, name: string } | null, destinationStore?: { __typename?: 'Store', id: number, name: string } | null }> };

export type GetInventoryTransferQueryVariables = Exact<{
  id: Scalars['Float'];
}>;


export type GetInventoryTransferQuery = { __typename?: 'Query', getInventoryTransfer?: { __typename?: 'Inventory', id: number, details?: string | null, type: string, granted: boolean, received: boolean, transferDate: string, sourceStore?: { __typename?: 'Store', id: number, name: string } | null, destinationStore?: { __typename?: 'Store', id: number, name: string } | null, keeper?: { __typename?: 'Employee', id: number, userId: number } | null, consumer?: { __typename?: 'Employee', id: number, userId: number } | null, transfers: Array<{ __typename?: 'Transfer', id: number, itemId: number, quantity: number, pieceQuantity: number, subPieceQuantity: number }>, items: Array<{ __typename?: 'Item', id: number, name: string, unit: string, pieceUnit?: string | null, subPieceUnit?: string | null, sellingPrice: number, pieceSellingPrice: number, subPieceSellingPrice: number, type: string, pieces?: number | null, subPieces?: number | null }> } | null };

export type GetItemTransfersQueryVariables = Exact<{
  itemId: Scalars['Float'];
  type?: InputMaybe<Scalars['String']>;
}>;


export type GetItemTransfersQuery = { __typename?: 'Query', getItemTransfers: Array<{ __typename?: 'Transfer', id: number, details?: string | null, quantity: number, pieceQuantity: number, subPieceQuantity: number, batch: string, inventoryTransfer: { __typename?: 'Inventory', id: number, updatedAt: string, details?: string | null, type: string, transferDate: string, sourceStoreId?: number | null, destinationStoreId?: number | null }, item: { __typename?: 'Item', id: number, name: string, unit: string, pieceUnit?: string | null, subPieceUnit?: string | null } }> };

export type GetWriteOffsByCompanyQueryVariables = Exact<{ [key: string]: never; }>;


export type GetWriteOffsByCompanyQuery = { __typename?: 'Query', getWriteOffsByCompany: Array<{ __typename?: 'Transfer', id: number, createdAt: string, quantity: number, pieceQuantity: number, subPieceQuantity: number, details?: string | null, item: { __typename?: 'Item', id: number, name: string, unit: string, pieceUnit?: string | null, subPieceUnit?: string | null } }> };

export type GetDispatchesQueryVariables = Exact<{ [key: string]: never; }>;


export type GetDispatchesQuery = { __typename?: 'Query', getDispatches: Array<{ __typename?: 'Inventory', id: number, updatedAt: string, transferDate: string, granted: boolean, dispatched: boolean, received: boolean, keeperId?: number | null, consumerId?: number | null, sourceStore?: { __typename?: 'Store', id: number, name: string } | null, destinationStore?: { __typename?: 'Store', id: number, name: string } | null, items: Array<{ __typename?: 'Item', id: number, name: string, unit: string, pieceUnit?: string | null, subPieceUnit?: string | null }>, transfers: Array<{ __typename?: 'Transfer', id: number, itemId: number, quantity: number, pieceQuantity: number, subPieceQuantity: number, batch: string }> }> };

export type GetTransfersQueryVariables = Exact<{ [key: string]: never; }>;


export type GetTransfersQuery = { __typename?: 'Query', getTransfers: Array<{ __typename?: 'Inventory', id: number, updatedAt: string, transferDate: string, granted: boolean, dispatched: boolean, received: boolean, keeperId?: number | null, consumerId?: number | null, sourceStore?: { __typename?: 'Store', id: number, name: string } | null, destinationStore?: { __typename?: 'Store', id: number, name: string } | null, items: Array<{ __typename?: 'Item', id: number, name: string, unit: string, pieceUnit?: string | null, subPieceUnit?: string | null }>, transfers: Array<{ __typename?: 'Transfer', id: number, itemId: number, quantity: number, pieceQuantity: number, subPieceQuantity: number, batch: string }> }> };

export type GetItemBatchStocksQueryVariables = Exact<{
  itemId: Scalars['Float'];
}>;


export type GetItemBatchStocksQuery = { __typename?: 'Query', getItemBatchStocks: Array<{ __typename?: 'BatchStock', id: number, batch: string, expireDate: string, stock: number, pieceStock: number, subPieceStock: number, storeItemStocks: Array<{ __typename?: 'StoreItemStock', id: number, storeId: number, stock: number, pieceStock: number, subPieceStock: number }> }> };

export type GetBatchStockForStoreQueryVariables = Exact<{
  itemId?: InputMaybe<Scalars['Float']>;
  storeId?: InputMaybe<Scalars['Float']>;
}>;


export type GetBatchStockForStoreQuery = { __typename?: 'Query', getBatchStockForStore: Array<{ __typename?: 'BatchStock', itemId: number, batch: string, expireDate: string, stock: number, pieceStock: number, subPieceStock: number, storeItemStocks: Array<{ __typename?: 'StoreItemStock', storeId: number, stock: number, pieceStock: number, subPieceStock: number }> }> };

export type GetItemStoreStocksQueryVariables = Exact<{
  itemId: Scalars['Float'];
}>;


export type GetItemStoreStocksQuery = { __typename?: 'Query', getItemStoreStocks: Array<{ __typename?: 'StoreItemStock', id: number, storeId: number, batchId: number, stock: number, pieceStock: number, subPieceStock: number, store: { __typename?: 'Store', id: number, name: string } }> };

export type GetItemBatchImportsQueryVariables = Exact<{
  itemId: Scalars['Float'];
}>;


export type GetItemBatchImportsQuery = { __typename?: 'Query', getItemBatchImports: Array<{ __typename?: 'Import', id: number, importPrice: number, batch: string }> };

export type MeQueryVariables = Exact<{ [key: string]: never; }>;


export type MeQuery = { __typename?: 'Query', me?: { __typename?: 'User', id: number, firstname: string, middlename: string, lastname: string, email?: string | null, phone: string, role: { __typename?: 'Role', id: number, name: string }, employee?: { __typename?: 'Employee', id: number } | null } | null };

export type GetPatientsQueryVariables = Exact<{ [key: string]: never; }>;


export type GetPatientsQuery = { __typename?: 'Query', getPatients: Array<{ __typename?: 'Patient', id: number, firstname: string, lastname: string, middlename: string, phone: string, email?: string | null, status: string, dateOfBirth: string, nationalId?: string | null, religion: string, gender: string, fileNumber?: string | null, insuranceId?: string | null, insuranceUserId: string, insuranceStatus: string, insuranceProvider: string, insuranceSchemeId?: string | null, insuranceCardNumber?: string | null, nextOfKinName: string, nextOfKinPhone: string, nextOfKinRelationship: string, address: { __typename?: 'Address', country: string, city: string, street: string, ward: string, district: string } }> };

export type GetPatientQueryVariables = Exact<{
  id: Scalars['Float'];
}>;


export type GetPatientQuery = { __typename?: 'Query', getPatient?: { __typename?: 'Patient', id: number, firstname: string, lastname: string, middlename: string, phone: string, email?: string | null, status: string, dateOfBirth: string, nationalId?: string | null, religion: string, gender: string, fileNumber?: string | null, insuranceId?: string | null, insuranceUserId: string, insuranceStatus: string, insuranceProvider: string, insuranceSchemeId?: string | null, insuranceCardNumber?: string | null, nextOfKinName: string, nextOfKinPhone: string, nextOfKinRelationship: string, address: { __typename?: 'Address', country: string, city: string, street: string, ward: string, district: string } } | null };

export type GetVisitsQueryVariables = Exact<{ [key: string]: never; }>;


export type GetVisitsQuery = { __typename?: 'Query', getVisits: Array<{ __typename?: 'Visit', id: number, status: string, type: string, reason: string, consultation?: string | null, currentLocation?: string | null, clientId: number, client: { __typename?: 'Patient', id: number, firstname: string, middlename: string, lastname: string, status: string, email?: string | null, phone: string, insuranceProvider: string, insuranceId?: string | null, insuranceUserId: string, insuranceStatus: string, insuranceSchemeId?: string | null, insuranceCardNumber?: string | null, gender: string, dateOfBirth: string, fileNumber?: string | null, nationalId?: string | null, bloodGroup?: string | null, registererId?: number | null }, bills: Array<{ __typename?: 'Bill', id: number, cleared: boolean, amount: number, paymentType: string }>, visitToClinics: Array<{ __typename?: 'VisitToClinic', id: number, clinicId: number }> }> };

export type GetPermissionsQueryVariables = Exact<{ [key: string]: never; }>;


export type GetPermissionsQuery = { __typename?: 'Query', getPermissions: Array<{ __typename?: 'Permission', id: number, name: string, roles: Array<{ __typename?: 'Role', id: number, name: string }> }> };

export type GetFeaturesQueryVariables = Exact<{ [key: string]: never; }>;


export type GetFeaturesQuery = { __typename?: 'Query', getFeatures: Array<{ __typename?: 'Feature', id: number, name: string, companies: Array<{ __typename?: 'Company', id: number, name: string, type: string }> }> };

export type GetTypesQueryVariables = Exact<{ [key: string]: never; }>;


export type GetTypesQuery = { __typename?: 'Query', getTypes: Array<{ __typename?: 'Type', id: number, name: string, description: string, category: Array<{ __typename?: 'Category', id: number, name: string }> }> };

export type GetTypeQueryVariables = Exact<{
  id: Scalars['Float'];
}>;


export type GetTypeQuery = { __typename?: 'Query', getType?: { __typename?: 'Type', id: number, name: string, description: string, category: Array<{ __typename?: 'Category', id: number, name: string }> } | null };

export type GetRolesQueryVariables = Exact<{ [key: string]: never; }>;


export type GetRolesQuery = { __typename?: 'Query', getRoles: Array<{ __typename?: 'Role', id: number, sys: boolean, name: string }> };

export type GetRoleQueryVariables = Exact<{
  name: Scalars['String'];
}>;


export type GetRoleQuery = { __typename?: 'Query', getRole?: { __typename?: 'Role', id: number, name: string, permissions: Array<{ __typename?: 'Permission', id: number, name: string }> } | null };

export type GetAllCategoriesQueryVariables = Exact<{ [key: string]: never; }>;


export type GetAllCategoriesQuery = { __typename?: 'Query', getAllCategories: Array<{ __typename?: 'Category', id: number, name: string }> };

export type GetCategoriesQueryVariables = Exact<{
  type: Scalars['String'];
}>;


export type GetCategoriesQuery = { __typename?: 'Query', getCategories: Array<{ __typename?: 'Category', id: number, name: string }> };

export type GetAllAddressQueryVariables = Exact<{ [key: string]: never; }>;


export type GetAllAddressQuery = { __typename?: 'Query', getAllAddress: Array<{ __typename?: 'Address', id: number, country: string, city: string, district: string, ward: string, street: string, zip: string }> };

export const ErrorFragmentDoc = gql`
    fragment Error on FieldError {
  target
  message
}
    `;
export const MeFragmentDoc = gql`
    fragment Me on User {
  id
  firstname
  middlename
  lastname
  email
  phone
  role {
    id
    name
  }
  employee {
    id
  }
}
    `;
export const BooleanResponseFragmentDoc = gql`
    fragment BooleanResponse on BooleanResponse {
  status
  error {
    target
    message
  }
}
    `;
export const ClientFragmentDoc = gql`
    fragment Client on Patient {
  id
  firstname
  lastname
  middlename
  phone
  email
  status
  dateOfBirth
  nationalId
  address {
    country
    city
    street
    ward
    district
  }
  religion
  gender
  fileNumber
  insuranceId
  insuranceUserId
  insuranceStatus
  insuranceProvider
  insuranceSchemeId
  insuranceCardNumber
  nextOfKinName
  nextOfKinPhone
  nextOfKinRelationship
}
    `;
export const RegisterCompanyDocument = gql`
    mutation registerCompany($params: RegisterCompanyArgs!) {
  registerCompany(params: $params) {
    id
    status
    error {
      target
      message
    }
  }
}
    `;

export function useRegisterCompanyMutation() {
  return Urql.useMutation<RegisterCompanyMutation, RegisterCompanyMutationVariables>(RegisterCompanyDocument);
};
export const AddCompanyWithAddressDocument = gql`
    mutation addCompanyWithAddress($params: RegisterCompanyAddressedArgs!) {
  addCompanyWithAddress(params: $params) {
    status
    error {
      target
      message
    }
  }
}
    `;

export function useAddCompanyWithAddressMutation() {
  return Urql.useMutation<AddCompanyWithAddressMutation, AddCompanyWithAddressMutationVariables>(AddCompanyWithAddressDocument);
};
export const AddStoreDocument = gql`
    mutation addStore($args: StoreInput!) {
  addStore(args: $args) {
    status
    error {
      target
      message
    }
  }
}
    `;

export function useAddStoreMutation() {
  return Urql.useMutation<AddStoreMutation, AddStoreMutationVariables>(AddStoreDocument);
};
export const EditStoreDocument = gql`
    mutation editStore($args: StoreEditInput!) {
  editStore(args: $args) {
    status
    error {
      target
      message
    }
  }
}
    `;

export function useEditStoreMutation() {
  return Urql.useMutation<EditStoreMutation, EditStoreMutationVariables>(EditStoreDocument);
};
export const AddSchedulesDocument = gql`
    mutation addSchedules($args: ScheduleBulkArgs!) {
  addSchedules(args: $args) {
    status
    error {
      target
      message
    }
    schedules {
      id
      day
      onTime
      offTime
      description
      clinicId
      employeeId
    }
  }
}
    `;

export function useAddSchedulesMutation() {
  return Urql.useMutation<AddSchedulesMutation, AddSchedulesMutationVariables>(AddSchedulesDocument);
};
export const EditSchedulesDocument = gql`
    mutation editSchedules($args: ScheduleEditBulkArgs!) {
  editSchedules(args: $args) {
    status
    error {
      target
      message
    }
    schedules {
      id
      day
      onTime
      offTime
      description
      clinicId
      employeeId
    }
  }
}
    `;

export function useEditSchedulesMutation() {
  return Urql.useMutation<EditSchedulesMutation, EditSchedulesMutationVariables>(EditSchedulesDocument);
};
export const AddDepartmentDocument = gql`
    mutation addDepartment($params: DepartmentInputArgs!) {
  addDepartment(params: $params) {
    error {
      target
      message
    }
    status
  }
}
    `;

export function useAddDepartmentMutation() {
  return Urql.useMutation<AddDepartmentMutation, AddDepartmentMutationVariables>(AddDepartmentDocument);
};
export const EditDepartmentDocument = gql`
    mutation editDepartment($id: Float!, $params: DepartmentInputArgs!) {
  editDepartment(id: $id, params: $params) {
    error {
      target
      message
    }
    status
  }
}
    `;

export function useEditDepartmentMutation() {
  return Urql.useMutation<EditDepartmentMutation, EditDepartmentMutationVariables>(EditDepartmentDocument);
};
export const AddClinicDocument = gql`
    mutation addClinic($params: ClinicInputArgs!) {
  addClinic(params: $params) {
    error {
      target
      message
    }
    status
    clinic {
      id
      name
      description
      status
      clinicType
      size
    }
  }
}
    `;

export function useAddClinicMutation() {
  return Urql.useMutation<AddClinicMutation, AddClinicMutationVariables>(AddClinicDocument);
};
export const EditClinicDocument = gql`
    mutation editClinic($id: Int!, $params: ClinicEditArgs!) {
  editClinic(id: $id, params: $params) {
    error {
      target
      message
    }
    status
    clinic {
      id
      name
      description
      status
      clinicType
      size
    }
  }
}
    `;

export function useEditClinicMutation() {
  return Urql.useMutation<EditClinicMutation, EditClinicMutationVariables>(EditClinicDocument);
};
export const DeleteClinicDocument = gql`
    mutation deleteClinic($id: Int!) {
  deleteClinic(id: $id) {
    error {
      target
      message
    }
    status
  }
}
    `;

export function useDeleteClinicMutation() {
  return Urql.useMutation<DeleteClinicMutation, DeleteClinicMutationVariables>(DeleteClinicDocument);
};
export const SetHeadOfDepartmentDocument = gql`
    mutation SetHeadOfDepartment($departmentId: Int!, $employeeId: Int!) {
  setHeadOfDepartment(departmentId: $departmentId, employeeId: $employeeId) {
    status
    error {
      target
      message
    }
  }
}
    `;

export function useSetHeadOfDepartmentMutation() {
  return Urql.useMutation<SetHeadOfDepartmentMutation, SetHeadOfDepartmentMutationVariables>(SetHeadOfDepartmentDocument);
};
export const ImportItemDocument = gql`
    mutation importItem($args: ImportInput!) {
  importItem(args: $args) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;

export function useImportItemMutation() {
  return Urql.useMutation<ImportItemMutation, ImportItemMutationVariables>(ImportItemDocument);
};
export const AddItemDocument = gql`
    mutation addItem($args: ItemInput!) {
  addItem(args: $args) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;

export function useAddItemMutation() {
  return Urql.useMutation<AddItemMutation, AddItemMutationVariables>(AddItemDocument);
};
export const AddServiceDocument = gql`
    mutation addService($args: ServiceInput!) {
  addService(args: $args) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;

export function useAddServiceMutation() {
  return Urql.useMutation<AddServiceMutation, AddServiceMutationVariables>(AddServiceDocument);
};
export const WriteOffItemsDocument = gql`
    mutation writeOffItems($args: [WriteOffInput!]!) {
  writeOffItems(args: $args) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;

export function useWriteOffItemsMutation() {
  return Urql.useMutation<WriteOffItemsMutation, WriteOffItemsMutationVariables>(WriteOffItemsDocument);
};
export const DispatchItemsDocument = gql`
    mutation dispatchItems($args: [DispatchInput!]!) {
  dispatchItems(args: $args) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;

export function useDispatchItemsMutation() {
  return Urql.useMutation<DispatchItemsMutation, DispatchItemsMutationVariables>(DispatchItemsDocument);
};
export const QuickSaleDocument = gql`
    mutation quickSale($args: [SaleInput!]!) {
  quickSale(args: $args) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;

export function useQuickSaleMutation() {
  return Urql.useMutation<QuickSaleMutation, QuickSaleMutationVariables>(QuickSaleDocument);
};
export const ClearBillDocument = gql`
    mutation clearBill($saleId: Float!) {
  clearBill(saleId: $saleId) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;

export function useClearBillMutation() {
  return Urql.useMutation<ClearBillMutation, ClearBillMutationVariables>(ClearBillDocument);
};
export const TransferItemsDocument = gql`
    mutation transferItems($args: [DispatchInput!]!) {
  transferItems(args: $args) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;

export function useTransferItemsMutation() {
  return Urql.useMutation<TransferItemsMutation, TransferItemsMutationVariables>(TransferItemsDocument);
};
export const AddItemsFromExcelDocument = gql`
    mutation addItemsFromExcel($args: BulkItemInput!) {
  addItemsFromExcel(args: $args) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;

export function useAddItemsFromExcelMutation() {
  return Urql.useMutation<AddItemsFromExcelMutation, AddItemsFromExcelMutationVariables>(AddItemsFromExcelDocument);
};
export const EditItemDocument = gql`
    mutation editItem($args: ItemInput!, $id: Float!) {
  editItem(id: $id, args: $args) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;

export function useEditItemMutation() {
  return Urql.useMutation<EditItemMutation, EditItemMutationVariables>(EditItemDocument);
};
export const EditServiceDocument = gql`
    mutation editService($args: ServiceInput!, $id: Float!) {
  editService(id: $id, args: $args) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;

export function useEditServiceMutation() {
  return Urql.useMutation<EditServiceMutation, EditServiceMutationVariables>(EditServiceDocument);
};
export const DeleteItemDocument = gql`
    mutation deleteItem($id: Float!) {
  deleteItem(id: $id) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;

export function useDeleteItemMutation() {
  return Urql.useMutation<DeleteItemMutation, DeleteItemMutationVariables>(DeleteItemDocument);
};
export const ChangeInventoryApprovalStatusDocument = gql`
    mutation changeInventoryApprovalStatus($inventoryId: Float!) {
  changeInventoryApprovalStatus(inventoryId: $inventoryId) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;

export function useChangeInventoryApprovalStatusMutation() {
  return Urql.useMutation<ChangeInventoryApprovalStatusMutation, ChangeInventoryApprovalStatusMutationVariables>(ChangeInventoryApprovalStatusDocument);
};
export const ChangeInventoryDispatchedStatusDocument = gql`
    mutation changeInventoryDispatchedStatus($inventoryId: Float!) {
  changeInventoryDispatchedStatus(inventoryId: $inventoryId) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;

export function useChangeInventoryDispatchedStatusMutation() {
  return Urql.useMutation<ChangeInventoryDispatchedStatusMutation, ChangeInventoryDispatchedStatusMutationVariables>(ChangeInventoryDispatchedStatusDocument);
};
export const ChangeInventorySoldStatusDocument = gql`
    mutation changeInventorySoldStatus($inventoryId: Float!) {
  changeInventorySoldStatus(inventoryId: $inventoryId) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;

export function useChangeInventorySoldStatusMutation() {
  return Urql.useMutation<ChangeInventorySoldStatusMutation, ChangeInventorySoldStatusMutationVariables>(ChangeInventorySoldStatusDocument);
};
export const ChangeInventoryReceivedStatusDocument = gql`
    mutation changeInventoryReceivedStatus($inventoryId: Float!) {
  changeInventoryReceivedStatus(inventoryId: $inventoryId) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;

export function useChangeInventoryReceivedStatusMutation() {
  return Urql.useMutation<ChangeInventoryReceivedStatusMutation, ChangeInventoryReceivedStatusMutationVariables>(ChangeInventoryReceivedStatusDocument);
};
export const RegisterPatientDocument = gql`
    mutation registerPatient($params: RegisterPatientArgs!) {
  registerPatient(params: $params) {
    error {
      target
      message
    }
    patient {
      ...Client
    }
  }
}
    ${ClientFragmentDoc}`;

export function useRegisterPatientMutation() {
  return Urql.useMutation<RegisterPatientMutation, RegisterPatientMutationVariables>(RegisterPatientDocument);
};
export const EditPatientDocument = gql`
    mutation editPatient($id: Float!, $params: RegisterPatientArgs!) {
  editPatient(id: $id, params: $params) {
    error {
      target
      message
    }
    patient {
      ...Client
    }
  }
}
    ${ClientFragmentDoc}`;

export function useEditPatientMutation() {
  return Urql.useMutation<EditPatientMutation, EditPatientMutationVariables>(EditPatientDocument);
};
export const AddVisitDocument = gql`
    mutation addVisit($params: VisitInputArgs!) {
  addVisit(params: $params) {
    error {
      target
      message
    }
    visit {
      id
      status
      type
      reason
      consultation
      currentLocation
      clientId
      client {
        id
        firstname
        middlename
        lastname
        status
        email
        phone
        insuranceProvider
        insuranceId
        insuranceUserId
        insuranceStatus
        insuranceSchemeId
        insuranceCardNumber
        gender
        dateOfBirth
        fileNumber
        nationalId
        bloodGroup
        registererId
      }
    }
  }
}
    `;

export function useAddVisitMutation() {
  return Urql.useMutation<AddVisitMutation, AddVisitMutationVariables>(AddVisitDocument);
};
export const AddVitalsDocument = gql`
    mutation addVitals($params: VitalsInputArgs!) {
  addVitals(params: $params) {
    error {
      target
      message
    }
    visit {
      id
      status
      type
      reason
      consultation
      currentLocation
      clientId
      client {
        id
        firstname
        middlename
        lastname
        status
        email
        phone
        insuranceProvider
        insuranceId
        insuranceUserId
        insuranceStatus
        insuranceSchemeId
        insuranceCardNumber
        gender
        dateOfBirth
        fileNumber
        nationalId
        bloodGroup
        registererId
      }
      vitals {
        id
        height
        weight
        pulseRate
        bodyTemperature
        respirationRate
        oxygenSaturation
        systolicPressure
        diastolicPressure
        bloodGlucose
      }
    }
  }
}
    `;

export function useAddVitalsMutation() {
  return Urql.useMutation<AddVitalsMutation, AddVitalsMutationVariables>(AddVitalsDocument);
};
export const DeleteTypeDocument = gql`
    mutation deleteType($id: Float!) {
  deleteType(id: $id) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;

export function useDeleteTypeMutation() {
  return Urql.useMutation<DeleteTypeMutation, DeleteTypeMutationVariables>(DeleteTypeDocument);
};
export const AddTypeDocument = gql`
    mutation addType($args: TypeArgs!) {
  addType(args: $args) {
    status
    error {
      target
      message
    }
    data {
      id
      name
      description
    }
  }
}
    `;

export function useAddTypeMutation() {
  return Urql.useMutation<AddTypeMutation, AddTypeMutationVariables>(AddTypeDocument);
};
export const EditTypeDocument = gql`
    mutation editType($id: Float!, $args: TypeEditArgs!) {
  editType(id: $id, args: $args) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;

export function useEditTypeMutation() {
  return Urql.useMutation<EditTypeMutation, EditTypeMutationVariables>(EditTypeDocument);
};
export const AddPermissionDocument = gql`
    mutation addPermission($name: String!, $userId: Float, $roleId: Float) {
  addPermission(name: $name, userId: $userId, roleId: $roleId) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;

export function useAddPermissionMutation() {
  return Urql.useMutation<AddPermissionMutation, AddPermissionMutationVariables>(AddPermissionDocument);
};
export const EditPermissionDocument = gql`
    mutation editPermission($id: Float!, $name: String!) {
  editPermission(id: $id, name: $name) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;

export function useEditPermissionMutation() {
  return Urql.useMutation<EditPermissionMutation, EditPermissionMutationVariables>(EditPermissionDocument);
};
export const DeletePermissionDocument = gql`
    mutation deletePermission($id: Float!) {
  deletePermission(id: $id) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;

export function useDeletePermissionMutation() {
  return Urql.useMutation<DeletePermissionMutation, DeletePermissionMutationVariables>(DeletePermissionDocument);
};
export const AddFeatureDocument = gql`
    mutation addFeature($name: String!, $companyId: Float!) {
  addFeature(name: $name, companyId: $companyId) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;

export function useAddFeatureMutation() {
  return Urql.useMutation<AddFeatureMutation, AddFeatureMutationVariables>(AddFeatureDocument);
};
export const EditFeatureDocument = gql`
    mutation editFeature($id: Float!, $name: String!) {
  editFeature(id: $id, name: $name) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;

export function useEditFeatureMutation() {
  return Urql.useMutation<EditFeatureMutation, EditFeatureMutationVariables>(EditFeatureDocument);
};
export const DeleteFeatureDocument = gql`
    mutation deleteFeature($id: Float!) {
  deleteFeature(id: $id) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;

export function useDeleteFeatureMutation() {
  return Urql.useMutation<DeleteFeatureMutation, DeleteFeatureMutationVariables>(DeleteFeatureDocument);
};
export const AddRoleDocument = gql`
    mutation addRole($name: String!) {
  addRole(name: $name) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;

export function useAddRoleMutation() {
  return Urql.useMutation<AddRoleMutation, AddRoleMutationVariables>(AddRoleDocument);
};
export const EditRoleDocument = gql`
    mutation editRole($id: Float!, $args: RoleArgs!) {
  editRole(id: $id, args: $args) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;

export function useEditRoleMutation() {
  return Urql.useMutation<EditRoleMutation, EditRoleMutationVariables>(EditRoleDocument);
};
export const DeleteRoleDocument = gql`
    mutation deleteRole($id: Float!) {
  deleteRole(id: $id) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;

export function useDeleteRoleMutation() {
  return Urql.useMutation<DeleteRoleMutation, DeleteRoleMutationVariables>(DeleteRoleDocument);
};
export const AddCategoryDocument = gql`
    mutation addCategory($args: CategoryArgs!) {
  addCategory(args: $args) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;

export function useAddCategoryMutation() {
  return Urql.useMutation<AddCategoryMutation, AddCategoryMutationVariables>(AddCategoryDocument);
};
export const EditCategoryDocument = gql`
    mutation editCategory($id: Float!, $args: CategoryArgs!) {
  editCategory(id: $id, args: $args) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;

export function useEditCategoryMutation() {
  return Urql.useMutation<EditCategoryMutation, EditCategoryMutationVariables>(EditCategoryDocument);
};
export const EditCategoryByNameDocument = gql`
    mutation editCategoryByName($name: String!, $args: CategoryArgs!) {
  editCategoryByName(name: $name, args: $args) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;

export function useEditCategoryByNameMutation() {
  return Urql.useMutation<EditCategoryByNameMutation, EditCategoryByNameMutationVariables>(EditCategoryByNameDocument);
};
export const DeleteCategoryDocument = gql`
    mutation deleteCategory($id: Float!) {
  deleteCategory(id: $id) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;

export function useDeleteCategoryMutation() {
  return Urql.useMutation<DeleteCategoryMutation, DeleteCategoryMutationVariables>(DeleteCategoryDocument);
};
export const AddAddressDocument = gql`
    mutation addAddress($id: Float!, $target: String!, $params: AddressType!) {
  addAddress(id: $id, target: $target, params: $params) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;

export function useAddAddressMutation() {
  return Urql.useMutation<AddAddressMutation, AddAddressMutationVariables>(AddAddressDocument);
};
export const ForgotPasswordDocument = gql`
    mutation forgotPassword($email: String!) {
  forgotPassword(email: $email) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;

export function useForgotPasswordMutation() {
  return Urql.useMutation<ForgotPasswordMutation, ForgotPasswordMutationVariables>(ForgotPasswordDocument);
};
export const LoginDocument = gql`
    mutation Login($params: EmailPasswordArgs!) {
  login(params: $params) {
    user {
      ...Me
    }
    error {
      ...Error
    }
  }
}
    ${MeFragmentDoc}
${ErrorFragmentDoc}`;

export function useLoginMutation() {
  return Urql.useMutation<LoginMutation, LoginMutationVariables>(LoginDocument);
};
export const LogoutDocument = gql`
    mutation Logout {
  logout
}
    `;

export function useLogoutMutation() {
  return Urql.useMutation<LogoutMutation, LogoutMutationVariables>(LogoutDocument);
};
export const ResetPasswordDocument = gql`
    mutation resetPassword($token: String!, $newPassword: String!) {
  resetPassword(token: $token, newPassword: $newPassword) {
    user {
      ...Me
    }
    error {
      ...Error
    }
  }
}
    ${MeFragmentDoc}
${ErrorFragmentDoc}`;

export function useResetPasswordMutation() {
  return Urql.useMutation<ResetPasswordMutation, ResetPasswordMutationVariables>(ResetPasswordDocument);
};
export const RegisterDocument = gql`
    mutation register($params: RegisterUserArgs!) {
  register(params: $params) {
    status
    error {
      ...Error
    }
  }
}
    ${ErrorFragmentDoc}`;

export function useRegisterMutation() {
  return Urql.useMutation<RegisterMutation, RegisterMutationVariables>(RegisterDocument);
};
export const EditUserDocument = gql`
    mutation editUser($id: Float!, $params: EditUserArgs!) {
  editUser(id: $id, params: $params) {
    status
    error {
      target
      message
    }
  }
}
    `;

export function useEditUserMutation() {
  return Urql.useMutation<EditUserMutation, EditUserMutationVariables>(EditUserDocument);
};
export const GetCompanyDocument = gql`
    query getCompany($id: Float!) {
  getCompany(id: $id) {
    id
    name
    tinNumber
    registrationNumber
    type
    employees {
      id
      role {
        name
      }
    }
    features {
      id
      name
    }
    location {
      id
      city
      district
      ward
      street
      zip
    }
  }
}
    `;

export function useGetCompanyQuery(options: Omit<Urql.UseQueryArgs<GetCompanyQueryVariables>, 'query'>) {
  return Urql.useQuery<GetCompanyQuery, GetCompanyQueryVariables>({ query: GetCompanyDocument, ...options });
};
export const GetCompaniesDocument = gql`
    query getCompanies($types: [String!]) {
  getCompanies(types: $types) {
    id
    name
    tinNumber
    registrationNumber
    type
    location {
      id
      city
      district
      ward
      street
      zip
    }
  }
}
    `;

export function useGetCompaniesQuery(options?: Omit<Urql.UseQueryArgs<GetCompaniesQueryVariables>, 'query'>) {
  return Urql.useQuery<GetCompaniesQuery, GetCompaniesQueryVariables>({ query: GetCompaniesDocument, ...options });
};
export const GetSchedulesDocument = gql`
    query getSchedules($ownerId: Int!, $owner: String!) {
  getSchedules(ownerId: $ownerId, owner: $owner) {
    id
    onTime
    offTime
    day
    description
    clinicId
    employeeId
  }
}
    `;

export function useGetSchedulesQuery(options: Omit<Urql.UseQueryArgs<GetSchedulesQueryVariables>, 'query'>) {
  return Urql.useQuery<GetSchedulesQuery, GetSchedulesQueryVariables>({ query: GetSchedulesDocument, ...options });
};
export const GetEmployeesDocument = gql`
    query getEmployees {
  getEmployees {
    ...Me
  }
}
    ${MeFragmentDoc}`;

export function useGetEmployeesQuery(options?: Omit<Urql.UseQueryArgs<GetEmployeesQueryVariables>, 'query'>) {
  return Urql.useQuery<GetEmployeesQuery, GetEmployeesQueryVariables>({ query: GetEmployeesDocument, ...options });
};
export const GetDepartmentsDocument = gql`
    query getDepartments {
  getDepartments {
    id
    name
    type
    description
    status
    parentId
    headOfDepartment {
      id
    }
    employees {
      id
    }
    clinics {
      id
      name
      clinicType
      size
      status
      description
    }
  }
}
    `;

export function useGetDepartmentsQuery(options?: Omit<Urql.UseQueryArgs<GetDepartmentsQueryVariables>, 'query'>) {
  return Urql.useQuery<GetDepartmentsQuery, GetDepartmentsQueryVariables>({ query: GetDepartmentsDocument, ...options });
};
export const GetClinicsDocument = gql`
    query getClinics {
  getClinics {
    id
    name
    description
    status
    clinicType
    size
    department {
      id
      name
    }
    leader {
      id
    }
  }
}
    `;

export function useGetClinicsQuery(options?: Omit<Urql.UseQueryArgs<GetClinicsQueryVariables>, 'query'>) {
  return Urql.useQuery<GetClinicsQuery, GetClinicsQueryVariables>({ query: GetClinicsDocument, ...options });
};
export const GetClinicDocument = gql`
    query getClinic($id: Int!) {
  getClinic(id: $id) {
    id
    name
    description
    status
    clinicType
    size
    department {
      id
      name
    }
    leader {
      id
    }
  }
}
    `;

export function useGetClinicQuery(options: Omit<Urql.UseQueryArgs<GetClinicQueryVariables>, 'query'>) {
  return Urql.useQuery<GetClinicQuery, GetClinicQueryVariables>({ query: GetClinicDocument, ...options });
};
export const GetUsersDocument = gql`
    query getUsers($roles: [Float!]) {
  getUsers(roles: $roles) {
    ...Me
  }
}
    ${MeFragmentDoc}`;

export function useGetUsersQuery(options?: Omit<Urql.UseQueryArgs<GetUsersQueryVariables>, 'query'>) {
  return Urql.useQuery<GetUsersQuery, GetUsersQueryVariables>({ query: GetUsersDocument, ...options });
};
export const GetUserDocument = gql`
    query getUser($id: Float!) {
  getUser(id: $id) {
    ...Me
    permissions {
      id
      name
    }
  }
}
    ${MeFragmentDoc}`;

export function useGetUserQuery(options: Omit<Urql.UseQueryArgs<GetUserQueryVariables>, 'query'>) {
  return Urql.useQuery<GetUserQuery, GetUserQueryVariables>({ query: GetUserDocument, ...options });
};
export const GetAllItemsDocument = gql`
    query getAllItems {
  getAllItems {
    id
    name
    type
    description
    sellingPrice
    pieceSellingPrice
    subPieceSellingPrice
    reorder
    reference
    internal
    unit
    pieceUnit
    subPieceUnit
    stock
    pieceStock
    subPieceStock
    pieces
    subPieces
  }
}
    `;

export function useGetAllItemsQuery(options?: Omit<Urql.UseQueryArgs<GetAllItemsQueryVariables>, 'query'>) {
  return Urql.useQuery<GetAllItemsQuery, GetAllItemsQueryVariables>({ query: GetAllItemsDocument, ...options });
};
export const GetAllServicesDocument = gql`
    query getAllServices {
  getAllServices {
    id
    name
    description
    sellingPrice
    reference
  }
}
    `;

export function useGetAllServicesQuery(options?: Omit<Urql.UseQueryArgs<GetAllServicesQueryVariables>, 'query'>) {
  return Urql.useQuery<GetAllServicesQuery, GetAllServicesQueryVariables>({ query: GetAllServicesDocument, ...options });
};
export const GetStoreItemsDocument = gql`
    query getStoreItems($storeId: Float!) {
  getStoreItems(storeId: $storeId) {
    id
    name
    type
    description
    sellingPrice
    reorder
    reference
    internal
    unit
    pieceUnit
    subPieceUnit
    pieces
    subPieces
    stock
    pieceStock
    subPieceStock
  }
}
    `;

export function useGetStoreItemsQuery(options: Omit<Urql.UseQueryArgs<GetStoreItemsQueryVariables>, 'query'>) {
  return Urql.useQuery<GetStoreItemsQuery, GetStoreItemsQueryVariables>({ query: GetStoreItemsDocument, ...options });
};
export const GetInternalItemsDocument = gql`
    query getInternalItems {
  getInternalItems {
    id
    name
    type
    description
    reorder
    reference
    internal
    unit
    stock
    pieceUnit
    pieceStock
    subPieceUnit
    subPieceStock
    pieces
    subPieces
  }
}
    `;

export function useGetInternalItemsQuery(options?: Omit<Urql.UseQueryArgs<GetInternalItemsQueryVariables>, 'query'>) {
  return Urql.useQuery<GetInternalItemsQuery, GetInternalItemsQueryVariables>({ query: GetInternalItemsDocument, ...options });
};
export const GetMerchandiseItemsDocument = gql`
    query getMerchandiseItems {
  getMerchandiseItems {
    id
    name
    type
    description
    sellingPrice
    pieceSellingPrice
    subPieceSellingPrice
    reorder
    reference
    internal
    unit
    stock
    pieceUnit
    pieceStock
    subPieceUnit
    subPieceStock
    pieces
    subPieces
  }
}
    `;

export function useGetMerchandiseItemsQuery(options?: Omit<Urql.UseQueryArgs<GetMerchandiseItemsQueryVariables>, 'query'>) {
  return Urql.useQuery<GetMerchandiseItemsQuery, GetMerchandiseItemsQueryVariables>({ query: GetMerchandiseItemsDocument, ...options });
};
export const GetItemDocument = gql`
    query getItem($id: Float!) {
  getItem(id: $id) {
    id
    name
    type
    description
    internal
    reorder
    reference
    internal
    unit
    stock
    sellingPrice
    pieceSellingPrice
    subPieceSellingPrice
    pieceStock
    pieceUnit
    subPieceStock
    subPieceUnit
    pieces
    subPieces
    imports {
      id
      importDate
      supplier
      quantity
      importPrice
      sellingPrice
    }
    inventoryTransfers {
      id
      type
      details
      transferDate
      sourceStoreId
      destinationStoreId
    }
    transfers {
      id
      inventoryId
      itemId
      quantity
      pieceQuantity
      subPieceQuantity
    }
  }
}
    `;

export function useGetItemQuery(options: Omit<Urql.UseQueryArgs<GetItemQueryVariables>, 'query'>) {
  return Urql.useQuery<GetItemQuery, GetItemQueryVariables>({ query: GetItemDocument, ...options });
};
export const GetSalesDocument = gql`
    query getSales {
  getSales {
    id
    createdAt
    details
    type
    granted
    dispatched
    sourceStoreId
    keeper {
      id
      userId
    }
    approver {
      id
      userId
    }
    bill {
      amount
      cleared
      paymentType
    }
    transfers {
      id
      itemId
      quantity
      pieceQuantity
      subPieceQuantity
      details
      batch
    }
    items {
      id
      name
      unit
      pieceUnit
      subPieceUnit
      pieces
      subPieces
      sellingPrice
      pieceSellingPrice
      subPieceSellingPrice
      type
      reference
      description
    }
  }
}
    `;

export function useGetSalesQuery(options?: Omit<Urql.UseQueryArgs<GetSalesQueryVariables>, 'query'>) {
  return Urql.useQuery<GetSalesQuery, GetSalesQueryVariables>({ query: GetSalesDocument, ...options });
};
export const GetStoresDocument = gql`
    query getStores {
  getStores {
    id
    name
    primary
    address
    storeKeepers {
      id
    }
  }
}
    `;

export function useGetStoresQuery(options?: Omit<Urql.UseQueryArgs<GetStoresQueryVariables>, 'query'>) {
  return Urql.useQuery<GetStoresQuery, GetStoresQueryVariables>({ query: GetStoresDocument, ...options });
};
export const GetInventoryTransfersDocument = gql`
    query getInventoryTransfers($type: String) {
  getInventoryTransfers(type: $type) {
    id
    details
    type
    granted
    received
    transferDate
    sourceStore {
      id
      name
    }
    destinationStore {
      id
      name
    }
  }
}
    `;

export function useGetInventoryTransfersQuery(options?: Omit<Urql.UseQueryArgs<GetInventoryTransfersQueryVariables>, 'query'>) {
  return Urql.useQuery<GetInventoryTransfersQuery, GetInventoryTransfersQueryVariables>({ query: GetInventoryTransfersDocument, ...options });
};
export const GetInventoryTransferDocument = gql`
    query getInventoryTransfer($id: Float!) {
  getInventoryTransfer(id: $id) {
    id
    details
    type
    granted
    received
    transferDate
    sourceStore {
      id
      name
    }
    destinationStore {
      id
      name
    }
    keeper {
      id
      userId
    }
    consumer {
      id
      userId
    }
    transfers {
      id
      itemId
      quantity
      pieceQuantity
      subPieceQuantity
    }
    items {
      id
      name
      unit
      pieceUnit
      subPieceUnit
      sellingPrice
      pieceSellingPrice
      subPieceSellingPrice
      type
      pieces
      subPieces
    }
  }
}
    `;

export function useGetInventoryTransferQuery(options: Omit<Urql.UseQueryArgs<GetInventoryTransferQueryVariables>, 'query'>) {
  return Urql.useQuery<GetInventoryTransferQuery, GetInventoryTransferQueryVariables>({ query: GetInventoryTransferDocument, ...options });
};
export const GetItemTransfersDocument = gql`
    query getItemTransfers($itemId: Float!, $type: String) {
  getItemTransfers(itemId: $itemId, type: $type) {
    id
    details
    quantity
    pieceQuantity
    subPieceQuantity
    batch
    inventoryTransfer {
      id
      updatedAt
      details
      type
      transferDate
      sourceStoreId
      destinationStoreId
    }
    item {
      id
      name
      unit
      pieceUnit
      subPieceUnit
    }
  }
}
    `;

export function useGetItemTransfersQuery(options: Omit<Urql.UseQueryArgs<GetItemTransfersQueryVariables>, 'query'>) {
  return Urql.useQuery<GetItemTransfersQuery, GetItemTransfersQueryVariables>({ query: GetItemTransfersDocument, ...options });
};
export const GetWriteOffsByCompanyDocument = gql`
    query GetWriteOffsByCompany {
  getWriteOffsByCompany {
    id
    createdAt
    quantity
    pieceQuantity
    subPieceQuantity
    details
    item {
      id
      name
      unit
      pieceUnit
      subPieceUnit
    }
  }
}
    `;

export function useGetWriteOffsByCompanyQuery(options?: Omit<Urql.UseQueryArgs<GetWriteOffsByCompanyQueryVariables>, 'query'>) {
  return Urql.useQuery<GetWriteOffsByCompanyQuery, GetWriteOffsByCompanyQueryVariables>({ query: GetWriteOffsByCompanyDocument, ...options });
};
export const GetDispatchesDocument = gql`
    query getDispatches {
  getDispatches {
    id
    updatedAt
    transferDate
    granted
    dispatched
    received
    sourceStore {
      id
      name
    }
    destinationStore {
      id
      name
    }
    items {
      id
      name
      unit
      pieceUnit
      subPieceUnit
    }
    keeperId
    consumerId
    transfers {
      id
      itemId
      quantity
      pieceQuantity
      subPieceQuantity
      batch
    }
  }
}
    `;

export function useGetDispatchesQuery(options?: Omit<Urql.UseQueryArgs<GetDispatchesQueryVariables>, 'query'>) {
  return Urql.useQuery<GetDispatchesQuery, GetDispatchesQueryVariables>({ query: GetDispatchesDocument, ...options });
};
export const GetTransfersDocument = gql`
    query getTransfers {
  getTransfers {
    id
    updatedAt
    transferDate
    granted
    dispatched
    received
    sourceStore {
      id
      name
    }
    destinationStore {
      id
      name
    }
    items {
      id
      name
      unit
      pieceUnit
      subPieceUnit
    }
    keeperId
    consumerId
    transfers {
      id
      itemId
      quantity
      pieceQuantity
      subPieceQuantity
      batch
    }
  }
}
    `;

export function useGetTransfersQuery(options?: Omit<Urql.UseQueryArgs<GetTransfersQueryVariables>, 'query'>) {
  return Urql.useQuery<GetTransfersQuery, GetTransfersQueryVariables>({ query: GetTransfersDocument, ...options });
};
export const GetItemBatchStocksDocument = gql`
    query getItemBatchStocks($itemId: Float!) {
  getItemBatchStocks(itemId: $itemId) {
    id
    batch
    expireDate
    stock
    pieceStock
    subPieceStock
    storeItemStocks {
      id
      storeId
      stock
      pieceStock
      subPieceStock
    }
  }
}
    `;

export function useGetItemBatchStocksQuery(options: Omit<Urql.UseQueryArgs<GetItemBatchStocksQueryVariables>, 'query'>) {
  return Urql.useQuery<GetItemBatchStocksQuery, GetItemBatchStocksQueryVariables>({ query: GetItemBatchStocksDocument, ...options });
};
export const GetBatchStockForStoreDocument = gql`
    query getBatchStockForStore($itemId: Float, $storeId: Float) {
  getBatchStockForStore(itemId: $itemId, storeId: $storeId) {
    itemId
    batch
    expireDate
    stock
    pieceStock
    subPieceStock
    storeItemStocks {
      storeId
      stock
      pieceStock
      subPieceStock
    }
  }
}
    `;

export function useGetBatchStockForStoreQuery(options?: Omit<Urql.UseQueryArgs<GetBatchStockForStoreQueryVariables>, 'query'>) {
  return Urql.useQuery<GetBatchStockForStoreQuery, GetBatchStockForStoreQueryVariables>({ query: GetBatchStockForStoreDocument, ...options });
};
export const GetItemStoreStocksDocument = gql`
    query getItemStoreStocks($itemId: Float!) {
  getItemStoreStocks(itemId: $itemId) {
    id
    storeId
    batchId
    stock
    pieceStock
    subPieceStock
    store {
      id
      name
    }
  }
}
    `;

export function useGetItemStoreStocksQuery(options: Omit<Urql.UseQueryArgs<GetItemStoreStocksQueryVariables>, 'query'>) {
  return Urql.useQuery<GetItemStoreStocksQuery, GetItemStoreStocksQueryVariables>({ query: GetItemStoreStocksDocument, ...options });
};
export const GetItemBatchImportsDocument = gql`
    query getItemBatchImports($itemId: Float!) {
  getItemBatchImports(itemId: $itemId) {
    id
    importPrice
    batch
  }
}
    `;

export function useGetItemBatchImportsQuery(options: Omit<Urql.UseQueryArgs<GetItemBatchImportsQueryVariables>, 'query'>) {
  return Urql.useQuery<GetItemBatchImportsQuery, GetItemBatchImportsQueryVariables>({ query: GetItemBatchImportsDocument, ...options });
};
export const MeDocument = gql`
    query Me {
  me {
    ...Me
  }
}
    ${MeFragmentDoc}`;

export function useMeQuery(options?: Omit<Urql.UseQueryArgs<MeQueryVariables>, 'query'>) {
  return Urql.useQuery<MeQuery, MeQueryVariables>({ query: MeDocument, ...options });
};
export const GetPatientsDocument = gql`
    query getPatients {
  getPatients {
    id
    firstname
    lastname
    middlename
    phone
    email
    status
    dateOfBirth
    nationalId
    address {
      country
      city
      street
      ward
      district
    }
    religion
    gender
    fileNumber
    insuranceId
    insuranceUserId
    insuranceStatus
    insuranceProvider
    insuranceSchemeId
    insuranceCardNumber
    nextOfKinName
    nextOfKinPhone
    nextOfKinRelationship
  }
}
    `;

export function useGetPatientsQuery(options?: Omit<Urql.UseQueryArgs<GetPatientsQueryVariables>, 'query'>) {
  return Urql.useQuery<GetPatientsQuery, GetPatientsQueryVariables>({ query: GetPatientsDocument, ...options });
};
export const GetPatientDocument = gql`
    query getPatient($id: Float!) {
  getPatient(id: $id) {
    id
    firstname
    lastname
    middlename
    phone
    email
    status
    dateOfBirth
    nationalId
    address {
      country
      city
      street
      ward
      district
    }
    religion
    gender
    fileNumber
    insuranceId
    insuranceUserId
    insuranceStatus
    insuranceProvider
    insuranceSchemeId
    insuranceCardNumber
    nextOfKinName
    nextOfKinPhone
    nextOfKinRelationship
  }
}
    `;

export function useGetPatientQuery(options: Omit<Urql.UseQueryArgs<GetPatientQueryVariables>, 'query'>) {
  return Urql.useQuery<GetPatientQuery, GetPatientQueryVariables>({ query: GetPatientDocument, ...options });
};
export const GetVisitsDocument = gql`
    query getVisits {
  getVisits {
    id
    status
    type
    reason
    consultation
    currentLocation
    clientId
    client {
      id
      firstname
      middlename
      lastname
      status
      email
      phone
      insuranceProvider
      insuranceId
      insuranceUserId
      insuranceStatus
      insuranceSchemeId
      insuranceCardNumber
      gender
      dateOfBirth
      fileNumber
      nationalId
      bloodGroup
      registererId
    }
    bills {
      id
      cleared
      amount
      paymentType
    }
    visitToClinics {
      id
      clinicId
    }
  }
}
    `;

export function useGetVisitsQuery(options?: Omit<Urql.UseQueryArgs<GetVisitsQueryVariables>, 'query'>) {
  return Urql.useQuery<GetVisitsQuery, GetVisitsQueryVariables>({ query: GetVisitsDocument, ...options });
};
export const GetPermissionsDocument = gql`
    query getPermissions {
  getPermissions {
    id
    name
    roles {
      id
      name
    }
  }
}
    `;

export function useGetPermissionsQuery(options?: Omit<Urql.UseQueryArgs<GetPermissionsQueryVariables>, 'query'>) {
  return Urql.useQuery<GetPermissionsQuery, GetPermissionsQueryVariables>({ query: GetPermissionsDocument, ...options });
};
export const GetFeaturesDocument = gql`
    query getFeatures {
  getFeatures {
    id
    name
    companies {
      id
      name
      type
    }
  }
}
    `;

export function useGetFeaturesQuery(options?: Omit<Urql.UseQueryArgs<GetFeaturesQueryVariables>, 'query'>) {
  return Urql.useQuery<GetFeaturesQuery, GetFeaturesQueryVariables>({ query: GetFeaturesDocument, ...options });
};
export const GetTypesDocument = gql`
    query getTypes {
  getTypes {
    id
    name
    description
    category {
      id
      name
    }
  }
}
    `;

export function useGetTypesQuery(options?: Omit<Urql.UseQueryArgs<GetTypesQueryVariables>, 'query'>) {
  return Urql.useQuery<GetTypesQuery, GetTypesQueryVariables>({ query: GetTypesDocument, ...options });
};
export const GetTypeDocument = gql`
    query getType($id: Float!) {
  getType(id: $id) {
    id
    name
    description
    category {
      id
      name
    }
  }
}
    `;

export function useGetTypeQuery(options: Omit<Urql.UseQueryArgs<GetTypeQueryVariables>, 'query'>) {
  return Urql.useQuery<GetTypeQuery, GetTypeQueryVariables>({ query: GetTypeDocument, ...options });
};
export const GetRolesDocument = gql`
    query getRoles {
  getRoles {
    id
    sys
    name
  }
}
    `;

export function useGetRolesQuery(options?: Omit<Urql.UseQueryArgs<GetRolesQueryVariables>, 'query'>) {
  return Urql.useQuery<GetRolesQuery, GetRolesQueryVariables>({ query: GetRolesDocument, ...options });
};
export const GetRoleDocument = gql`
    query getRole($name: String!) {
  getRole(name: $name) {
    id
    name
    permissions {
      id
      name
    }
  }
}
    `;

export function useGetRoleQuery(options: Omit<Urql.UseQueryArgs<GetRoleQueryVariables>, 'query'>) {
  return Urql.useQuery<GetRoleQuery, GetRoleQueryVariables>({ query: GetRoleDocument, ...options });
};
export const GetAllCategoriesDocument = gql`
    query getAllCategories {
  getAllCategories {
    id
    name
  }
}
    `;

export function useGetAllCategoriesQuery(options?: Omit<Urql.UseQueryArgs<GetAllCategoriesQueryVariables>, 'query'>) {
  return Urql.useQuery<GetAllCategoriesQuery, GetAllCategoriesQueryVariables>({ query: GetAllCategoriesDocument, ...options });
};
export const GetCategoriesDocument = gql`
    query getCategories($type: String!) {
  getCategories(type: $type) {
    id
    name
  }
}
    `;

export function useGetCategoriesQuery(options: Omit<Urql.UseQueryArgs<GetCategoriesQueryVariables>, 'query'>) {
  return Urql.useQuery<GetCategoriesQuery, GetCategoriesQueryVariables>({ query: GetCategoriesDocument, ...options });
};
export const GetAllAddressDocument = gql`
    query getAllAddress {
  getAllAddress {
    id
    country
    city
    district
    ward
    street
    zip
  }
}
    `;

export function useGetAllAddressQuery(options?: Omit<Urql.UseQueryArgs<GetAllAddressQueryVariables>, 'query'>) {
  return Urql.useQuery<GetAllAddressQuery, GetAllAddressQueryVariables>({ query: GetAllAddressDocument, ...options });
};