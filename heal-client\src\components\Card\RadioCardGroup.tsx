import React, { useEffect, useState } from "react";
import { HStack, useRadioGroup } from "@chakra-ui/react";
import RadioCard from "./RadioCard";

interface ITableRadioGroupProps {
  value: string;
  options: string[];
  onValueChange: (newValue: string) => any;
}

export const TableRadioGroup: React.FC<ITableRadioGroupProps> = (
  props: any
) => {
  const [value, setValue] = useState(props.value);
  const options: string[] = props.options;
  useEffect(() => {
    setValue(props.value);
  }, [props.value]);
  const { getRootProps, getRadioProps } = useRadioGroup({
    name: "options",
    defaultValue: value,
    value: value,
    onChange: (clickedValue) => {
      setValue(clickedValue);
      props.onValueChange(clickedValue);
    },
  });

  const group = getRootProps();

  return (
    <HStack {...group}>
      {options.map((value: string) => {
        const radio = getRadioProps({ value });
        return (
          <RadioCard key={value} {...radio}>
            {value}
          </RadioCard>
        );
      })}
    </HStack>
  );
};
