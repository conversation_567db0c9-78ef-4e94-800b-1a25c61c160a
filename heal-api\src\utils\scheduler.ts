import cron from "node-cron";
import { deleteUnclearedSales } from "./utils";
import logger from "./logger";
import { checkExpiredPayments } from "./paymentUtils";

const scheduler = () => {
  // Existing midnight cleanup
  cron.schedule("0 0 * * *", () => {
    const bigText = `
    ██████╗ ███████╗██╗     ███████╗████████╗██╗███╗   ██╗ ██████╗ 
    ██╔══██╗██╔════╝██║     ██╔════╝╚══██╔══╝██║████╗  ██║██╔════╝ 
    ██║  ██║█████╗  ██║     █████╗     ██║   ██║██╔██╗ ██║██║  ███╗
    ██║  ██║██╔══╝  ██║     ██╔══╝     ██║   ██║██║╚██╗██║██║   ██║
    ██████╔╝███████╗███████╗███████╗   ██║   ██║██║ ╚████║╚██████╔╝
    ╚═════╝ ╚══════╝╚══════╝╚══════╝   ╚═╝   ╚═╝╚═╝  ╚═══╝ ╚═════╝ 
    `;
    console.log("\x1b[31m%s\x1b[0m", bigText);
    deleteUnclearedSales();

    // Check for expired payments
    logger.info("Running payment expiration check");
    checkExpiredPayments()
      .then(() => logger.info("Payment check completed"))
      .catch((err: any) => logger.error("Error in payment check:", err));
  });
};

export default scheduler;
