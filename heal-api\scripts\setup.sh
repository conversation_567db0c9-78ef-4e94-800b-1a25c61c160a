#!/bin/bash

# Create certificates directory
mkdir -p certificates

# Generate SSL certificates if they don't exist
if [ ! -f certificates/localhost.pem ]; then
    openssl req -x509 \
        -newkey rsa:2048 \
        -keyout certificates/localhost-key.pem \
        -out certificates/localhost.pem \
        -days 365 \
        -nodes \
        -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"
fi

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    cat > .env << EOL
NODE_ENV=development
API_PORT=4000
API_HTTPS_PORT=443
CLIENT_PORT=3000
POSTGRES_DB=heal_db
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
DB_TYPE=postgres
REDIS_PORT=6379
COOKIE_NAME=qid
CUSTOM_SECRET=your_secret_here
FRONTEND_WEB_ORIGIN=http://localhost:3000
MY_SENDER_EMAIL=<EMAIL>
MY_SENDER_EMAIL_PASSWORD=your_email_password
MY_SSL_EMAIL_SERVER=smtp.example.com
VITE_API_URL=http://localhost:4000/graphql
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_key
EOL
    echo "Created .env file. Please update it with your configuration."
fi

# Start the application
docker-compose up -d