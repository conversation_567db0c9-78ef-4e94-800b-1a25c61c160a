import { PowerSyncServiceClient } from "./PowerSyncServiceClient";

const POWERSYNC_ENDPOINT =
  process.env.POWERSYNC_ENDPOINT || "https://your-powersync-api-url";

export class PowerSyncConnector {
  /**
   * Called by PowerSync client to get credentials for connecting to the PowerSync service.
   * Returns endpoint and JWT token for authentication.
   */
  async fetchCredentials() {
    const token = PowerSyncServiceClient.getDecryptedToken();
    if (!token) throw new Error("PowerSync token not found or invalid");
    return {
      endpoint: POWERSYNC_ENDPOINT,
      token,
    };
  }

  /**
   * Empty uploadData method to satisfy PowerSyncBackendConnector interface.
   * PowerSync will handle all sync automatically.
   */
  async uploadData(_database: any) {
    // No-op: PowerSync handles all sync
    return;
  }
}
