import { Field, ObjectType } from "type-graphql";
import { Column, <PERSON>tity, ManyToMany, OneToMany, Unique } from "typeorm";
import { User } from "./User";
import { AuditBaseEntity } from "./AuditEntity";
import { Permission } from "./Permission";
import { Employee } from "./Employee";

@ObjectType()
@Entity()
@Unique(["name", "companyId"]) // Ensure the combination of name and companyId is unique
export class Role extends AuditBaseEntity {
  @Field(() => [User], { nullable: true })
  @OneToMany(() => User, (user) => user.role)
  users: User[];

  @Field(() => [Employee], { nullable: true })
  @OneToMany(() => Employee, (employee) => employee.role)
  employees: Employee[];

  @Field(() => [Permission], { nullable: true })
  @ManyToMany(() => Permission, (permission) => permission.roles)
  permissions: Permission[];

  @Field(() => Boolean)
  @Column({ type: "boolean", default: false })
  sys!: boolean;

  @Field(() => String)
  @Column({ type: "text" })
  name!: string;
}
