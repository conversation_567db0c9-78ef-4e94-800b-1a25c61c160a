import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import {
  Flex,
  Button,
  FormControl,
  FormLabel,
  Input,
  Text,
  useColorModeValue,
  FormErrorMessage,
  useToast,
  Grid,
  GridItem,
  RadioGroup,
  Radio,
  HStack,
  Select,
} from "@chakra-ui/react";
import {
  useGetPatientQuery,
  useEditPatientMutation,
} from "../../generated/graphql";
import { HSeparator } from "../../components/Separator/Separator";
import { useLocation } from "react-router-dom";

interface IEditPatientProps {}

interface State {
  patientId: number;
}

const EditPatient: React.FC<IEditPatientProps> = () => {
  const toast = useToast({
    position: "top",
  });
  const location = useLocation();
  const { patientId } = location.state as State;
  const [{ data }] = useGetPatientQuery({
    variables: { id: patientId }, // Fetch the patient data by ID
  });
  const [, updatePatient] = useEditPatientMutation();
  const [updateError, setUpdateError] = useState("");

  interface IPatientInput {
    // Personal info
    firstname: string;
    middlename: string;
    lastname: string;
    gender: string;
    DOB: string;
    religion: string;
    email: string;
    phone: string;
    nationalId: string;
    status: string;
    // Address variables
    country: string;
    city: string;
    district: string;
    ward: string;
    street: string;
    // Next of kin
    nextOfKinName: string;
    nextOfKinPhone: string;
    nextOfKinRelationship: string;
    // Insurance
    insuranceProvider: string;
    insuranceUserId: string;
    insuranceStatus: string;
  }

  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<IPatientInput>();

  useEffect(() => {
    if (data?.getPatient) {
      // Populate the form with the patient's current data
      const patient = data.getPatient;
      setValue("firstname", patient.firstname);
      setValue("middlename", patient.middlename);
      setValue("lastname", patient.lastname);
      setValue("gender", patient.gender);
      setValue("DOB", patient.dateOfBirth);
      setValue("religion", patient.religion);
      setValue("email", patient.email!);
      setValue("phone", patient.phone);
      setValue("nationalId", patient.nationalId!);
      setValue("status", patient.status);
      setValue("country", patient.address.country);
      setValue("city", patient.address.city);
      setValue("district", patient.address.district);
      setValue("ward", patient.address.ward);
      setValue("street", patient.address.street);
      setValue("nextOfKinName", patient.nextOfKinName);
      setValue("nextOfKinPhone", patient.nextOfKinPhone);
      setValue("nextOfKinRelationship", patient.nextOfKinRelationship);
      setValue("insuranceProvider", patient.insuranceProvider);
      setValue("insuranceUserId", patient.insuranceUserId);
      setValue("insuranceStatus", patient.insuranceStatus);
    }
  }, [data, setValue]);

  async function onSubmit(values: IPatientInput) {
    setUpdateError("");
    const args = {
      firstname: values.firstname,
      middlename: values.middlename,
      lastname: values.lastname,
      gender: values.gender,
      status: values.status,
      DOB: values.DOB,
      religion: values.religion,
      email: values.email,
      phone: values.phone,
      nationalId: values.nationalId,
      country: values.country,
      city: values.city,
      district: values.district,
      ward: values.ward,
      street: values.street,
      nextOfKinName: values.nextOfKinName,
      nextOfKinPhone: values.nextOfKinPhone,
      nextOfKinRelationship: values.nextOfKinRelationship,
      insuranceProvider: values.insuranceProvider,
      insuranceUserId: values.insuranceUserId,
      insuranceStatus: values.insuranceStatus,
    };
    const { data: patient } = await updatePatient({
      id: data?.getPatient?.id!,
      params: args,
    });
    if (patient?.editPatient.error) {
      toast({
        title: "Update failed!",
        variant: "left-accent",
        status: "error",
        isClosable: true,
      });
      return setUpdateError(patient?.editPatient.error.message);
    } else if (!patient?.editPatient.error) {
      toast({
        title: "Patient updated successfully!",
        variant: "left-accent",
        status: "success",
        isClosable: true,
      });
    }
  }

  const textColor = useColorModeValue("gray.700", "white");
  const bgForm = useColorModeValue("white", "navy.800");

  return (
    <Flex
      w="100%"
      h="100%"
      alignItems="center"
      justifyContent="center"
      mb="60px"
      mt={{ base: "50px", md: "50px" }}
    >
      <Flex
        zIndex="2"
        direction="column"
        w="80%"
        background="transparent"
        borderRadius="15px"
        p="40px"
        mx={{ base: "100px" }}
        m={{ base: "20px", md: "auto" }}
        bg={bgForm}
        boxShadow={useColorModeValue(
          "0px 5px 14px rgba(0, 0, 0, 0.05)",
          "unset"
        )}
      >
        <Text
          fontSize="xl"
          color={textColor}
          fontWeight="bold"
          textAlign="center"
          mb="12px"
        >
          Edit Patient
        </Text>

        <HSeparator mt="3px" mb="3px" />
        <Text textAlign="center" mt="0px" mb="0px">
          Edit Patient Details:
        </Text>
        <HSeparator mb="3px" mt="3px" />
        {updateError && (
          <Text pb="15px" m={"auto"} color="red.500" textColor="red.300">
            {updateError}
          </Text>
        )}
        <form onSubmit={handleSubmit(onSubmit)}>
          <Grid templateColumns="repeat(8, 1fr)" gap={6} mt={3}>
            {/* Personal Information */}
            <FormControl
              as={GridItem}
              colSpan={2}
              mb={0}
              isInvalid={errors.firstname as any}
            >
              <FormLabel htmlFor="firstname" fontSize="xs">
                Firstname
              </FormLabel>
              <Input
                variant="filled"
                ms="4px"
                type="text"
                fontSize="xs"
                placeholder="User Firstname"
                mb="4px"
                size="lg"
                id="firstname"
                {...register("firstname", {
                  required: "User firstname is required",
                  pattern: {
                    value: /^[a-zA-Z- -0-9]+$/,
                    message: "User firstname can not have special characters",
                  },
                })}
              />
              <FormErrorMessage mb="10px" fontSize="xs">
                {errors.firstname && (errors.firstname.message as any)}
              </FormErrorMessage>
            </FormControl>
            <FormControl
              as={GridItem}
              colSpan={2}
              mb={0}
              isInvalid={errors.middlename as any}
            >
              <FormLabel htmlFor="middlename" fontSize="xs">
                Middlename
              </FormLabel>
              <Input
                variant="filled"
                ms="4px"
                type="text"
                fontSize="xs"
                placeholder="Patient Middlename"
                mb="4px"
                size="lg"
                id="middlename"
                {...register("middlename", {
                  required: "Patient middlename is required",
                  pattern: {
                    value: /^[a-zA-Z- -0-9]+$/,
                    message:
                      "Patient middlename can not have special characters",
                  },
                })}
              />
              <FormErrorMessage mb="10px" fontSize="xs">
                {errors.middlename && (errors.middlename.message as any)}
              </FormErrorMessage>
            </FormControl>
            <FormControl
              as={GridItem}
              colSpan={2}
              mb={0}
              isInvalid={errors.lastname as any}
            >
              <FormLabel htmlFor="lastname" fontSize="xs">
                Lastname
              </FormLabel>
              <Input
                variant="filled"
                ms="4px"
                type="text"
                fontSize="xs"
                placeholder="Patient Lastname"
                mb="4px"
                size="lg"
                id="lastname"
                {...register("lastname", {
                  required: "Patient lastname is required",
                  pattern: {
                    value: /^[a-zA-Z- -0-9]+$/,
                    message: "Patient lastname can not have special characters",
                  },
                })}
              />
              <FormErrorMessage mb="10px" fontSize="xs">
                {errors.lastname && (errors.lastname.message as any)}
              </FormErrorMessage>
            </FormControl>
            <FormControl
              as={GridItem}
              colSpan={2}
              mb={0}
              isInvalid={errors.email as any}
            >
              <FormLabel htmlFor="email" fontSize="xs">
                Email
              </FormLabel>
              <Input
                variant="filled"
                ms="4px"
                type="email"
                fontSize="xs"
                placeholder="User Email"
                mb="4px"
                size="lg"
                id="email"
                {...register("email", {
                  required: "User email is required",
                  pattern: {
                    value: /^\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/,
                    message: "User email is incorrectly formatted",
                  },
                })}
              />

              <FormErrorMessage mb="10px" fontSize="xs">
                {errors.email && (errors.email.message as any)}
              </FormErrorMessage>
            </FormControl>
            <FormControl
              as={GridItem}
              colSpan={2}
              mb={0}
              isInvalid={errors.phone as any}
            >
              <FormLabel htmlFor="phone" fontSize="xs">
                Phone
              </FormLabel>
              <Input
                variant="filled"
                ms="4px"
                type="text" // Change type to text to allow spaces
                fontSize="xs"
                maxLength={12} // Limit the total length to 12 characters
                placeholder="User Phone"
                mb="4px"
                size="lg"
                id="phone"
                {...register("phone", {
                  required: "User phone is required",
                  pattern: {
                    value: /^0(\d{3})\s?(\d{3})\s?(\d{3})$/,
                    message: "User phone must be in the format 0xxx xxx xxx",
                  },
                })}
                onChange={(e) => {
                  let value = e.target.value.replace(/\D/g, ""); // Remove non-digit characters
                  // Ensure the first character is "0"
                  value = value.replace(/^0*(\d*)/, "0$1");
                  // Limit to 12 characters, including spaces
                  value = value.slice(0, 12);
                  const formattedValue = value.replace(
                    /(\d{1})(\d{3})(\d{3})(\d{3})/,
                    "$1$2 $3 $4"
                  ); // Add spaces between numbers
                  e.target.value = formattedValue;
                }}
              />

              <FormErrorMessage fontSize="xs">
                {errors.phone && (errors.phone.message as any)}
              </FormErrorMessage>
            </FormControl>
            <FormControl
              as={GridItem}
              colSpan={2}
              mb={0}
              isInvalid={errors.religion as any}
            >
              <FormLabel htmlFor="religion" fontSize="xs">
                Religion
              </FormLabel>
              <Input
                variant="filled"
                ms="4px"
                type="text"
                fontSize="xs"
                placeholder="Patient Religion"
                mb="4px"
                size="lg"
                id="religion"
                {...register("religion", {
                  required: "Patient religion is required",
                  pattern: {
                    value: /^[a-zA-Z- -0-9]+$/,
                    message: "Patient religion can not have special characters",
                  },
                })}
              />
              <FormErrorMessage mb="10px" fontSize="xs">
                {errors.religion && (errors.religion.message as any)}
              </FormErrorMessage>
            </FormControl>
            <FormControl
              as={GridItem}
              colSpan={2}
              mb={0}
              isInvalid={errors.nationalId as any}
            >
              <FormLabel htmlFor="nationalId" fontSize="xs">
                National ID
              </FormLabel>
              <Input
                variant="filled"
                ms="4px"
                type="text"
                fontSize="xs"
                placeholder="YYYYMMDD-12345-12345-12"
                mb="4px"
                size="lg"
                id="nationalId"
                {...register("nationalId", {
                  required: "National ID is required",
                  pattern: {
                    value:
                      /^(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12][0-9]|3[01])-\d{5}-\d{5}-\d{2}$/,
                    message: "National ID is incorrectly formatted",
                  },
                })}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                  let value = e.target.value.replace(/\D/g, ""); // Remove non-digit characters

                  // Insert dashes at the correct positions
                  if (value.length > 8)
                    value = value.replace(/(\d{8})(\d{1,5})/, "$1-$2");
                  if (value.length > 14)
                    value = value.replace(
                      /(\d{8})-(\d{5})(\d{1,5})/,
                      "$1-$2-$3"
                    );
                  if (value.length > 20)
                    value = value.replace(
                      /(\d{8})-(\d{5})-(\d{5})(\d{1,2})/,
                      "$1-$2-$3-$4"
                    );

                  // Limit the length to 23 characters (including dashes)
                  value = value.slice(0, 23);

                  setValue("nationalId", value, {
                    shouldValidate: true,
                  });
                }}
              />
              <FormErrorMessage fontSize="xs">
                {errors.nationalId && (errors.nationalId.message as any)}
              </FormErrorMessage>
            </FormControl>
            <FormControl
              as={GridItem}
              colSpan={2}
              mb={0}
              isInvalid={errors.DOB as any}
            >
              <FormLabel htmlFor="DOB" fontSize="xs">
                Date of Birth
              </FormLabel>
              <Input
                variant="filled"
                ms="4px"
                type="date"
                fontSize="xs"
                placeholder="Date of Birth"
                mb="4px"
                size="lg"
                id="DOB"
                {...register("DOB", {
                  required: "Date of Birth is required",
                })}
              />
              <FormErrorMessage mb="10px" fontSize="xs">
                {errors.DOB && (errors.DOB.message as any)}
              </FormErrorMessage>
            </FormControl>
            <FormControl
              as={GridItem}
              colSpan={2}
              mb={0}
              isInvalid={errors.gender as any}
            >
              <FormLabel htmlFor="gender" fontSize="xs">
                Gender
              </FormLabel>
              <RadioGroup defaultValue="male">
                <HStack spacing="24px">
                  <Radio
                    value="male"
                    id="male"
                    {...register("gender", {
                      required: "Gender is required",
                    })}
                  >
                    Male
                  </Radio>
                  <Radio
                    value="female"
                    id="female"
                    {...register("gender", {
                      required: "Gender is required",
                    })}
                  >
                    Female
                  </Radio>
                </HStack>
              </RadioGroup>
              <FormErrorMessage fontSize="xs">
                {errors.gender && (errors.gender.message as any)}
              </FormErrorMessage>
            </FormControl>
            <FormControl
              as={GridItem}
              colSpan={6}
              mb={0}
              isInvalid={errors.status as any}
            >
              <FormLabel htmlFor="status" fontSize="xs">
                Patient Status
              </FormLabel>
              <Select
                id="status"
                variant="filled"
                defaultValue="normal"
                {...register("status", {
                  required: "Patient status is required",
                })}
              >
                <option value="normal">Normal</option>
                <option value="critical" color="red">
                  Critical
                </option>
              </Select>
              <FormErrorMessage fontSize="xs">
                {errors.status && (errors.status.message as any)}
              </FormErrorMessage>
            </FormControl>
            {/* Address Information */}
            <Flex flexDirection={"column"} as={GridItem} colSpan={8}>
              {" "}
              <HSeparator mt="3px" mb="3px" />
              <Text textAlign="center" mt="0px" mb="0px">
                Patient Address Information:
              </Text>
              <HSeparator mb="3px" mt="3px" />
            </Flex>
            <FormControl
              as={GridItem}
              colSpan={4}
              mb={0}
              isInvalid={errors.country as any}
            >
              <FormLabel htmlFor="country" fontSize="xs">
                Country
              </FormLabel>
              <Input
                variant="filled"
                ms="4px"
                type="text"
                fontSize="xs"
                placeholder="Country"
                defaultValue={"Tanzania, Republic of"}
                mb="4px"
                size="lg"
                id="country"
                {...register("country", {
                  required: "Country is required",
                })}
              />
              <FormErrorMessage mb="10px" fontSize="xs">
                {errors.country && (errors.country.message as any)}
              </FormErrorMessage>
            </FormControl>
            <FormControl
              as={GridItem}
              colSpan={2}
              mb={0}
              isInvalid={errors.city as any}
            >
              <FormLabel htmlFor="city" fontSize="xs">
                City
              </FormLabel>
              <Input
                variant="filled"
                ms="4px"
                type="text"
                fontSize="xs"
                placeholder="City"
                mb="4px"
                size="lg"
                id="city"
                {...register("city", {
                  required: "City is required",
                })}
              />
              <FormErrorMessage mb="10px" fontSize="xs">
                {errors.city && (errors.city.message as any)}
              </FormErrorMessage>
            </FormControl>
            <FormControl
              as={GridItem}
              colSpan={2}
              mb={0}
              isInvalid={errors.district as any}
            >
              <FormLabel htmlFor="district" fontSize="xs">
                District
              </FormLabel>
              <Input
                variant="filled"
                ms="4px"
                type="text"
                fontSize="xs"
                placeholder="District"
                mb="4px"
                size="lg"
                id="district"
                {...register("district", {
                  required: "District is required",
                })}
              />
              <FormErrorMessage mb="10px" fontSize="xs">
                {errors.district && (errors.district.message as any)}
              </FormErrorMessage>
            </FormControl>
            <FormControl
              as={GridItem}
              colSpan={2}
              mb={0}
              isInvalid={errors.ward as any}
            >
              <FormLabel htmlFor="ward" fontSize="xs">
                Ward
              </FormLabel>
              <Input
                variant="filled"
                ms="4px"
                type="text"
                fontSize="xs"
                placeholder="Ward"
                mb="4px"
                size="lg"
                id="ward"
                {...register("ward", {
                  required: "Ward is required",
                })}
              />
              <FormErrorMessage mb="10px" fontSize="xs">
                {errors.ward && (errors.ward.message as any)}
              </FormErrorMessage>
            </FormControl>
            <FormControl
              as={GridItem}
              colSpan={2}
              mb={0}
              isInvalid={errors.street as any}
            >
              <FormLabel htmlFor="street" fontSize="xs">
                Street
              </FormLabel>
              <Input
                variant="filled"
                ms="4px"
                type="text"
                fontSize="xs"
                placeholder="Street"
                mb="4px"
                size="lg"
                id="street"
                {...register("street", {
                  required: "Street is required",
                })}
              />
              <FormErrorMessage mb="10px" fontSize="xs">
                {errors.street && (errors.street.message as any)}
              </FormErrorMessage>
            </FormControl>

            {/* Next of Kin Information */}
            <Flex flexDirection={"column"} as={GridItem} colSpan={8}>
              {" "}
              <HSeparator mt="3px" mb="3px" />
              <Text textAlign="center" mt="0px" mb="0px">
                Patient Next Of Kin Details:
              </Text>
              <HSeparator mb="3px" mt="3px" />
            </Flex>
            <FormControl
              as={GridItem}
              colSpan={2}
              mb={0}
              isInvalid={errors.nextOfKinName as any}
            >
              <FormLabel htmlFor="nextOfKinName" fontSize="xs">
                Next of Kin Name
              </FormLabel>
              <Input
                variant="filled"
                ms="4px"
                type="text"
                fontSize="xs"
                placeholder="Next of Kin Name"
                mb="4px"
                size="lg"
                id="nextOfKinName"
                {...register("nextOfKinName", {
                  required: "Next of Kin Name is required",
                })}
              />
              <FormErrorMessage mb="10px" fontSize="xs">
                {errors.nextOfKinName && (errors.nextOfKinName.message as any)}
              </FormErrorMessage>
            </FormControl>
            <FormControl
              as={GridItem}
              colSpan={2}
              mb={0}
              isInvalid={errors.nextOfKinPhone as any}
            >
              <FormLabel htmlFor="nextOfKinPhone" fontSize="xs">
                Next of Kin Phone
              </FormLabel>
              <Input
                variant="filled"
                ms="4px"
                type="text"
                fontSize="xs"
                placeholder="Next of Kin Phone"
                mb="4px"
                size="lg"
                id="nextOfKinPhone"
                {...register("nextOfKinPhone", {
                  required: "Next of Kin Phone is required",
                })}
              />
              <FormErrorMessage mb="10px" fontSize="xs">
                {errors.nextOfKinPhone &&
                  (errors.nextOfKinPhone.message as any)}
              </FormErrorMessage>
            </FormControl>
            <FormControl
              as={GridItem}
              colSpan={2}
              mb={0}
              isInvalid={errors.nextOfKinRelationship as any}
            >
              <FormLabel htmlFor="nextOfKinRelationship" fontSize="xs">
                Next of Kin Relationship
              </FormLabel>
              <Input
                variant="filled"
                ms="4px"
                type="text"
                fontSize="xs"
                placeholder="Next of Kin Relationship"
                mb="4px"
                size="lg"
                id="nextOfKinRelationship"
                {...register("nextOfKinRelationship", {
                  required: "Next of Kin Relationship is required",
                })}
              />
              <FormErrorMessage mb="10px" fontSize="xs">
                {errors.nextOfKinRelationship &&
                  (errors.nextOfKinRelationship.message as any)}
              </FormErrorMessage>
            </FormControl>

            {/* Continue similar pattern for Next of Kin fields */}

            {/* Insurance Information */}
            <Flex flexDirection={"column"} as={GridItem} colSpan={8}>
              {" "}
              <HSeparator mt="3px" mb="3px" />
              <Text textAlign="center" mt="0px" mb="0px">
                Patient Insurance Details:
              </Text>
              <HSeparator mb="3px" mt="3px" />
            </Flex>
            <FormControl
              as={GridItem}
              colSpan={2}
              mb={0}
              isInvalid={errors.insuranceProvider as any}
            >
              <FormLabel htmlFor="insuranceProvider" fontSize="xs">
                Insurance Provider
              </FormLabel>
              <Input
                variant="filled"
                ms="4px"
                type="text"
                fontSize="xs"
                placeholder="Insurance Provider"
                mb="4px"
                size="lg"
                id="insuranceProvider"
                {...register("insuranceProvider", {
                  required: "Insurance Provider is required",
                })}
              />
              <FormErrorMessage mb="10px" fontSize="xs">
                {errors.insuranceProvider &&
                  (errors.insuranceProvider.message as any)}
              </FormErrorMessage>
            </FormControl>

            <FormControl
              as={GridItem}
              colSpan={2}
              mb={0}
              isInvalid={errors.insuranceUserId as any}
            >
              <FormLabel htmlFor="insuranceUserId" fontSize="xs">
                Insurance User ID
              </FormLabel>
              <Input
                variant="filled"
                ms="4px"
                type="text"
                fontSize="xs"
                placeholder="Insurance User ID"
                mb="4px"
                size="lg"
                id="insuranceUserId"
                {...register("insuranceUserId", {
                  required: "Insurance User ID is required",
                })}
              />
              <FormErrorMessage mb="10px" fontSize="xs">
                {errors.insuranceUserId &&
                  (errors.insuranceUserId.message as any)}
              </FormErrorMessage>
            </FormControl>

            <FormControl
              as={GridItem}
              colSpan={2}
              mb={0}
              isInvalid={errors.insuranceStatus as any}
            >
              <FormLabel htmlFor="insuranceStatus" fontSize="xs">
                Insurance Status
              </FormLabel>
              <Input
                variant="filled"
                ms="4px"
                type="text"
                fontSize="xs"
                placeholder="Insurance Status"
                mb="4px"
                size="lg"
                id="insuranceStatus"
                {...register("insuranceStatus", {
                  required: "Insurance Status is required",
                })}
              />
              <FormErrorMessage mb="10px" fontSize="xs">
                {errors.insuranceStatus &&
                  (errors.insuranceStatus.message as any)}
              </FormErrorMessage>
            </FormControl>

            {/* Continue similar pattern for other insurance fields */}
          </Grid>

          <Button
            mt="6"
            colorScheme="teal"
            isLoading={isSubmitting}
            type="submit"
            w="full"
          >
            Update Patient
          </Button>
        </form>
      </Flex>
    </Flex>
  );
};

export default EditPatient;
