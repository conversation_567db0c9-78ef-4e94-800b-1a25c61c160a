import React, { useContext, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import {
  Flex,
  Button,
  FormControl,
  FormLabel,
  Input,
  Text,
  useColorModeValue,
  FormErrorMessage,
  useToast,
  Grid,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  GridItem,
  Tag,
  TagLabel,
  Badge,
  Tfoot,
} from "@chakra-ui/react";
import {
  useGetStoresQuery,
  Item,
  useQuickSaleMutation,
  useGetSalesQuery,
  useGetMerchandiseItemsQuery,
  useChangeInventorySoldStatusMutation,
  Inventory,
} from "../../generated/graphql";
import { formatToMoney } from "../../utils/Helpers";
import { MeContext } from "../../components/Wrapper";
import { jsPDF } from "jspdf";

//icons
import { MdOutlineMoveUp } from "react-icons/md";
import AllItemsInput from "../Inventory/components/AllItemsInput";
import UnitInput from "../Inventory/components/UnitInput";
import BatchInput from "../Inventory/components/BatchInput";
import { backgroundImage1 } from "../../assets/img/base64";

interface IPharmacyProps {}

interface ISaleItem {
  itemId: number;
  quantity: number;
  remarks: string;
  batch: string;
  unit: string;
}

const Pharmacy: React.FC<IPharmacyProps> = () => {
  const toast = useToast({
    position: "top",
  });

  const nameColor = useColorModeValue("gray.500", "white");

  const me = useContext(MeContext);

  const [, saleItems] = useQuickSaleMutation();
  const [{ data: stores }] = useGetStoresQuery();
  const [{ data: items }] = useGetMerchandiseItemsQuery({
    requestPolicy: "network-only",
  });
  const [{ data: sales }, reGetSales] = useGetSalesQuery({
    requestPolicy: "network-only",
  });
  const [, completeSale] = useChangeInventorySoldStatusMutation();
  const [selectedItem, setSelectedItem] = useState<number | null>(null);
  const [itemsAdded, setItemsAdded] = useState<ISaleItem[]>([]);
  const [unitType, setUnitType] = useState("");

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
    setValue,
  } = useForm({
    defaultValues: {
      itemId: 0,
      quantity: 0,
      remarks: "Unprescribed",
      batch: "",
      unit: "",
    },
  });

  useEffect(() => {
    const interval = setInterval(() => {
      reGetSales({
        requestPolicy: "network-only",
      });
    }, 5000);

    // Cleanup function to clear the interval when the component unmounts
    return () => clearInterval(interval);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleItemChange = (value: any) => {
    setSelectedItem(value.value);
    setValue("itemId", value.value);
    reset();
  };

  const handleUnitChange: any = (value: any) => {
    if (value) {
      setUnitType(value.value);
      setValue("unit", value.value);
    } else {
      setUnitType("");
    }
  };

  const handleBatchChange = (value: any) => {
    setValue("batch", value.value);
  };

  const onSubmit = (values: ISaleItem) => {
    setItemsAdded((oldItems) => {
      return [
        ...oldItems,
        { ...values, itemId: selectedItem!, unit: unitType },
      ];
    });
    setSelectedItem(null);
    setUnitType("");
    return reset();
  };

  async function onDBSubmit() {
    const args = itemsAdded.map((item: ISaleItem) => ({
      itemId: Number(item.itemId),
      quantity: Number(item.quantity),
      remarks: item.remarks,
      unit: item.unit,
      batch: item.batch,
    }));
    const { data } = await saleItems({ args });
    if (data?.quickSale.error) {
      toast({
        title: data.quickSale.error.message,
        status: "error",
        isClosable: true,
      });
    } else {
      reset();
      setSelectedItem(null);
      setItemsAdded([]);
      reGetSales();
      return toast({
        title: `Sale request sent to cashier successfully!`,
        variant: "left-accent",
        status: "success",
        isClosable: true,
      });
    }
  }

  const calculateTotalSale = () => {
    return itemsAdded.reduce((total, item) => {
      const theItem = items?.getMerchandiseItems.find(
        (i) => i.id === item.itemId
      );
      if (!theItem) return total;

      const price =
        item.unit === theItem.unit
          ? theItem.sellingPrice
          : item.unit === theItem.pieceUnit
          ? theItem.pieceSellingPrice
          : item.unit === theItem.subPieceUnit
          ? theItem.subPieceSellingPrice
          : 0;

      return total + price * item.quantity;
    }, 0);
  };

  const printInvoice = (sale: Inventory) => {
    const doc = new jsPDF();
    const backgroundImage = backgroundImage1;

    // Adding the background image
    doc.addImage(backgroundImage, "JPEG", 0, 0, 210, 297); // Adjust the dimensions according to your background image and page size

    // Hospital information
    doc.setFontSize(17);
    doc.text("Heal Demo Hospital", 10, 10);
    doc.setFontSize(14);
    doc.text("P. O. Box 123 Mbeya,", 10, 20);
    doc.text("+255 722 343 444", 10, 30);
    doc.text("<EMAIL>", 10, 40);

    // Bill To and Invoice Information
    doc.setFontSize(19);
    doc.text("Bill To", 10, 60);
    doc.setFontSize(14);
    doc.text("Anonymous,", 10, 70);
    doc.text(`Invoice # ${sale.id}`, 150, 60);
    doc.text(`Invoice Date: ${new Date().toLocaleDateString()}`, 150, 70);

    // Invoice items
    doc.setFontSize(16);
    doc.text("Description", 10, 90);
    doc.text("Amount", 150, 90);
    let yPosition = 100;
    sale.transfers.forEach((transfer) => {
      const item = sale.items.find((item) => item.id === transfer.itemId);
      if (item) {
        doc.setFontSize(14);
        doc.text(
          `${item.name} - ${
            transfer.quantity > 0
              ? transfer.quantity + " " + item.unit
              : transfer.pieceQuantity > 0
              ? transfer.pieceQuantity + " " + item.pieceUnit
              : transfer.subPieceQuantity > 0
              ? transfer.subPieceQuantity + " " + item.subPieceUnit
              : ""
          }`,
          10,
          yPosition
        );
        const itemCost =
          transfer.quantity * item.sellingPrice +
          transfer.pieceQuantity * item.pieceSellingPrice +
          transfer.subPieceQuantity * item.subPieceSellingPrice;
        doc.text(formatToMoney(Number(itemCost)), 150, yPosition);
        yPosition += 10;
      }
    });

    // Total Price
    doc.setFontSize(16);
    doc.text("Invoice Total", 10, yPosition + 10);
    doc.text(formatToMoney(Number(sale.bill?.amount)), 150, yPosition + 10);

    // Terms & Conditions
    doc.setFontSize(14);
    doc.text("Terms & Conditions", 10, yPosition + 30);
    doc.text(
      "Payment must be done today or the invoice will be invalid!",
      10,
      yPosition + 40
    );

    // Save the PDF
    doc.save(`Invoice-${sale.id}.pdf`);
  };

  const bgColor = useColorModeValue("white", "navy.800");
  const boxShadowColor = useColorModeValue(
    "0px 5px 14px rgba(0, 0, 0, 0.05)",
    "unset"
  );

  return (
    <Flex direction="column" position="relative" mb="90px" mt="100px">
      <Flex
        w="100%"
        mx="auto"
        justifyContent="space-between"
        mb="60px"
        pt={{ md: "0px" }}
        direction="column"
      >
        {(me?.role!.name === "admin" ||
          stores?.getStores.find((store) =>
            store.storeKeepers.find(
              (storeKeeper) => storeKeeper.id === me?.employee?.id
            )
          )?.name) && (
          <Flex
            w="100%"
            as={Grid}
            templateColumns="repeat(12, 1fr)"
            gap={6}
            mb="60px"
          >
            <Flex
              direction="column"
              background="transparent"
              borderRadius="15px"
              as={GridItem}
              colSpan={5}
              width={"100%"}
              p="40px"
              bg={bgColor}
              boxShadow={boxShadowColor}
            >
              <Text
                fontSize="xl"
                color={nameColor}
                fontWeight="bold"
                textAlign="center"
                mb="12px"
              >
                Sale Items
              </Text>
              <FormControl mb={4}>
                <FormLabel fontSize="xs">Item</FormLabel>
                <AllItemsInput
                  onChange={handleItemChange}
                  selectedItemsIds={itemsAdded.map((item) => {
                    return { itemId: item.itemId };
                  })}
                />
              </FormControl>
              {selectedItem && (
                <>
                  <Text
                    fontSize="xl"
                    color={nameColor}
                    fontWeight="bold"
                    textAlign="center"
                    mb="12px"
                  >
                    Item Sale Details
                  </Text>
                  <form onSubmit={handleSubmit(onSubmit)}>
                    <Grid templateColumns="repeat(2, 1fr)" gap={4} mb={4}>
                      <Input
                        type="hidden"
                        value={selectedItem}
                        {...register("itemId", { value: selectedItem })}
                      />
                      <FormControl
                        isInvalid={Boolean(errors.batch)}
                        mb={4}
                        as={GridItem}
                        colSpan={2}
                      >
                        <FormLabel htmlFor="batch" fontSize="xs">
                          Batch Number
                        </FormLabel>
                        <BatchInput
                          onChange={handleBatchChange}
                          storeId={undefined}
                          item={
                            items?.getMerchandiseItems.find(
                              (item) => item.id === selectedItem
                            ) as Item
                          }
                        />
                        <FormErrorMessage fontSize="xs">
                          {errors.batch?.message}
                        </FormErrorMessage>
                      </FormControl>{" "}
                      <FormControl isInvalid={Boolean(errors.quantity)} mb={4}>
                        <FormLabel htmlFor="quantity" fontSize="xs">
                          Quantity
                        </FormLabel>
                        <Input
                          id="quantity"
                          type="number"
                          placeholder="Quantity"
                          {...register("quantity", {
                            required: "This is required",
                          })}
                        />
                        <FormErrorMessage fontSize="xs">
                          {errors.quantity?.message}
                        </FormErrorMessage>
                      </FormControl>
                      <FormControl isInvalid={Boolean(errors.unit)} mb={4}>
                        <FormLabel htmlFor="unit" fontSize="xs">
                          Unit
                        </FormLabel>
                        <UnitInput
                          onChange={handleUnitChange}
                          selectedItem={
                            items?.getMerchandiseItems.find(
                              (item) => item.id === selectedItem
                            ) as Item
                          }
                          price={true}
                        />
                        <FormErrorMessage fontSize="xs">
                          {errors.unit?.message}
                        </FormErrorMessage>
                      </FormControl>
                      <FormControl
                        isInvalid={Boolean(errors.remarks)}
                        mb={4}
                        as={GridItem}
                        colSpan={2}
                      >
                        <FormLabel htmlFor="remarks" fontSize="xs">
                          Remarks
                        </FormLabel>
                        <Input
                          id="remarks"
                          placeholder="Remarks"
                          {...register("remarks", {
                            required: "This is required",
                          })}
                        />
                        <FormErrorMessage fontSize="xs">
                          {errors.remarks?.message}
                        </FormErrorMessage>
                      </FormControl>
                    </Grid>
                    <Button
                      colorScheme="blue"
                      type="submit"
                      isLoading={isSubmitting}
                      w={"100%"}
                    >
                      Add Item
                    </Button>
                  </form>
                </>
              )}
            </Flex>
            <Flex
              direction="column"
              background="transparent"
              borderRadius="15px"
              as={GridItem}
              colSpan={7}
              width={"100%"}
              p="40px"
              bg={bgColor}
              boxShadow={boxShadowColor}
            >
              <Text
                fontSize="xl"
                color={nameColor}
                fontWeight="bold"
                textAlign="center"
                mb="12px"
              >
                Items For Sale
              </Text>
              <Table variant="simple" mb="20px">
                <Thead>
                  <Tr>
                    <Th>Name</Th>
                    <Th>Quantity</Th>
                    <Th>Remarks</Th>
                    <Th>Batch Number</Th>
                    <Th>Total Price</Th>
                  </Tr>
                </Thead>
                {itemsAdded.length > 0 && (
                  <>
                    {" "}
                    <Tbody>
                      {itemsAdded.map((item, index) => {
                        // Check if items and getMerchandiseItems are defined
                        if (!items || !items.getMerchandiseItems) {
                          console.error(
                            "Items or getMerchandiseItems is undefined"
                          );
                          return null;
                        }

                        const theItem = items.getMerchandiseItems.find(
                          (i) => i.id === item.itemId
                        );

                        if (!theItem) {
                          console.error(
                            "theItem is undefined for itemId:",
                            item.itemId
                          );
                          return null;
                        }

                        const isUnit = theItem.unit === item.unit;
                        const isPieceUnit = theItem.pieceUnit === item.unit;
                        const isSubPieceUnit =
                          theItem.subPieceUnit === item.unit;

                        return (
                          <Tr key={index}>
                            <Td>{theItem.name}</Td>
                            <Td>{item.quantity}</Td>
                            <Td>{item.remarks}</Td>
                            <Td>{item.batch}</Td>
                            <Td>
                              {isUnit
                                ? formatToMoney(
                                    Number(theItem.sellingPrice * item.quantity)
                                  )
                                : ""}
                              {isPieceUnit
                                ? formatToMoney(
                                    Number(
                                      theItem.pieceSellingPrice * item.quantity
                                    )
                                  )
                                : ""}
                              {isSubPieceUnit
                                ? formatToMoney(
                                    Number(
                                      theItem.subPieceSellingPrice *
                                        item.quantity
                                    )
                                  )
                                : ""}
                            </Td>
                          </Tr>
                        );
                      })}
                    </Tbody>
                    <Tfoot>
                      <Th colSpan={4}>Total sale:</Th>
                      <Th>{formatToMoney(calculateTotalSale())}</Th>
                    </Tfoot>
                  </>
                )}
              </Table>
              <Flex justifyContent="center">
                {itemsAdded.length > 0 && (
                  <Button
                    colorScheme="blue"
                    onClick={onDBSubmit}
                    isLoading={isSubmitting}
                    disabled={isSubmitting}
                  >
                    Create Bill
                  </Button>
                )}
              </Flex>
            </Flex>
          </Flex>
        )}
        <Flex direction="column" overflow="auto">
          <Text
            fontSize="xl"
            color={nameColor}
            fontWeight="bold"
            textAlign="center"
            mb="12px"
          >
            Sale Orders
          </Text>
          <Table variant="simple">
            <Thead>
              <Tr>
                <Th>Order No</Th>
                <Th>Bill Amount</Th>
                <Th>Remarks</Th>
                <Th>Payment</Th>
                <Th>Dispensed</Th>
                <Th>Details</Th>
              </Tr>
            </Thead>
            <Tbody>
              {sales?.getSales.map((sale) => {
                const approved = sale.granted;
                const dispensed = sale.dispatched;
                return (
                  <Tr key={sale.id}>
                    <Td>{sale.id}</Td>
                    <Td>{formatToMoney(Number(sale.bill?.amount))}</Td>
                    <Td>{sale.details}</Td>
                    <Td>
                      {approved ? (
                        <Tag size="sm" colorScheme="green" borderRadius="full">
                          <TagLabel>Bill Cleared</TagLabel>
                        </Tag>
                      ) : (
                        <Tag size="sm" colorScheme="blue" borderRadius="full">
                          <TagLabel>Bill Pending</TagLabel>
                        </Tag>
                      )}
                    </Td>
                    <Td>
                      {dispensed ? (
                        <Badge
                          colorScheme="green"
                          variant="outline"
                          borderRadius="full"
                          px={2}
                        >
                          Yes
                        </Badge>
                      ) : (me?.role!.name === "admin" ||
                          stores?.getStores.find((store) =>
                            store.storeKeepers.find(
                              (storeKeeper) =>
                                storeKeeper.id === sale.keeper!.id
                            )
                          )?.name) &&
                        sale.granted ? (
                        <Button
                          rightIcon={<MdOutlineMoveUp />}
                          colorScheme="teal"
                          variant="outline"
                          my={1}
                          size="xs"
                          onClick={async () => {
                            const saleInitiated = await completeSale({
                              inventoryId: sale.id,
                            });
                            if (
                              saleInitiated.data?.changeInventorySoldStatus
                                .error
                            ) {
                              return toast({
                                title:
                                  saleInitiated.data?.changeInventorySoldStatus
                                    .error.message,
                                status: "error",
                                isClosable: true,
                              });
                            }
                            await reGetSales();
                            return toast({
                              title: "Dispensed items successfully!",
                              variant: "left-accent",
                              status: "success",
                              isClosable: true,
                            });
                          }}
                        >
                          Dispense
                        </Button>
                      ) : (
                        <Badge
                          colorScheme="red"
                          variant="outline"
                          borderRadius="full"
                          px={2}
                        >
                          No
                        </Badge>
                      )}
                    </Td>
                    <Td>
                      <Button
                        size="sm"
                        colorScheme="teal"
                        leftIcon={<MdOutlineMoveUp />}
                        onClick={() => printInvoice(sale as Inventory)}
                      >
                        Print Invoice
                      </Button>
                    </Td>
                  </Tr>
                );
              })}
            </Tbody>
          </Table>
        </Flex>
      </Flex>
    </Flex>
  );
};

export default Pharmacy;
