# Heal API

## Prerequisites
- <PERSON><PERSON> and Docker Compose
- Node.js 14.15.4 (if running locally)
- PostgreSQL (if running locally)
- Redis (if running locally)

## Setup Instructions

### Using Docker (Recommended)

1. Clone the repository:
```bash
git clone <repository-url>
cd heal-api
```

2. Create SSL certificates for HTTPS:
```bash
mkdir certificates
cd certificates
openssl req -x509 -newkey rsa:2048 -keyout localhost-key.pem -out localhost.pem -days 365 -nodes
cd ..
```

3. Create `.env` file in the root directory and configure your environment variables.

4. Start the application:
```bash
docker-compose up --build
```

The application will be available at:
- API: http://localhost:4000 (HTTP) and https://localhost:443 (HTTPS)
- Client: http://localhost:3000
- GraphQL Playground: http://localhost:4000/graphql

### Running Locally

1. Install dependencies:
```bash
npm install
```

2. Create SSL certificates (see step 2 above)

3. Configure environment variables

4. Start PostgreSQL and Redis

5. Build and run the application:
```bash
npm run build
npm start
```

## Development

- `npm run watch` - Watch for TypeScript changes
- `npm run dev` - Run in development mode
- `npm run build` - Build the application

## Project Structure

```
heal-api/
├── src/
│   ├── entities/        # Database entities
│   ├── resolvers/      # GraphQL resolvers
│   ├── migrations/     # Database migrations
│   ├── utils/          # Utility functions
│   └── index.ts        # Application entry point
├── certificates/       # SSL certificates
├── dist/              # Compiled JavaScript
├── Dockerfile.node    # Docker configuration
└── docker-compose.yml # Docker Compose configuration
```

## Available GraphQL Operations

Access the GraphQL Playground at http://localhost:4000/graphql to explore available operations.

## Common Issues

1. Certificate errors: Ensure SSL certificates are properly generated
2. Database connection: Check PostgreSQL credentials and connection
3. Redis connection: Verify Redis is running and accessible