// Import necessary modules and configuration
import { isAuth } from "../middleware/isAuth";
import {
  Arg,
  Mutation,
  Resolver,
  UseMiddleware,
  Query,
  InputType,
  Field,
  Ctx,
} from "type-graphql"; // Import necessary modules and configuration
import { Role } from "../entities/Role";
import { BooleanResponse } from "./user";
import { Permission } from "../entities/Permission";
import { In } from "typeorm";
import { MyContext } from "../types";
import { logError } from "../utils/utils";

@InputType()
class RoleArgs {
  @Field()
  name: string;
  @Field(() => [String], { nullable: true })
  permissions?: [string];
}

@Resolver(Role)
export class RoleResolver {
  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async addRole(
    @Arg("name") name: string,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    try {
      await Role.create({ name: name, companyId }).save();
    } catch (err) {
      logError(
        companyId,
        err.message,
        "ROLE_ADD_ERROR",
        JSON.stringify(err),
        "high",
        `Failed to add role: ${name}`,
        userId
      );
      console.error(err.message);
      return {
        status: false,
        error: { target: "general", message: err.message },
      };
    }
    return { status: true };
  }

  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async editRole(
    @Arg("id", () => String) id: string,
    @Arg("args") editArgs: RoleArgs,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    if (!editArgs.name || editArgs.name === "") {
      logError(
        companyId,
        "Empty role name",
        "ROLE_EDIT_EMPTY_NAME",
        JSON.stringify({ id, editArgs }),
        "medium",
        `Edit role failed - empty name provided for ID: ${id}`,
        userId
      );
      return {
        status: false,
        error: { target: "general", message: "name can not be empty!" },
      };
    }

    const role = await Role.findOne({
      where: { id, companyId: req.session.companyId },
    });
    if (!role) {
      logError(
        companyId,
        "Role not found",
        "ROLE_EDIT_NOT_FOUND",
        JSON.stringify({ id }),
        "medium",
        `Edit role failed - role not found: ${id}`,
        userId
      );
      return {
        status: false,
        error: { target: "general", message: "role does not exist!" },
      };
    }

    try {
      if (editArgs.permissions) {
        const perms = await Permission.find({
          where: {
            id: In(editArgs.permissions!),
            companyId: req.session.companyId,
          },
        });
        role.permissions = perms;
      }
      role.name = editArgs.name;
      await role.save();
    } catch (err) {
      logError(
        companyId,
        err.message,
        "ROLE_EDIT_ERROR",
        JSON.stringify({ error: err, id, editArgs }),
        "high",
        `Failed to edit role: ${id}`,
        userId
      );
      console.error(err.message);
      return {
        status: false,
        error: { target: "general", message: err.message },
      };
    }
    return { status: true };
  }

  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async deleteRole(
    @Arg("id", () => String) id: string,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    try {
      const result = await Role.delete({
        id,
        companyId: req.session.companyId,
      });
      if (result.affected === 0) {
        logError(
          companyId,
          "Role not found",
          "ROLE_DELETE_NOT_FOUND",
          JSON.stringify({ id }),
          "medium",
          `Delete role failed - role not found: ${id}`,
          userId
        );
      }
    } catch (err) {
      logError(
        companyId,
        err.message,
        "ROLE_DELETE_ERROR",
        JSON.stringify({ error: err, id }),
        "high",
        `Failed to delete role: ${id}`,
        userId
      );
      console.error(err.message);
      return {
        status: false,
        error: { target: "general", message: err.message },
      };
    }
    return { status: true };
  }

  @Query(() => [Role])
  @UseMiddleware(isAuth)
  getRoles(
    @Arg("sys", { nullable: true }) sys: boolean,
    @Ctx() { req }: MyContext
  ): Promise<Role[]> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    try {
      if (sys !== null && sys !== undefined)
        return Role.find({
          where: { sys, companyId },
          relations: ["users", "employees"],
        });
      return Role.find({ where: { companyId }, relations: ["users"] });
    } catch (err) {
      logError(
        companyId,
        err.message,
        "GET_ROLES_ERROR",
        JSON.stringify({ error: err, sys }),
        "high",
        "Failed to fetch roles",
        userId
      );
      throw err;
    }
  }

  @Query(() => Role, { nullable: true })
  @UseMiddleware(isAuth)
  async getRole(
    @Arg("name") name: string,
    @Ctx() { req }: MyContext
  ): Promise<Role | undefined> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    try {
      return await Role.findOne({
        where: { name: name, companyId: req.session.companyId },
        relations: ["users", "permissions"],
      });
    } catch (err) {
      logError(
        companyId,
        err.message,
        "GET_ROLE_ERROR",
        JSON.stringify({ error: err, name }),
        "high",
        `Failed to fetch role: ${name}`,
        userId
      );
      throw err;
    }
  }
}
