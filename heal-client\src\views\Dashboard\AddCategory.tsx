// Chakra imports
import {
  Box,
  Button,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Grid,
  GridItem,
  Input,
  Text,
  useColorModeValue,
  useToast,
} from "@chakra-ui/react";
import { ActionMeta, AsyncCreatableSelect } from "chakra-react-select";
import Card from "../../components/Card/Card.js";
import CardBody from "../../components/Card/CardBody.js";
import CardHeader from "../../components/Card/CardHeader.js";
import { useState } from "react";
import CategoryRow from "../../components/Tables/CategoryRow";
import {
  Category,
  useAddCategoryMutation,
  useDeleteCategoryMutation,
  useEditCategoryByNameMutation,
  useEditCategoryMutation,
  useGetTypesQuery,
} from "../../generated/graphql";
import { useHistory } from "react-router-dom";
import { useForm } from "react-hook-form";
import DeleteConfirm from "../../components/DeleteConfirmation";

interface OptionsCategory {
  value: number;
  label: string;
}

interface SubmitCategory {
  name: string;
}

const AddCategory = () => {
  const [{ data, fetching }, getTypesAsync] = useGetTypesQuery();

  const history = useHistory();

  const [error, setError] = useState("");

  const [openDelete, setopenDelete] = useState({ open: false, id: -1000000 });

  const [value, setValue] = useState<OptionsCategory | null | undefined>(null);

  const [categories, setCategories] = useState<Category[]>([]);

  const [, addCategory] = useAddCategoryMutation();

  const [, editCategory] = useEditCategoryMutation();

  const [{ fetching: loadingDelete }, deleteCategory] =
    useDeleteCategoryMutation();

  const [, editCategoryByName] = useEditCategoryByNameMutation();

  const toast = useToast({
    position: "top",
  });

  const [count, setCount] = useState(-1);
  const [isEdit, setIsEdit] = useState(false);
  const [catToEdit, setCatToEdit] = useState<Category>();

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm();

  const textColor = useColorModeValue("gray.700", "white");
  const bgForm = useColorModeValue("white", "navy.800");

  const editActivated = (name: string) => {
    const cat = categories.filter((c) => {
      return c.name === name;
    })[0];
    setIsEdit(true);
    setCatToEdit(cat);
  };

  const handleCreateOption = (inputValue: string) => {
    history.push({
      pathname: "/admin/add-type",
      state: {
        typeName: inputValue,
      },
    });
  };

  const fetchTypes: any = async (inputValue: string) => {
    if (!fetching && data) {
      console.log("inputValue is...", inputValue);

      if (inputValue)
        return data.getTypes
          .filter(
            (t) => t.name.toLowerCase().match(inputValue.toLowerCase()) !== null
          )
          .map((t) => {
            return {
              label: t.name,
              value: t.id,
            };
          });
      else if (
        inputValue === "" ||
        inputValue === undefined ||
        inputValue === null ||
        inputValue === " "
      )
        return data.getTypes.map((t) => {
          return {
            label: t.name,
            value: t.id,
          };
        });
    }
    await getTypesAsync({ requestPolicy: "network-only" });

    if (data)
      return data?.getTypes.map((t) => {
        return {
          label: t.name,
          value: t.id,
        };
      });
  };

  async function onSubmit(values: SubmitCategory) {
    console.log("submitted values: ", values);
    setError("");
    if (isEdit) {
      if (
        categories.filter((c) => {
          return c.name === values.name;
        }).length > 0
      ) {
        return toast({
          title: "This category already exists for this type!",
          variant: "left-accent",
          status: "error",
          isClosable: true,
        });
      }
      if (catToEdit && catToEdit.id < 0) {
        const { data } = await editCategoryByName({
          name: catToEdit.name,
          args: {
            name: values.name,
            type: value?.value as number,
          },
        });
        if (data?.editCategoryByName.error) {
          console.log(
            "The error cam eback: ",
            data.editCategoryByName.error.message
          );
          return setError(data?.editCategoryByName.error.message);
        } else {
          setCategories((oldCategories) => {
            const cats = oldCategories.filter((c) => {
              return c.name !== catToEdit.name;
            });
            return [
              {
                ...catToEdit,
                name: values.name,
              } as any,
              ...cats,
            ];
          });
          getTypesAsync({ requestPolicy: "network-only" });
          return toast({
            title: "Category edited successful!",
            variant: "left-accent",
            status: "success",
            isClosable: true,
          });
        }
      } else if (catToEdit && catToEdit.id >= 0) {
        const { data } = await editCategory({
          id: catToEdit.id,
          args: {
            name: values.name,
            type: value?.value as number,
          },
        });
        if (data?.editCategory.error) {
          console.log("The error cam eback: ", data.editCategory.error.message);
          return setError(data?.editCategory.error.message);
        } else {
          setCategories((oldCategories) => {
            const cats = oldCategories.filter((c) => {
              return c.name !== catToEdit.name;
            });
            return [
              {
                ...catToEdit,
                name: values.name,
              } as any,
              ...cats,
            ];
          });
          getTypesAsync({ requestPolicy: "network-only" });
          return toast({
            title: "Category edited successful!",
            variant: "left-accent",
            status: "success",
            isClosable: true,
          });
        }
      }
    } else {
      const { data } = await addCategory({
        args: {
          name: values.name,
          type: value?.value as number,
        },
      });
      if (data?.addCategory.error) {
        console.log("The error cam eback: ", data.addCategory.error.message);
        return setError(data?.addCategory.error.message);
      } else {
        reset();
        setCategories((oldCategories) => {
          return [
            {
              name: values.name,
              type: value?.label,
              id: count,
            } as any,
            ...oldCategories,
          ];
        });
        setCount((oldCount) => {
          return oldCount - 1;
        });
        getTypesAsync({ requestPolicy: "network-only" });
        return toast({
          title: "Category added successful!",
          variant: "left-accent",
          status: "success",
          isClosable: true,
        });
      }
    }
  }

  const handleChange: any = async (
    newValue: OptionsCategory,
    actionMeta: ActionMeta<{ label: string; value: number }>
  ) => {
    console.log(`action: ${actionMeta.action}`);
    console.log(`value: ${newValue}`);
    setValue(newValue);
    setIsEdit(false);
    setCatToEdit(undefined);
    if (newValue)
      setCategories(
        data?.getTypes[
          data.getTypes.findIndex((t) => {
            return t.id === newValue.value;
          })
        ].category as any
      );
    else setCategories([]);
  };

  const callDeleteCategory: any = async (id: number) => {
    console.log("delete with id: ", id);
    if (id < 0) {
      console.log("we got id: ", id);
      let type = data?.getTypes.find((t) => {
        return t.id === value?.value;
      });
      let cat = categories.find((c) => {
        return c.id === id;
      });
      let catId;
      if (type && cat)
        catId = type?.category.find((c) => {
          return c.name === cat?.name;
        })?.id;
      if (catId) {
        console.log("we used id: ", catId);
        const { error } = await deleteCategory({ id: catId });
        setopenDelete({ open: false, id: -1000000 });
        await getTypesAsync({ requestPolicy: "network-only" });
        if (error)
          toast({
            title: "Category delete failed!",
            variant: "left-accent",
            status: "error",
            isClosable: true,
          });
        else {
          let index = categories.findIndex((c) => {
            return c.id === id;
          });
          categories.splice(index, 1);
          toast({
            title: "Category delete successful!",
            variant: "left-accent",
            status: "success",
            isClosable: true,
          });
        }
        toast({
          title: "Category delete successful!",
          variant: "left-accent",
          status: "success",
          isClosable: true,
        });
      }
    } else {
      const { error } = await deleteCategory({ id });
      setopenDelete({ open: false, id: -1000000 });
      getTypesAsync({ requestPolicy: "network-only" });
      if (error)
        toast({
          title: "Category delete failed!",
          variant: "left-accent",
          status: "error",
          isClosable: true,
        });
      else {
        let index = categories.findIndex((c) => {
          return c.id === id;
        });
        categories.splice(index, 1);
        toast({
          title: "Category delete successful!",
          variant: "left-accent",
          status: "success",
          isClosable: true,
        });
      }
    }
  };

  return (
    <Flex direction="column" pt={{ base: "120px", md: "75px" }}>
      <DeleteConfirm
        loading={loadingDelete}
        item="Category"
        open={openDelete.open}
        id={openDelete.id!}
        feedback={callDeleteCategory}
        nofeedback={() => setopenDelete({ open: false, id: -1000000 })}
      />

      <Grid templateColumns={{ sm: "1fr", lg: "2fr 1.2fr" }} templateRows="1fr">
        <Box>
          <Grid
            templateColumns={{
              sm: "1fr",
              md: "1fr 1fr",
              xl: "1fr 1fr 1fr 1fr",
            }}
            templateRows={{ sm: "auto auto auto", md: "1fr auto", xl: "1fr" }}
            gap="13px"
            mb={55}
          >
            <GridItem rowSpan={2} colSpan={1}>
              <Text fontSize="lg" color={textColor} fontWeight="bold">
                {isEdit ? "Edit" : "Add"} Category of
              </Text>
            </GridItem>

            <GridItem rowSpan={2} colSpan={3}>
              <AsyncCreatableSelect
                variant="flushed"
                isClearable
                // ml={3}
                // width={100}
                name="types"
                defaultOptions={true}
                cacheOptions={false}
                allowCreateWhileLoading={false}
                onCreateOption={handleCreateOption}
                isLoading={fetching}
                loadOptions={fetchTypes}
                placeholder="Select or create type..."
                closeMenuOnSelect={true}
                onChange={handleChange}
                defaultInputValue=""
                escapeClearsValue={true}
                hideSelectedOptions={true}
                // isMulti={true}
              />
            </GridItem>
          </Grid>
          <Box>
            {value && (
              <Flex
                zIndex="2"
                direction="column"
                w="445px"
                background="transparent"
                borderRadius="15px"
                p="40px"
                mx={{ base: "100px" }}
                m={{ base: "20px", md: "auto" }}
                bg={bgForm}
              >
                <Text
                  fontSize="xl"
                  color={textColor}
                  fontWeight="bold"
                  textAlign="center"
                  mb="22px"
                >
                  {isEdit ? "EDIT CATEGORY" : "ADD NEW CATEGORY"}
                </Text>
                {error && (
                  <Text mb="10px" color="red.500" textColor="red.300">
                    {error}
                  </Text>
                )}
                <form onSubmit={handleSubmit(onSubmit as any)}>
                  <FormControl mb={5} isInvalid={errors.name as any}>
                    <FormLabel htmlFor="name">Category name</FormLabel>
                    <Input
                      variant="filled"
                      fontSize="sm"
                      ms="4px"
                      type="text"
                      placeholder="Category Name"
                      mb="4px"
                      size="lg"
                      id="name"
                      {...register("name", {
                        required: "This is required",
                      })}
                      defaultValue={
                        isEdit ? (catToEdit ? catToEdit.name : "") : ""
                      }
                    />
                    <FormErrorMessage mb="10px">
                      {errors.name && (errors.name.message as any)}
                    </FormErrorMessage>
                  </FormControl>
                  <Button
                    fontSize="14px"
                    variant="dark"
                    fontWeight="bold"
                    w="100%"
                    h="45"
                    mb="24px"
                    mt={4}
                    colorScheme="teal"
                    isLoading={isSubmitting}
                    type="submit"
                  >
                    {isEdit ? "Edit" : "Add"} Category
                  </Button>
                  {isEdit ? (
                    <Button
                      fontSize="14px"
                      // variant="dark"
                      fontWeight="bold"
                      w="100%"
                      h="45"
                      mb="24px"
                      mt={4}
                      backgroundColor={"darkkhaki"}
                      color={"white"}
                      type="reset"
                      onClick={() => {
                        setCatToEdit(undefined);
                        setIsEdit(false);
                        reset();
                      }}
                    >
                      Cancel Edit
                    </Button>
                  ) : null}
                </form>
              </Flex>
            )}
          </Box>
        </Box>

        {value && (
          <Card my={{ lg: "24px" }} me={{ lg: "24px" }}>
            <Flex direction="column">
              <CardHeader py="12px">
                <Text color={textColor} fontSize="lg" fontWeight="bold">
                  Categories
                </Text>
              </CardHeader>
              <CardBody>
                <Flex direction="column" w="100%">
                  {categories.length > 0 ? (
                    categories.map((row) => {
                      return (
                        <CategoryRow
                          callOnClick={() =>
                            setopenDelete({ open: true, id: row.id })
                          }
                          editActivated={editActivated}
                          name={row.name}
                          type={value.label}
                          key={row.id}
                        />
                      );
                    })
                  ) : (
                    <Text>No categories of this type!</Text>
                  )}
                </Flex>
              </CardBody>
            </Flex>
          </Card>
        )}
      </Grid>
    </Flex>
  );
};

export default AddCategory;
