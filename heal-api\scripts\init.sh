#!/bin/bash

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to detect OS
detect_os() {
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        OS=$NAME
    elif command_exists sw_vers; then
        OS="macOS"
    elif command_exists systeminfo; then
        OS="Windows"
    else
        OS="Unknown"
    fi
    echo $OS
}

# Function to install Docker on Ubuntu/Debian
install_docker_ubuntu() {
    echo "Installing Docker on Ubuntu/Debian..."
    sudo apt-get update
    sudo apt-get install -y ca-certificates curl gnupg
    sudo install -m 0755 -d /etc/apt/keyrings
    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /etc/apt/keyrings/docker.gpg
    sudo chmod a+r /etc/apt/keyrings/docker.gpg
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
    sudo apt-get update
    sudo apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
    sudo usermod -aG docker $USER
    echo "Docker installed successfully!"
}

# Function to install Docker on macOS
install_docker_mac() {
    echo "Installing Docker Desktop for Mac..."
    if command_exists brew; then
        brew install --cask docker
    else
        echo "Please install Docker Desktop manually from https://www.docker.com/products/docker-desktop"
        exit 1
    fi
}

# Function to install Docker on Windows
install_docker_windows() {
    echo "Please install Docker Desktop manually from https://www.docker.com/products/docker-desktop"
    echo "After installation, run this script again."
    exit 1
}

# Check and install Docker if needed
if ! command_exists docker; then
    echo "Docker not found. Installing Docker..."
    OS=$(detect_os)
    case $OS in
        "Ubuntu"|"Debian GNU/Linux")
            install_docker_ubuntu
            ;;
        "macOS")
            install_docker_mac
            ;;
        "Windows")
            install_docker_windows
            ;;
        *)
            echo "Unsupported operating system: $OS"
            exit 1
            ;;
    esac
else
    echo "Docker is already installed."
fi

# Check Docker Compose
if ! command_exists docker-compose && ! docker compose version > /dev/null 2>&1; then
    echo "Installing Docker Compose..."
    sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    sudo chmod +x /usr/local/bin/docker-compose
fi

# Check if .env exists, if not create it from example
if [ ! -f .env ]; then
    cp .env.example .env
    echo "Created .env file. Please update it with your configuration."
fi

# Generate SSL certificates if they don't exist
if [ ! -f certificates/localhost.pem ]; then
    ./scripts/generate-certs.sh
fi

# Build and start Docker containers
echo "Building and starting Docker containers..."
docker-compose up --build -d

echo "Initialization complete!"
echo "The application should now be running at:"
echo "- API: http://localhost:4000 (HTTP) and https://localhost:443 (HTTPS)"
echo "- Client: http://localhost:3000"
echo "- GraphQL Playground: http://localhost:4000/graphql"

# Check if services are running
echo -e "\nChecking service status..."
sleep 5  # Give services time to start

if curl -s http://localhost:4000 > /dev/null; then
    echo "✅ API is running"
else
    echo "❌ API is not responding"
fi

if curl -s http://localhost:3000 > /dev/null; then
    echo "✅ Client is running"
else
    echo "❌ Client is not responding"
fi

echo -e "\nTo stop the services, run: docker-compose down"
