// Chakra imports
import {
  Button,
  Flex,
  Icon,
  Table,
  Tbody,
  Text,
  Th,
  Thead,
  Tr,
  // useColorMode,
  useColorModeValue,
  useToast,
} from "@chakra-ui/react";
// Custom components
import Card from "../../components/Card/Card";
import CardBody from "../../components/Card/CardBody";
import CardHeader from "../../components/Card/CardHeader";
import ItemsTableRow from "../../components/Tables/ItemsRow";
import { FaCompressArrowsAlt, FaPlus, FaShoppingCart } from "react-icons/fa";
import { SearchBar } from "../../components/Navbars/SearchBar/SearchBar";
import {
  Item,
  useDeleteItemMutation,
  useGetInternalItemsQuery,
  useGetMerchandiseItemsQuery,
} from "../../generated/graphql";
import { Link, useHistory } from "react-router-dom";
import DeleteConfirm from "../../components/DeleteConfirmation";
import { useContext, useState } from "react";
import { MeContext } from "../../components/Wrapper";
import { RiLuggageCartFill } from "react-icons/ri";
import { MdAddChart } from "react-icons/md";
import { RiFileExcel2Fill } from "react-icons/ri";
import ExcelExport from "../../utils/ExcelExport";

function Stock() {
  const [
    { data: internalData, fetching: internalFetching },
    getInternalItemsAsync,
  ] = useGetInternalItemsQuery({
    requestPolicy: "network-only",
  });
  const me = useContext(MeContext);
  const [{ data, fetching }, getItemsAsync] = useGetMerchandiseItemsQuery({
    requestPolicy: "network-only",
  });
  const [openDelete, setopenDelete] = useState({ open: false, id: -1000000 });
  const history = useHistory();
  const [{ fetching: loadingDelete }, deleteItem] = useDeleteItemMutation();
  // const { colorMode } = useColorMode();
  const borderColor = useColorModeValue("gray.200", "gray.600");
  const textColor = useColorModeValue("gray.700", "white");
  const bgProfile = useColorModeValue("hsla(0,0%,100%,.8)", "navy.800");
  const borderProfileColor = useColorModeValue("white", "transparent");

  const toast = useToast({
    position: "top",
  });

  const callDeleteItem: any = async (id: number) => {
    console.log("delete with id: ", id);

    const { error } = await deleteItem({ id });
    setopenDelete({ open: false, id: -1000000 });
    if (error)
      toast({
        title: "Item delete failed!",
        variant: "left-accent",
        status: "error",
        isClosable: true,
      });
    else {
      await getItemsAsync({ requestPolicy: "network-only" });
      await getInternalItemsAsync({ requestPolicy: "network-only" });
      toast({
        title: "Item deleted successful!",
        variant: "left-accent",
        status: "success",
        isClosable: true,
      });
    }
  };

  return (
    <Flex direction="column" pt={{ base: "120px", md: "75px" }}>
      <DeleteConfirm
        loading={loadingDelete}
        item="Item"
        open={openDelete.open}
        id={openDelete.id!}
        feedback={callDeleteItem}
        nofeedback={() => setopenDelete({ open: false, id: -1000000 })}
      />

      <Card overflowX={{ sm: "scroll", xl: "hidden" }} pb="0px">
        <CardHeader p="0px 0px 8px 0px">
          <Flex
            direction={{ sm: "column", md: "row" }}
            mb="12px"
            maxH="330px"
            justifyContent={{ sm: "center", md: "space-between" }}
            align="center"
            backdropFilter="blur(21px)"
            boxShadow="0px 2px 5.5px rgba(0, 0, 0, 0.02)"
            border="1.5px solid"
            borderColor={borderProfileColor}
            bg={bgProfile}
            p="24px"
            borderRadius="20px"
          >
            <Flex
              align="center"
              mb={{ sm: "10px", md: "0px" }}
              direction={{ sm: "column", md: "row" }}
              w={{ sm: "100%" }}
              textAlign={{ sm: "center", md: "start" }}
            >
              <Text>
                {window.location.pathname.includes("merch")
                  ? "Merchandise"
                  : "Internal"}{" "}
                Items
              </Text>
            </Flex>
            <Flex
              direction={{ sm: "column", lg: "row" }}
              w={{ sm: "100%", md: "50%", lg: "auto" }}
            >
              <SearchBar placeholder="Search items ..." />
              <Link to={`/${me?.role!.name}/write-off`}>
                <Button p="2px" px={2} bg="transparent" variant="no-effects">
                  <Flex
                    align="center"
                    w={{ lg: "90px" }}
                    borderRadius="15px"
                    justifyContent="center"
                    py="10px"
                    cursor="pointer"
                  >
                    <Icon
                      as={FaCompressArrowsAlt}
                      color={textColor}
                      fontSize="xs"
                      mr="4px"
                    />
                    <Text fontSize="xs" color={textColor} fontWeight="bold">
                      WRITE OFF
                    </Text>
                  </Flex>
                </Button>
              </Link>
              <Link to={`/${me?.role!.name}/transfer-items`}>
                <Button p="2px" px={2} bg="transparent" variant="no-effects">
                  <Flex
                    align="center"
                    w={{ lg: "90px" }}
                    borderRadius="15px"
                    justifyContent="center"
                    py="10px"
                    cursor="pointer"
                  >
                    <Icon
                      as={FaShoppingCart}
                      color={textColor}
                      fontSize="xs"
                      mr="4px"
                    />
                    <Text fontSize="xs" color={textColor} fontWeight="bold">
                      TRANSFER
                    </Text>
                  </Flex>
                </Button>
              </Link>
              <Link to={`/${me?.role!.name}/dispatch`}>
                <Button p="2px" px={2} bg="transparent" variant="no-effects">
                  <Flex
                    align="center"
                    w={{ lg: "85px" }}
                    borderRadius="15px"
                    justifyContent="center"
                    py="10px"
                    cursor="pointer"
                  >
                    <Icon
                      as={RiLuggageCartFill}
                      color={textColor}
                      fontSize="s"
                      mr="4px"
                    />
                    <Text fontSize="xs" color={textColor} fontWeight="bold">
                      DISPATCH
                    </Text>
                  </Flex>
                </Button>
              </Link>
              <Link to={`/${me?.role!.name}/import-stock`}>
                <Button px={1} bg="transparent" variant="no-effects">
                  <Flex
                    align="center"
                    w={{ lg: "135px" }}
                    borderRadius="15px"
                    justifyContent="center"
                    py="10px"
                    cursor="pointer"
                  >
                    <Icon
                      as={MdAddChart}
                      color={textColor}
                      fontSize="s"
                      mr="4px"
                    />
                    <Text fontSize="xs" color={textColor} fontWeight="bold">
                      IMPORT STOCK
                    </Text>
                  </Flex>
                </Button>
              </Link>
              <ExcelExport
                data={
                  data && data.getMerchandiseItems.length > 0
                    ? data?.getMerchandiseItems
                    : undefined
                }
                fileName={`${
                  window.location.pathname.includes("internal")
                    ? "Internal Use"
                    : "Merchandise"
                } Items - Heal`}
              />
              <Link to={`/${me?.role!.name}/bulk-import`}>
                <Button px={1} bg="transparent" variant="no-effects">
                  <Flex
                    align="center"
                    w={{ lg: "135px" }}
                    borderRadius="15px"
                    justifyContent="center"
                    py="10px"
                    cursor="pointer"
                  >
                    <Icon
                      as={RiFileExcel2Fill}
                      color={textColor}
                      fontSize="s"
                      mr="4px"
                    />
                    <Text fontSize="xs" color={textColor} fontWeight="bold">
                      ADD ITEMS FROM EXCEL
                    </Text>
                  </Flex>
                </Button>
              </Link>
              <Link
                to={
                  window.location.pathname.includes("internal")
                    ? `/${me?.role!.name}/internal/add-item`
                    : `/${me?.role!.name}/merch/add-item`
                }
              >
                <Button p="2px" bg="transparent" variant="no-effects">
                  <Flex
                    align="center"
                    w={{ lg: "135px" }}
                    borderRadius="15px"
                    justifyContent="center"
                    py="10px"
                    cursor="pointer"
                  >
                    <Icon
                      as={FaPlus}
                      color={textColor}
                      fontSize="xs"
                      mr="4px"
                    />
                    <Text fontSize="xs" color={textColor} fontWeight="bold">
                      ADD NEW ITEM
                    </Text>
                  </Flex>
                </Button>
              </Link>
            </Flex>
          </Flex>
        </CardHeader>
        <CardBody>
          <Table variant="simple" color={textColor}>
            <Thead>
              <Tr my=".8rem" pl="0px" color="gray.400">
                <Th pl="0px" borderColor={borderColor} color="gray.400">
                  Trade Name
                </Th>
                <Th pl="0px" borderColor={borderColor} color="gray.400">
                  Generic Name
                </Th>
                <Th borderColor={borderColor} color="gray.400">
                  Item type
                </Th>
                <Th borderColor={borderColor} color="gray.400">
                  Stock
                </Th>
                <Th borderColor={borderColor}></Th>
              </Tr>
            </Thead>
            <Tbody>
              {window.location.pathname.includes("merch") &&
                !fetching &&
                data?.getMerchandiseItems &&
                data?.getMerchandiseItems.map((item, index, arr) => {
                  return (
                    <ItemsTableRow
                      callOnClick={() =>
                        setopenDelete({ open: true, id: item.id })
                      }
                      role={me?.role!.name ? me?.role!.name : "employee"}
                      history={history}
                      isLast={index === arr.length - 1 ? true : false}
                      key={item.id}
                      item={item as Item}
                    />
                  );
                })}

              {window.location.pathname.includes("internal") &&
                !internalFetching &&
                internalData?.getInternalItems &&
                internalData?.getInternalItems.map((item, index, arr) => {
                  return (
                    <ItemsTableRow
                      callOnClick={() =>
                        setopenDelete({ open: true, id: item.id })
                      }
                      role={me?.role!.name!}
                      item={item as Item}
                      history={history}
                      isLast={index === arr.length - 1 ? true : false}
                      key={item.id}
                    />
                  );
                })}
            </Tbody>
          </Table>
          {window.location.pathname.includes("merch") &&
            !fetching &&
            !data?.getMerchandiseItems && (
              <Link to="/employee/add-item">
                <Button
                  left={40}
                  m="80px"
                  p="80px"
                  bg="transparent"
                  border="1px solid lightgray"
                  borderRadius="15px"
                  minHeight={{ sm: "200px", md: "100%" }}
                >
                  <Flex
                    direction="column"
                    justifyContent="center"
                    align="center"
                  >
                    <Icon
                      as={FaPlus}
                      mb={10}
                      color={textColor}
                      fontSize="lg"
                      mr="12px"
                    />
                    <Text fontSize="lg" color={textColor} fontWeight="bold">
                      No Items In DB, Add One Now
                    </Text>
                  </Flex>
                </Button>
              </Link>
            )}
          {window.location.pathname.includes("internal") &&
            !internalFetching &&
            !internalData?.getInternalItems && (
              <Link to="/employee/internal/add-item">
                <Button
                  left={40}
                  m="80px"
                  p="80px"
                  bg="transparent"
                  border="1px solid lightgray"
                  borderRadius="15px"
                  minHeight={{ sm: "200px", md: "100%" }}
                >
                  <Flex
                    direction="column"
                    justifyContent="center"
                    align="center"
                  >
                    <Icon
                      as={FaPlus}
                      mb={10}
                      color={textColor}
                      fontSize="lg"
                      mr="12px"
                    />
                    <Text fontSize="lg" color={textColor} fontWeight="bold">
                      No Items In DB, Add One Now
                    </Text>
                  </Flex>
                </Button>
              </Link>
            )}
        </CardBody>
      </Card>
    </Flex>
  );
}

export default Stock;
