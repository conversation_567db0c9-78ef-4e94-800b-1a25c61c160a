import {
  Box,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Button,
  useColorModeValue,
} from "@chakra-ui/react";
import { Item, StoreItemStock } from "../../../generated/graphql";

const StoreStockTable = ({
  item,
  onView,
  stores,
}: {
  item: Item;
  onView: any;
  stores: StoreItemStock[];
}) => {
  const borderColor = useColorModeValue("gray.200", "gray.600");
  const textColor = useColorModeValue("gray.700", "white");

  // Group stores by storeId and calculate total stocks
  const storeTotals = stores.reduce((acc, store) => {
    const { storeId, stock, pieceStock, subPieceStock } = store;
    if (!acc[storeId]) {
      acc[storeId] = {
        ...store,
        totalStock: stock,
        totalPieceStock: pieceStock,
        totalSubPieceStock: subPieceStock,
      };
    } else {
      acc[storeId].totalStock += stock;
      acc[storeId].totalPieceStock += pieceStock;
      acc[storeId].totalSubPieceStock += subPieceStock;
    }
    return acc;
  }, {} as { [key: number]: StoreItemStock & { totalStock: number; totalPieceStock: number; totalSubPieceStock: number } });

  const groupedStores = Object.values(storeTotals);

  return (
    <Box>
      <Table variant="simple" color={textColor}>
        <Thead>
          <Tr>
            <Th borderColor={borderColor}>Store Name</Th>
            <Th borderColor={borderColor}>Stock in {item.unit}</Th>
            {item.pieceUnit && (
              <Th borderColor={borderColor}>Stock in {item.pieceUnit}</Th>
            )}
            {item.subPieceUnit && (
              <Th borderColor={borderColor}>Stock in {item.subPieceUnit}</Th>
            )}
            <Th borderColor={borderColor}></Th>
          </Tr>
        </Thead>
        <Tbody>
          {groupedStores.map((store) => (
            <Tr key={store.storeId}>
              <Td borderColor={borderColor}>{store.store.name}</Td>
              <Td borderColor={borderColor}>{store.totalStock}</Td>
              {item.pieceUnit && (
                <Td borderColor={borderColor}>{store.totalPieceStock}</Td>
              )}
              {item.subPieceUnit && (
                <Td borderColor={borderColor}>{store.totalSubPieceStock}</Td>
              )}
              <Td borderColor={borderColor}>
                <Button onClick={() => onView(store)}>View</Button>
              </Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
    </Box>
  );
};

export default StoreStockTable;
