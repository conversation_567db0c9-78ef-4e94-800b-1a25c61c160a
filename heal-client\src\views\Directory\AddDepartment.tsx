import React, { useState } from "react";
import { useForm } from "react-hook-form";
import {
  Flex,
  Button,
  FormControl,
  FormLabel,
  Input,
  Text,
  useColorModeValue,
  FormErrorMessage,
  useToast,
  Grid,
  GridItem,
  Select,
  Box,
  Icon,
} from "@chakra-ui/react";
import {
  Department,
  useAddDepartmentMutation,
  useGetEmployeesQuery,
} from "../../generated/graphql";
import { HSeparator } from "../../components/Separator/Separator";
import { useHistory, useLocation } from "react-router-dom";
import { FaBuilding } from "react-icons/fa";

interface IDepartmentInput {
  name: string;
  description: string;
  status: string;
  type: string;
  headOfDepartmentId: string | null;
}

interface LocationState {
  department: Department;
}

const AddDepartment: React.FC = () => {
  const toast = useToast({
    position: "top",
  });
  const history = useHistory();

  const location = useLocation<LocationState>();

  // Load employees to populate the Head of Department dropdown
  const [{ data: employeesData }] = useGetEmployeesQuery();

  // Mutation for adding a department
  const [, addDepartment] = useAddDepartmentMutation();

  const [error, setError] = useState("");
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<IDepartmentInput>();

  const onSubmit = async (values: IDepartmentInput) => {
    setError("");
    const params = {
      name: values.name,
      description: values.description,
      status: values.status,
      type: values.type,
      headOfDepartmentId: values.headOfDepartmentId
        ? parseInt(values.headOfDepartmentId)
        : null,
      parentId:
        location &&
        location.state &&
        location.state.department &&
        location.state.department?.id > 0
          ? location.state.department?.id
          : null,
    };

    const { data } = await addDepartment({ params });

    if (data?.addDepartment.status) {
      toast({
        title: "Department added successfully!",
        variant: "left-accent",
        status: "success",
        isClosable: true,
      });
      reset();
      history.push("departments"); // Redirect to department list page
    } else {
      setError(data?.addDepartment.error?.message || "An error occurred.");
      toast({
        title: "Error adding department",
        description: data?.addDepartment.error?.message,
        variant: "left-accent",
        status: "error",
        isClosable: true,
      });
    }
  };

  const textColor = useColorModeValue("gray.700", "white");
  const bgForm = useColorModeValue("white", "navy.800");
  const borderColor = useColorModeValue("gray.200", "gray.600");
  const bgColor = useColorModeValue("white", "gray.700");

  return (
    <Flex
      w="100%"
      h="100%"
      alignItems="center"
      justifyContent="center"
      mb="60px"
      mt={{ base: "50px", md: "50px" }}
    >
      <Flex
        zIndex="2"
        direction="column"
        w="80%"
        background="transparent"
        borderRadius="15px"
        p="40px"
        mx={{ base: "100px" }}
        m={{ base: "20px", md: "auto" }}
        bg={bgForm}
        boxShadow={useColorModeValue(
          "0px 5px 14px rgba(0, 0, 0, 0.05)",
          "unset"
        )}
      >
        {" "}
        {location &&
          location.state &&
          location.state.department &&
          location.state.department.id > 0 && (
            <Box
              p={5}
              bg={bgColor}
              borderRadius="lg"
              boxShadow="md"
              border="1px solid"
              borderColor={borderColor}
              mb={5}
            >
              <Grid templateColumns="repeat(3, 1fr)" gap={6}>
                <GridItem>
                  <Text fontSize="2xl" fontWeight="bold" color={textColor}>
                    <Icon as={FaBuilding} mr={2} />
                    Department Details
                  </Text>
                </GridItem>
                <GridItem>
                  <Text fontSize="lg" fontWeight="bold" color={textColor}>
                    <Box as="span" minWidth="120px" display="inline-block">
                      Name:
                    </Box>
                    {location.state.department.name}
                  </Text>
                  <Text fontSize="md" color={textColor}>
                    <Box as="span" minWidth="120px" display="inline-block">
                      Description:
                    </Box>
                    {location.state.department.description}
                  </Text>
                </GridItem>
              </Grid>
            </Box>
          )}
        <Text
          fontSize="xl"
          color={textColor}
          fontWeight="bold"
          textAlign="center"
          mb="12px"
        >
          Add{" "}
          {location &&
            location.state &&
            location.state.department &&
            location.state.department?.id > 0 &&
            "Sub "}{" "}
          Department
        </Text>
        <HSeparator mt="3px" mb="3px" />
        <Text textAlign="center" mt="0px" mb="0px">
          {location &&
            location.state &&
            location.state.department &&
            location.state.department?.id > 0 &&
            "Sub "}{" "}
          Department Details:
        </Text>
        <HSeparator mb="3px" mt="3px" />
        {error && (
          <Text pb="15px" m={"auto"} color="red.500" textColor="red.300">
            {error}
          </Text>
        )}
        <form onSubmit={handleSubmit(onSubmit)}>
          <Grid templateColumns="repeat(6, 1fr)" gap={6}>
            {/* Department Name */}
            <FormControl
              as={GridItem}
              colSpan={6}
              isInvalid={errors.name as any}
            >
              <FormLabel htmlFor="name" fontSize="xs">
                {location &&
                  location.state &&
                  location.state.department &&
                  location.state.department?.id > 0 &&
                  "Sub "}{" "}
                Department Name
              </FormLabel>
              <Input
                variant="filled"
                type="text"
                fontSize="xs"
                placeholder="Department Name"
                size="lg"
                id="name"
                {...register("name", {
                  required: "Department name is required",
                })}
              />
              <FormErrorMessage fontSize="xs">
                {errors.name && (errors.name.message as any)}
              </FormErrorMessage>
            </FormControl>

            {/* Description */}
            <FormControl
              as={GridItem}
              colSpan={6}
              isInvalid={errors.description as any}
            >
              <FormLabel htmlFor="description" fontSize="xs">
                {location &&
                  location.state &&
                  location.state.department &&
                  location.state.department?.id > 0 &&
                  "Sub "}{" "}
                Department Description
              </FormLabel>
              <Input
                variant="filled"
                type="text"
                fontSize="xs"
                placeholder="Department Description"
                size="lg"
                id="description"
                {...register("description", {
                  required: "Description is required",
                })}
              />
              <FormErrorMessage fontSize="xs">
                {errors.description && (errors.description.message as any)}
              </FormErrorMessage>
            </FormControl>

            {/* Status */}
            <FormControl
              as={GridItem}
              colSpan={6}
              isInvalid={errors.status as any}
            >
              <FormLabel htmlFor="status" fontSize="xs">
                {location &&
                  location.state &&
                  location.state.department &&
                  location.state.department?.id > 0 &&
                  "Sub "}{" "}
                Department Status
              </FormLabel>
              <Select
                id="status"
                variant="filled"
                {...register("status", {
                  required: "Status is required",
                })}
              >
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </Select>
              <FormErrorMessage fontSize="xs">
                {errors.status && (errors.status.message as any)}
              </FormErrorMessage>
            </FormControl>

            {/* Department Type */}
            {location &&
            location.state &&
            location.state.department &&
            location.state.department?.id > 0 ? undefined : (
              <FormControl
                as={GridItem}
                colSpan={6}
                isInvalid={errors.type as any}
              >
                <FormLabel htmlFor="type" fontSize="xs">
                  Department Type
                </FormLabel>
                <Select
                  id="type"
                  variant="filled"
                  {...register("type", {
                    required: "Type is required",
                  })}
                >
                  <option value="administration">Administration</option>
                  <option value="clinical">Clinical</option>
                  <option value="support">Support</option>
                </Select>
                <FormErrorMessage fontSize="xs">
                  {errors.type && (errors.type.message as any)}
                </FormErrorMessage>
              </FormControl>
            )}

            {/* Head of Department */}
            <FormControl
              as={GridItem}
              colSpan={6}
              isInvalid={errors.headOfDepartmentId as any}
            >
              <FormLabel htmlFor="headOfDepartmentId" fontSize="xs">
                Head of{" "}
                {location &&
                  location.state &&
                  location.state.department &&
                  location.state.department?.id > 0 &&
                  "Sub "}{" "}
                Department (Optional)
              </FormLabel>
              <Select
                id="headOfDepartmentId"
                variant="filled"
                placeholder="Select Head of Department"
                {...register("headOfDepartmentId")}
              >
                {employeesData?.getEmployees
                  .filter((emp) => emp.role.name === "employee")
                  .map((employee) => (
                    <option key={employee.id} value={employee.employee?.id}>
                      {employee.firstname} {employee.lastname}
                    </option>
                  ))}
              </Select>
              <FormErrorMessage fontSize="xs">
                {errors.headOfDepartmentId &&
                  (errors.headOfDepartmentId.message as any)}
              </FormErrorMessage>
            </FormControl>
          </Grid>

          <Button
            mt="6"
            colorScheme="teal"
            isLoading={isSubmitting}
            type="submit"
            w="full"
          >
            Add{" "}
            {location &&
              location.state &&
              location.state.department &&
              location.state.department?.id > 0 &&
              "Sub "}{" "}
            Department
          </Button>
        </form>
      </Flex>
    </Flex>
  );
};

export default AddDepartment;
