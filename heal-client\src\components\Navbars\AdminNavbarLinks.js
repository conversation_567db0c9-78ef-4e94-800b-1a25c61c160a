/* eslint-disable react-hooks/rules-of-hooks */
import {
  Avatar,
  Button,
  Center,
  Flex,
  Icon,
  Menu,
  MenuButton,
  MenuDivider,
  MenuItem,
  MenuList,
  Stack,
  Text,
  VStack,
  WrapItem,
  useColorMode,
  useColorModeValue,
} from "@chakra-ui/react";
import avatar1 from "../../assets/img/avatars/avatar1.png";
import { ArgonLogoDark, ArgonLogoLight, SettingsIcon } from "../Icons/Icons";
import { SearchBar } from "./SearchBar/SearchBar";
import { SidebarResponsive } from "../Sidebar/Sidebar";
import routes from "../../routes";
import { useContext } from "react";
import { MeContext } from "../Wrapper";
import { useLogoutMutation } from "../../generated/graphql";
import {
  IoMdPower,
  IoMdPerson,
  IoIosCog,
  IoMdNotificationsOutline,
} from "react-icons/io";
import { GiCheckedShield } from "react-icons/gi";
import { MdOutlineMessage } from "react-icons/md";

const HeaderLinks = (props) => {
  const { variant, children, fixed, scrolled, secondary, onOpen, ...rest } =
    props;

  const [{ fetching: loadingLogout }, logout] = useLogoutMutation();

  const { colorMode } = useColorMode();

  const me = useContext(MeContext);

  const logoutHandler = () => {
    logout();
  };

  let navbarIcon =
    fixed && scrolled
      ? useColorModeValue("gray.700", "gray.200")
      : useColorModeValue("white", "gray.200");
  if (secondary) {
    navbarIcon = "white";
  }

  return (
    <Flex
      pe={{ sm: "0px", md: "16px" }}
      w={{ sm: "100%", md: "auto" }}
      alignItems="center"
      flexDirection="row"
    >
      <SearchBar me="18px" />
      <Flex alignItems={"center"} me={{ sm: "2px", md: "16px" }}>
        <Stack direction={"row"} spacing={7}>
          <Menu>
            <MenuButton
              as={Button}
              rounded={"full"}
              variant={"link"}
              cursor={"pointer"}
              minW={0}
              isLoading={loadingLogout}
            >
              <WrapItem>
                <Avatar
                  size="sm"
                  name={`${me?.firstname} ${me?.lastname}`}
                  src={avatar1}
                />{" "}
              </WrapItem>
            </MenuButton>
            <MenuList
              backgroundColor={"blue.400"}
              alignItems={"center"}
              zIndex={5}
            >
              <br />
              <Center>
                <WrapItem>
                  <Avatar
                    size="xl"
                    name={`${me?.firstname} ${me?.lastname}`}
                    src={avatar1}
                  />{" "}
                </WrapItem>
              </Center>
              <br />
              <VStack>
                <Text>{`${me?.firstname} ${me?.lastname}`}</Text>
                <Text>{me?.role.name}</Text>
              </VStack>
              <br />
              <MenuDivider />
              <MenuItem backgroundColor={"inherit"}>
                <Icon as={IoMdNotificationsOutline} mr={3} />
                Notifications
              </MenuItem>
              <MenuItem backgroundColor={"inherit"}>
                <Icon as={GiCheckedShield} mr={3} />
                Requests
              </MenuItem>
              <MenuItem backgroundColor={"inherit"}>
                <Icon as={MdOutlineMessage} mr={3} />
                Messages
              </MenuItem>
              <MenuDivider />
              <MenuItem backgroundColor={"inherit"}>
                <Icon as={IoMdPerson} mr={3} />
                My Profile
              </MenuItem>
              <MenuItem backgroundColor={"inherit"}>
                <Icon as={IoIosCog} mr={3} />
                My Account Settings
              </MenuItem>
              <MenuItem backgroundColor={"inherit"} onClick={logoutHandler}>
                <Icon as={IoMdPower} mr={3} />
                Logout
              </MenuItem>
            </MenuList>
          </Menu>
        </Stack>
      </Flex>
      <SidebarResponsive
        hamburgerColor={"white"}
        logo={
          <Stack direction="row" spacing="12px" align="center" justify="center">
            {colorMode === "dark" ? (
              <ArgonLogoLight w="74px" h="27px" />
            ) : (
              <ArgonLogoDark w="74px" h="27px" />
            )}
          </Stack>
        }
        colorMode={colorMode}
        secondary={props.secondary}
        routes={routes}
        {...rest}
      />
      <SettingsIcon
        cursor="pointer"
        ms={{ base: "16px", xl: "0px" }}
        me="16px"
        onClick={props.onOpen}
        color={navbarIcon}
        w="18px"
        h="18px"
      />
    </Flex>
  );
};

export default HeaderLinks;
