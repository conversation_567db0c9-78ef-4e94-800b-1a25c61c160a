// Chakra imports
import { useEffect, useState } from "react";
// Chakra imports
import {
  Flex,
  Text,
  useColorModeValue,
  Icon,
  TableContainer,
  Table,
  Thead,
  Tr,
  Th,
  Tbody,
  Td,
  GridItem,
  Grid,
  TableCaption,
  useToast,
} from "@chakra-ui/react";
import { useTable, useExpanded } from "react-table";
import {
  ArrowDownRightCircleFillIcon,
  ArrowRightCircleFillIcon,
} from "../../components/Icons/Icons";
import { ActionMeta, AsyncSelect } from "chakra-react-select";
import {
  useGetCategoriesQuery,
  useGetCompanyQuery,
  useGetCompaniesQuery,
  useAddFeatureMutation,
} from "../../generated/graphql";
import { TableRadioGroup } from "../../components/Card/RadioCardGroup";

interface OptionsFeatures {
  value: string;
  label: string;
}

const AddFeatures = () => {
  const toast = useToast({
    position: "top",
  });
  const [mycompany, setCompany] = useState(0);
  const [featureState, setFeatureState] = useState<any[]>([]);
  // const [features, setFeatures] = useState<string[]>([]);
  const [{ data: companies, fetching }, getCompaniesAsync] =
    useGetCompaniesQuery();
  const [{ data: company }, refetchingCompany] = useGetCompanyQuery({
    variables: { id: mycompany },
  });
  const [, AddFeature] = useAddFeatureMutation();

  // const [{ data: perms, fetching: loading }] = useGetPermissionsQuery();

  const [{ data: cats, fetching: loading }] = useGetCategoriesQuery({
    variables: { type: "company features" },
  });

  // Chakra color mode
  // const titleColor = useColorModeValue("gray.700", "blue.500");
  // const colorIcons = useColorModeValue("gray.700", "white");
  // const bgIcons = useColorModeValue("trasnparent", "navy.700");
  // const bgIconsHover = useColorModeValue("gray.50", "whiteAlpha.100");

  const columns = [
    {
      // Build our expander column
      id: "expander", // Make sure it has an ID
      Header: ({ getToggleAllRowsExpandedProps, isAllRowsExpanded }: any) => (
        <span {...getToggleAllRowsExpandedProps()}>
          {isAllRowsExpanded ? (
            <Icon as={ArrowDownRightCircleFillIcon} fontSize={25} />
          ) : (
            <Icon as={ArrowRightCircleFillIcon} fontSize={25} />
          )}
        </span>
      ),
      Cell: ({ row }: any) =>
        // Use the row.canExpand and row.getToggleRowExpandedProps prop getter
        // to build the toggle for expanding a row
        row.canExpand ? (
          <span
            {...row.getToggleRowExpandedProps({
              style: {
                // We can even use the row.depth property
                // and paddingLeft to indicate the depth
                // of the row
                paddingLeft: `${row.depth * 2}rem`,
              },
            })}
          >
            {row.isExpanded ? (
              <Icon as={ArrowDownRightCircleFillIcon} fontSize={25} />
            ) : (
              <Icon as={ArrowRightCircleFillIcon} fontSize={25} />
            )}
          </span>
        ) : null,
    },
    {
      Header: "FEATURE",
      accessor: "feature",
    },
    {
      Header: "ACTIONS",
      accessor: "permission",
    },
  ];

  const changeState: any = async (newFeature: string, feature: string) => {
    console.log("mycompany is: ", mycompany);
    console.log("company is: ", company);
    if (mycompany && mycompany !== 0 && company && company.getCompany) {
      const { data } = await AddFeature({
        name: feature + ">" + newFeature,
        companyId: company?.getCompany?.id,
      });
      if (data?.addFeature.error) {
        return toast({
          title: "Feature change failed!",
          variant: "left-accent",
          status: "error",
          isClosable: true,
        });
      }
      // setFeatures((oldPerms) => {
      //   let perms: string[] = [];
      //   let index = oldPerms.findIndex((p) => p.match(feature + ">"));
      //   if (index === 0 || index > 0) {
      //     oldPerms.forEach((op, i) => {
      //       if (i === index) {
      //         return perms.push(feature + ">" + newPermission);
      //       } else return perms.push(op);
      //     });
      //   } else {
      //     perms = oldPerms;
      //     perms.push(feature + ">" + newPermission);
      //   }
      //   if (cats)
      //     setFeatureState(
      //       convertPermissionsIntoUIArray(cats.getCategories, perms)
      //     );
      //   return perms;
      // });
      return toast({
        title: "Permission set successful!",
        variant: "left-accent",
        status: "success",
        isClosable: true,
      });
    } else
      return toast({
        title: "Permission change company set!",
        variant: "left-accent",
        status: "warning",
        isClosable: true,
      });
  };

  useEffect(() => {
    console.log("company is: ", company);
    if (cats) {
      if (company && mycompany) {
        let perms: any[] = [];
        if (
          company.getCompany?.features &&
          company.getCompany?.features.length > 0
        ) {
          perms = company.getCompany?.features.map((perm) => {
            return perm.name;
          });
          // setFeatures(perms);
        }
        setFeatureState(convertFeaturesIntoUIArray(cats.getCategories, perms));
      } else {
        setFeatureState(convertFeaturesIntoUIArray(cats.getCategories, []));
      }
    } else setFeatureState([]);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [cats, company]);

  const textColor = useColorModeValue("gray.700", "white");

  const fetchCompanies: any = async (inputValue: string) => {
    if (!fetching && companies) {
      if (inputValue)
        return companies.getCompanies
          .filter(
            (t) => t.name.toLowerCase().match(inputValue.toLowerCase()) !== null
          )
          .map((t) => {
            return {
              label: t.name,
              value: t.id,
            };
          });
      else if (
        inputValue === "" ||
        inputValue === undefined ||
        inputValue === null ||
        inputValue === " "
      )
        return companies.getCompanies.map((t) => {
          return {
            label: t.name,
            value: t.id,
          };
        });
    }
    await getCompaniesAsync({ requestPolicy: "network-only" });

    if (companies)
      return companies?.getCompanies.map((t) => {
        return {
          label: t.name,
          value: t.id,
        };
      });
  };

  const handleChange: any = async (
    newValue: OptionsFeatures,
    actionMeta: ActionMeta<{ label: string; value: number }>
  ) => {
    if (actionMeta.action !== "clear") {
      if (newValue) {
        setCompany(Number(newValue.value));
        await refetchingCompany();
      } else {
        setCompany(0);
      }
    } else {
      setCompany(0);
    }
  };

  return (
    <Flex direction="column" pt={{ base: "120px", md: "75px" }}>
      <Grid templateColumns="repeat(5, 1fr)" templateRows="1fr" columnGap={5}>
        <GridItem rowSpan={2} colSpan={2}>
          <Text fontSize="lg" color={textColor} fontWeight="bold">
            Features for
          </Text>
        </GridItem>

        <GridItem rowSpan={2} colSpan={3}>
          <AsyncSelect
            variant="flushed"
            isClearable
            // ml={3}
            // width={100}
            name="companies"
            defaultOptions={true}
            cacheOptions={false}
            isLoading={fetching}
            loadOptions={fetchCompanies}
            placeholder="Select company"
            closeMenuOnSelect={true}
            onChange={handleChange}
            escapeClearsValue={true}
            hideSelectedOptions={true}
            // isMulti={true}
          />
        </GridItem>
        <TableContainer as={GridItem} colSpan={5} mt={10}>
          {!loading && mycompany > 0 && (
            <FeaturesTable
              columns={columns}
              data={featureState}
              changePermissionState={changeState}
            />
          )}
        </TableContainer>
      </Grid>
    </Flex>
  );
};

export default AddFeatures;

function FeaturesTable({
  columns: featuresColumns,
  data,
  changePermissionState,
}: any) {
  const { getTableProps, getTableBodyProps, headerGroups, rows, prepareRow } =
    useTable(
      {
        autoResetHiddenColumns: false,
        columns: featuresColumns,
        data,
      },
      useExpanded // Use the useExpanded plugin hook
    );

  return (
    <Table colorScheme="cyan" {...getTableProps()}>
      <TableCaption>Company features setup.</TableCaption>
      <Thead>
        {headerGroups.map((headerGroup) => (
          <Tr {...headerGroup.getHeaderGroupProps()}>
            {headerGroup.headers.map((column) => (
              <Th {...column.getHeaderProps()}>{column.render("Header")}</Th>
            ))}
          </Tr>
        ))}
      </Thead>
      <Tbody {...getTableBodyProps()}>
        {" "}
        {rows.map((row) => {
          prepareRow(row);
          return (
            <Tr
              {...row.getRowProps()}
              bgColor={
                row.id.split(".").length === 2
                  ? "blue.500"
                  : row.id.split(".").length === 3
                  ? "cyan.600"
                  : row.id.split(".").length === 4
                  ? "teal"
                  : "none"
              }
            >
              {row.cells.map((cell) => {
                return (
                  <Td {...cell.getCellProps()}>
                    {["disable", "enable"].includes(cell.value) ? (
                      <TableRadioGroup
                        value={cell.value}
                        options={["disable", "enable"]}
                        onValueChange={(value) =>
                          changePermissionState(value, row.cells[1].value)
                        }
                      />
                    ) : (
                      cell.render("Cell")
                    )}
                  </Td>
                );
              })}
            </Tr>
          );
        })}
      </Tbody>
    </Table>
  );
}

function convertFeaturesIntoUIArray(arr: any[], perms: any[]) {
  let result: any[] = [];

  arr.forEach((item: { name: string }) => {
    const features = item.name.split(">")[0].split("::");

    let current = result;
    let currentMainModule = "";
    let currentMainModuleIndex = -1;
    let currentSL1 = "";
    let currentSL1Index = -1;
    let currentSL2 = "";
    let currentSL2Index = -1;

    features.forEach((feature: string, index: number) => {
      if (index === 0) {
        const existingModule = current.find((m) => m.feature === feature);
        if (!existingModule) {
          const newModule = {
            feature: feature,
            permission: "disable",
            subRows: [],
          };
          current.push(newModule);
        }
        currentMainModule = feature;
      } else if (index === 1) {
        currentMainModuleIndex = current.findIndex(
          (c) => c.feature === currentMainModule
        );
        const existingL1SubModule = current[
          currentMainModuleIndex
        ].subRows.find((m: { feature: any }) => m.feature === feature);
        if (!existingL1SubModule) {
          const newModule = {
            feature: feature,
            permission: "disable",
            subRows: [],
          };
          current[currentMainModuleIndex].subRows.push(newModule);
        }
        currentSL1 = feature;
      } else if (index === 2) {
        currentSL1Index = current[currentMainModuleIndex].subRows.findIndex(
          (c: { feature: string }) => c.feature === currentSL1
        );
        const existingL2SubModule = current[currentMainModuleIndex].subRows[
          currentSL1Index
        ].subRows.find((m: { feature: any }) => m.feature === feature);
        if (!existingL2SubModule) {
          const newModule = {
            feature: feature,
            permission: "disable",
            subRows: [],
          };
          current[currentMainModuleIndex].subRows[currentSL1Index].subRows.push(
            newModule
          );
        }
        currentSL2 = feature;
      } else if (index === 3) {
        currentSL2Index = current[currentMainModuleIndex].subRows[
          currentSL1Index
        ].subRows.findIndex(
          (c: { feature: string }) => c.feature === currentSL2
        );
        const existingL3SubModule = current[currentMainModuleIndex].subRows[
          currentSL1Index
        ].subRows[currentSL2Index].subRows.find(
          (m: { feature: any }) => m.feature === feature
        );
        if (!existingL3SubModule) {
          const newModule = {
            feature: feature,
            permission: "disable",
            subRows: [],
          };
          current[currentMainModuleIndex].subRows[currentSL1Index].subRows[
            currentSL2Index
          ].subRows.push(newModule);
        }
      }
    });
    result = current;
  });

  perms.forEach((p) => {
    let pid = result.findIndex((r) => {
      return r.feature === p.split(">")[0];
    });
    if (pid >= 0) result[pid].permission = p.split(">")[1];
    else {
      result.forEach((res1, i1) => {
        if (res1.subRows.length > 0) {
          let pid1 = res1.subRows.findIndex(
            (s1: { feature: string; permission: string; subRows: any[] }) => {
              return s1.feature === p.split(">")[0];
            }
          );
          if (pid1 >= 0) result[i1].subRows[pid1].permission = p.split(">")[1];
          else {
            res1.subRows.forEach((res2: { subRows: any[] }, i2: any) => {
              if (res2.subRows.length > 0) {
                let pid2 = res2.subRows.findIndex(
                  (s2: {
                    feature: string;
                    permission: string;
                    subRows: any[];
                  }) => {
                    return s2.feature === p.split(">")[0];
                  }
                );
                if (pid2 >= 0)
                  result[i1].subRows[i2].subRows[pid2].permission =
                    p.split(">")[1];
                else {
                  res2.subRows.forEach((res3: { subRows: any[] }, i3: any) => {
                    if (res3.subRows.length > 0) {
                      let pid3 = res3.subRows.findIndex(
                        (s3: {
                          feature: string;
                          permission: string;
                          subRows: any[];
                        }) => {
                          return s3.feature === p.split(">")[0];
                        }
                      );
                      if (pid3 >= 0)
                        result[i1].subRows[i2].subRows[i3].subRows[
                          pid3
                        ].permission = p.split(">")[1];
                    }
                  });
                }
              }
            });
          }
        }
      });
    }
  });

  return result;
}
