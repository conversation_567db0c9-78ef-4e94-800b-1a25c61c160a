// Chakra imports
import {
  Avatar,
  Badge,
  Box,
  Button,
  Flex,
  Icon,
  Image,
  Stack,
  Text,
  useColorMode,
  useColorModeValue,
} from "@chakra-ui/react";

import BackgroundCard1 from "../../assets/img/BackgroundCard1.png";
import { Store } from "../../generated/graphql";
import { PrimaryStoreIcon, SecondaryStoreIcon } from "../Icons/Icons";
import { EditIcon } from "@chakra-ui/icons";
import { Link, useHistory } from "react-router-dom";
import { MeContext } from "../Wrapper";
import { useContext } from "react";

type IStoreListCardProps = React.InputHTMLAttributes<HTMLInputElement> & {
  data: Store;
};

export const StoreListCard: React.FC<IStoreListCardProps> = ({ data }) => {
  const { colorMode } = useColorMode();
  const textColor = useColorModeValue("gray.700", "white");
  const history = useHistory();
  const me = useContext(MeContext);
  return (
    <Flex direction="column">
      <Link to={{ pathname: "/admin/view-store", state: { store: data } }}>
        <Box mb="20px" position="relative" borderRadius="15px">
          <Image src={BackgroundCard1} borderRadius="15px" />
          <Flex mt={-110} justify={"center"}>
            <Avatar
              size={"xl"}
              css={{
                border: "2px solid white",
              }}
              bg={colorMode === "dark" ? "navy.900" : "#fff"}
              icon={
                <Icon
                  color={colorMode === "dark" ? "cyan" : "coral"}
                  as={data.primary ? PrimaryStoreIcon : SecondaryStoreIcon}
                  fontSize={100}
                />
              }
            />
          </Flex>
          <Box
            w="100%"
            h="100%"
            position="absolute"
            top="0"
            borderRadius="15px"
            bg="linear-gradient(360deg, rgba(49, 56, 96, 0.16) 0%, rgba(21, 25, 40, 0.88) 100%)"
          ></Box>
        </Box>
      </Link>
      <Flex direction="column">
        <Flex direction={"row"}>
          <Flex
            align="center"
            w={{ sm: "100%", lg: "170px" }}
            bg={colorMode === "dark" ? "navy.900" : "#fff"}
            borderRadius="8px"
            justifyContent="center"
            py="10px"
            mt={5}
            boxShadow="0px 2px 5.5px rgba(0, 0, 0, 0.06)"
          >
            <Icon
              color={colorMode === "dark" ? "cyan" : "coral"}
              as={data.primary ? PrimaryStoreIcon : SecondaryStoreIcon}
              me="6px"
            />
            <Text
              fontSize="md"
              color={colorMode === "dark" ? "cyan" : "coral"}
              fontWeight="bold"
            >
              {data.primary ? "Primary Store" : "Secondary Store"}
            </Text>
          </Flex>
          <Button
            mr={0}
            ml={20}
            mt={5}
            rightIcon={<EditIcon />}
            colorScheme="teal"
            variant="outline"
            onClick={() =>
              history.push({
                pathname: `/${me?.role!.name}/edit-store`,
                state: data,
              })
            }
          >
            Edit
          </Button>
        </Flex>

        <Text
          fontSize="xl"
          color={textColor}
          fontWeight="bold"
          mb="10px"
          mt={5}
        >
          Name: {data.name ? data.name : "Name of store"}
        </Text>
        <Flex direction="column">
          <Flex align="center" mb="18px">
            <Text fontSize="md" color={textColor} fontWeight="bold" me="10px">
              Details:{" "}
            </Text>
            <Stack direction="row">
              <Badge variant="outline" colorScheme="green">
                Open
              </Badge>
              <Badge variant="subtle" colorScheme="green">
                {data.address}
              </Badge>
              <Badge variant="solid" colorScheme="green">
                Store Keepers:{" "}
                {(data && data.storeKeepers && data.storeKeepers.length) || 0}
              </Badge>
            </Stack>
          </Flex>
        </Flex>
      </Flex>
    </Flex>
  );
};
