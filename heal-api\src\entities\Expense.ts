import { Field, ObjectType } from "type-graphql";
import { Column, <PERSON>tity, Join<PERSON><PERSON>umn, ManyToOne } from "typeorm";
import { AuditBaseEntity } from "./AuditEntity";
import { Employee } from "./Employee";

@ObjectType()
@Entity()
export class Expense extends AuditBaseEntity {
  @Field(() => String)
  @Column({ type: "timestamp" })
  expenseDate: Date;

  @Field(() => String, { nullable: false })
  @Column({ type: "uuid", nullable: true })
  authorizerId: string;

  @Field(() => Employee, { nullable: true })
  @ManyToOne(() => Employee, (employee) => employee.authorizedExpenses, {
    nullable: true,
  })
  @JoinColumn([
    { name: "authorizerId", referencedColumnName: "id" },
    { name: "companyId", referencedColumnName: "companyId" },
  ])
  authorizer: Employee;

  @Field(() => String)
  @Column({ type: "uuid" })
  requesterId: string;

  @Field(() => Employee, { nullable: true })
  @ManyToOne(() => Employee, (employee) => employee.requestedExpenses)
  @JoinColumn([
    { name: "requesterId", referencedColumnName: "id" },
    { name: "companyId", referencedColumnName: "companyId" },
  ])
  requester: Employee;

  @Field(() => String, { nullable: true })
  @Column({ type: "uuid", nullable: true })
  assetId: string;

  @Field(() => String)
  @Column({
    type: "enum",
    enum: ["office", "employee", "other"], //enum in postgres is case sensitive so mind the caps
    default: "other",
  })
  assetType: string;

  @Field(() => String)
  @Column({
    type: "enum",
    enum: ["debit", "credit"], //enum in postgres is case sensitive so mind the caps
    default: "credit",
  })
  type!: string;

  @Field(() => String)
  @Column({
    type: "enum",
    enum: ["requested", "approved"], //enum in postgres is case sensitive so mind the caps
    default: "requested",
  })
  status!: string;

  @Field()
  @Column({ type: "text" })
  title: string;

  @Field()
  @Column({ type: "text" })
  details: string;

  @Field()
  @Column({ type: "bigint" })
  amount: number;
}
