// @ts-nocheck

import { Schema } from "@powersync/node";

// Common columns from AuditBaseEntity
const auditColumns = [
  { name: "id", type: "integer" },
  { name: "companyId", type: "integer" },
  { name: "createdAt", type: "text" },
  { name: "updatedAt", type: "text" },
  { name: "deleted", type: "integer" },
];

export const AppSchema = new Schema([
  // AuditBaseEntity (added as a table)
  {
    table: "AuditBaseEntity",
    columns: [
      { name: "id", type: "integer" },
      { name: "companyId", type: "integer" },
      { name: "createdAt", type: "text" },
      { name: "updatedAt", type: "text" },
      { name: "deleted", type: "integer" },
    ],
  },
  // Company
  {
    table: "Company",
    columns: [
      { name: "id", type: "integer" },
      { name: "companyId", type: "integer" },
      { name: "createdAt", type: "text" },
      { name: "updatedAt", type: "text" },
      { name: "deleted", type: "integer" },
      { name: "isParent", type: "integer" },
      { name: "isBranch", type: "integer" },
      { name: "parentId", type: "integer" },
      { name: "branches", type: "text" },
      { name: "name", type: "text" },
      { name: "syncUrl", type: "text" },
      { name: "tinNumber", type: "text" },
      { name: "registrationNumber", type: "text" },
      { name: "type", type: "text" },
      { name: "phone", type: "text" },
      { name: "email", type: "text" },
      { name: "poBox", type: "text" },
      { name: "logo", type: "text" },
      { name: "website", type: "text" },
      { name: "location", type: "text" },
    ],
  },
  // User
  {
    table: "User",
    columns: [
      ...auditColumns,
      { name: "firstname", type: "text" },
      { name: "middlename", type: "text" },
      { name: "lastname", type: "text" },
      { name: "email", type: "text" },
      { name: "phone", type: "text" },
      { name: "gender", type: "text" },
      { name: "dateOfBirth", type: "text" },
      { name: "image", type: "text" },
      { name: "address", type: "text" },
      { name: "roleId", type: "integer" },
      { name: "password", type: "text" },
    ],
  },
  // Employee
  {
    table: "Employee",
    columns: [
      ...auditColumns,
      { name: "status", type: "text" },
      { name: "roleId", type: "integer" },
      { name: "designation", type: "text" },
      { name: "licenceNumber", type: "text" },
      { name: "image", type: "text" },
      { name: "userId", type: "integer" },
      { name: "storeId", type: "integer" },
      { name: "departmentId", type: "integer" },
      { name: "headingDepartmentId", type: "integer" },
    ],
  },
  // Department
  {
    table: "Department",
    columns: [
      ...auditColumns,
      { name: "name", type: "text" },
      { name: "description", type: "text" },
      { name: "status", type: "text" },
      { name: "parentId", type: "integer" },
      { name: "type", type: "text" },
    ],
  },
  // Role
  {
    table: "Role",
    columns: [
      ...auditColumns,
      { name: "sys", type: "integer" },
      { name: "name", type: "text" },
    ],
  },
  // Permission
  {
    table: "Permission",
    columns: [...auditColumns, { name: "name", type: "text" }],
  },
  // Item
  {
    table: "Item",
    columns: [
      ...auditColumns,
      { name: "name", type: "text" },
      { name: "description", type: "text" },
      { name: "barcode", type: "text" },
      { name: "type", type: "text" },
      { name: "reference", type: "text" },
      { name: "reorder", type: "integer" },
      { name: "internal", type: "integer" },
      { name: "image", type: "text" },
      { name: "unit", type: "text" },
      { name: "stock", type: "real" },
      { name: "sellingPrice", type: "integer" },
    ],
  },
  // Category
  {
    table: "Category",
    columns: [
      ...auditColumns,
      { name: "name", type: "text" },
      { name: "typeId", type: "integer" },
      { name: "parentCategoryId", type: "integer" },
      { name: "type_category", type: "text" },
    ],
  },
  // Unit
  {
    table: "Unit",
    columns: [
      ...auditColumns,
      { name: "barcode", type: "text" },
      { name: "name", type: "text" },
      { name: "quantity", type: "real" },
      { name: "price", type: "integer" },
      { name: "itemId", type: "integer" },
    ],
  },
  // BatchStock
  {
    table: "BatchStock",
    columns: [
      ...auditColumns,
      { name: "itemId", type: "integer" },
      { name: "batch", type: "text" },
      { name: "expireDate", type: "text" },
      { name: "stock", type: "real" },
    ],
  },
  // Inventory
  {
    table: "Inventory",
    columns: [
      ...auditColumns,
      { name: "details", type: "text" },
      { name: "customerTag", type: "text" },
      { name: "type", type: "text" },
      { name: "granted", type: "integer" },
      { name: "received", type: "integer" },
      { name: "dispatched", type: "integer" },
      { name: "transferDate", type: "text" },
      { name: "returnDate", type: "text" },
      { name: "startDate", type: "text" },
      { name: "sourceStoreId", type: "integer" },
      { name: "destinationStoreId", type: "integer" },
      { name: "keeperId", type: "integer" },
      { name: "consumerId", type: "integer" },
      { name: "approverId", type: "integer" },
    ],
  },
  // Store
  {
    table: "Store",
    columns: [
      ...auditColumns,
      { name: "name", type: "text" },
      { name: "primary", type: "integer" },
      { name: "address", type: "text" },
    ],
  },
  // StoreItemStock
  {
    table: "StoreItemStock",
    columns: [
      ...auditColumns,
      { name: "itemId", type: "integer" },
      { name: "storeId", type: "integer" },
      { name: "stock", type: "real" },
      { name: "batchId", type: "integer" },
    ],
  },
  // Transfer
  {
    table: "Transfer",
    columns: [
      ...auditColumns,
      { name: "inventoryId", type: "integer" },
      { name: "itemId", type: "integer" },
      { name: "price", type: "integer" },
      { name: "granted", type: "integer" },
      { name: "received", type: "integer" },
      { name: "dispatched", type: "integer" },
      { name: "quantity", type: "real" },
      { name: "details", type: "text" },
      { name: "batch", type: "text" },
    ],
  },
  // Bill
  {
    table: "Bill",
    columns: [
      ...auditColumns,
      { name: "cleared", type: "integer" },
      { name: "inventoryId", type: "integer" },
      { name: "amount", type: "integer" },
      { name: "paymentType", type: "text" },
    ],
  },
  // Payment
  {
    table: "Payment",
    columns: [
      ...auditColumns,
      { name: "packageName", type: "text" },
      { name: "status", type: "text" },
      { name: "billingCycle", type: "text" },
      { name: "startDate", type: "text" },
      { name: "endDate", type: "text" },
      { name: "amount", type: "integer" },
      { name: "paymentReference", type: "text" },
      { name: "autoRenew", type: "integer" },
      { name: "maxUsers", type: "integer" },
      { name: "features", type: "text" },
      { name: "lastPaymentDate", type: "text" },
      { name: "cancellationReason", type: "text" },
    ],
  },
  // Expense
  {
    table: "Expense",
    columns: [
      ...auditColumns,
      { name: "expenseDate", type: "text" },
      { name: "authorizerId", type: "integer" },
      { name: "requesterId", type: "integer" },
      { name: "assetId", type: "integer" },
      { name: "assetType", type: "text" },
      { name: "type", type: "text" },
      { name: "status", type: "text" },
      { name: "title", type: "text" },
      { name: "details", type: "text" },
      { name: "amount", type: "integer" },
    ],
  },
  // Feature
  {
    table: "Feature",
    columns: [...auditColumns, { name: "name", type: "text" }],
  },
  // Type
  {
    table: "Type",
    columns: [
      ...auditColumns,
      { name: "name", type: "text" },
      { name: "description", type: "text" },
    ],
  },
  // Approval
  {
    table: "Approval",
    columns: [
      ...auditColumns,
      { name: "feature", type: "text" },
      { name: "type", type: "text" },
      { name: "status", type: "integer" },
      { name: "requesterId", type: "integer" },
      { name: "approverId", type: "integer" },
      { name: "approvalDate", type: "text" },
      { name: "requestId", type: "integer" },
    ],
  },
  // Import
  {
    table: "Import",
    columns: [
      ...auditColumns,
      { name: "importDate", type: "text" },
      { name: "expireDate", type: "text" },
      { name: "supplier", type: "text" },
      { name: "quantity", type: "real" },
      { name: "unit", type: "text" },
      { name: "importPrice", type: "integer" },
      { name: "sellingPrice", type: "integer" },
      { name: "receipt", type: "text" },
      { name: "itemId", type: "integer" },
      { name: "batch", type: "text" },
    ],
  },
  // SyncHistory
  {
    table: "SyncHistory",
    columns: [
      { name: "id", type: "integer" },
      { name: "entityName", type: "text" },
      { name: "direction", type: "text" },
      { name: "status", type: "text" },
      { name: "error", type: "text" },
      { name: "recordsProcessed", type: "integer" },
      { name: "lastSyncTimestamp", type: "text" },
      { name: "companyId", type: "integer" },
      { name: "createdAt", type: "text" },
      { name: "updatedAt", type: "text" },
    ],
  },
  // ErrorLog
  {
    table: "ErrorLog",
    columns: [
      ...auditColumns,
      { name: "errorMessage", type: "text" },
      { name: "errorCode", type: "text" },
      { name: "errorStackTrace", type: "text" },
      { name: "severity", type: "text" },
      { name: "userId", type: "integer" },
      { name: "action", type: "text" },
      { name: "resolved", type: "integer" },
    ],
  },
]);
