mutation registerPatient($params: RegisterPatientArgs!) {
  registerPatient(params: $params) {
    error {
      target
      message
    }
    patient {
      ...Client
    }
  }
}
mutation editPatient($id: Float!, $params: RegisterPatientArgs!) {
  editPatient(id: $id, params: $params) {
    error {
      target
      message
    }
    patient {
      ...Client
    }
  }
}

mutation addVisit($params: VisitInputArgs!) {
  addVisit(params: $params) {
    error {
      target
      message
    }
    visit {
      id
      status
      type
      reason
      consultation
      currentLocation
      clientId
      client {
        id
        firstname
        middlename
        lastname
        status
        email
        phone
        insuranceProvider
        insuranceId
        insuranceUserId
        insuranceStatus
        insuranceSchemeId
        insuranceCardNumber
        gender
        dateOfBirth
        fileNumber
        nationalId
        bloodGroup
        registererId
      }
    }
  }
}

mutation addVitals($params: VitalsInputArgs!) {
  addVitals(params: $params) {
    error {
      target
      message
    }
    visit {
      id
      status
      type
      reason
      consultation
      currentLocation
      clientId
      client {
        id
        firstname
        middlename
        lastname
        status
        email
        phone
        insuranceProvider
        insuranceId
        insuranceUserId
        insuranceStatus
        insuranceSchemeId
        insuranceCardNumber
        gender
        dateOfBirth
        fileNumber
        nationalId
        bloodGroup
        registererId
      }
      vitals {
        id
        height
        weight
        pulseRate
        bodyTemperature
        respirationRate
        oxygenSaturation
        systolicPressure
        diastolicPressure
        bloodGlucose
      }
    }
  }
}
