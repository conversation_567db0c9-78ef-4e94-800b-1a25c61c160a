import {
  Arg,
  Ctx,
  Field,
  InputType,
  Mutation,
  ObjectType,
  Query,
  Resolver,
  UseMiddleware,
} from "type-graphql";
import { Message } from "../entities/Message";
import { isAuth } from "../middleware/isAuth";
import { MyContext } from "../types";
import { FieldError } from "./user";
import { logError } from "../utils/utils";
import { Between } from "typeorm";

@InputType()
class MessageInput {
  @Field()
  senderName: string;

  @Field()
  senderEmail: string;

  @Field({ nullable: true })
  senderPhone?: string;

  @Field()
  subject: string;

  @Field()
  message: string;
}

@ObjectType()
class MessageResponse {
  @Field(() => [FieldError], { nullable: true })
  errors?: FieldError[];

  @Field(() => Message, { nullable: true })
  message?: Message;

  @Field(() => Boolean)
  status: boolean;
}

@Resolver(Message)
export class MessageResolver {
  @Mutation(() => MessageResponse)
  async receiveMessage(
    @Arg("input") input: MessageInput
  ): Promise<MessageResponse> {
    try {
      // Basic validation
      if (!input.senderEmail.includes("@")) {
        return {
          errors: [{ target: "email", message: "Invalid email format" }],
          status: false,
        };
      }

      const message = await Message.create({
        ...input,
        attended: false,
      }).save();

      return {
        message,
        status: true,
      };
    } catch (error) {
      await logError(
        "",
        error.message,
        "MESSAGE_RECEIVE_ERROR",
        JSON.stringify({ error, input }),
        "medium",
        `Failed to receive message from: ${input.senderEmail}`
      );

      return {
        errors: [{ target: "general", message: "Failed to save message" }],
        status: false,
      };
    }
  }

  @Mutation(() => MessageResponse)
  @UseMiddleware(isAuth)
  async updateMessageStatus(
    @Arg("messageId", () => String) messageId: string,
    @Arg("attended") attended: boolean
  ): Promise<MessageResponse> {
    try {
      const message = await Message.findOne({
        where: { id: messageId },
      });

      if (!message) {
        return {
          errors: [{ target: "message", message: "Message not found" }],
          status: false,
        };
      }

      message.attended = attended;
      await message.save();

      return {
        message,
        status: true,
      };
    } catch (error) {
      await logError(
        "",
        error.message,
        "MESSAGE_STATUS_UPDATE_ERROR",
        JSON.stringify({ error, messageId, attended }),
        "medium",
        `Failed to update message status: ${messageId}`,
        ""
      );

      return {
        errors: [
          { target: "general", message: "Failed to update message status" },
        ],
        status: false,
      };
    }
  }

  @Query(() => [Message])
  @UseMiddleware(isAuth)
  async getMessages(
    @Arg("attended", { nullable: true }) attended: boolean,
    @Ctx() { req }: MyContext
  ): Promise<Message[]> {
    // check if user authenticated
    if (!req.session.userId) {
      return [];
    }
    return Message.find({
      where: { attended },
      order: { createdAt: "DESC" },
    });
  }

  //query messages on specific date range
  @Query(() => [Message])
  @UseMiddleware(isAuth)
  async getAllMessages(
    @Arg("startDate") startDate: Date,
    @Arg("endDate") endDate: Date,
    @Ctx() { req }: MyContext
  ): Promise<Message[]> {
    // check if user authenticated
    if (!req.session.userId) {
      return [];
    }
    return Message.find({
      where: { createdAt: Between(startDate, endDate) },
      order: { createdAt: "DESC" },
    });
  }
}
