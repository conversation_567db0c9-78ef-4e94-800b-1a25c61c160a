import {
  Button,
  Flex,
  Grid,
  GridItem,
  Icon,
  Text,
  useColorModeValue,
} from "@chakra-ui/react";
import { FaPlus } from "react-icons/fa";
import Card from "../../components/Card/Card";
import CardBody from "../../components/Card/CardBody";
import CardHeader from "../../components/Card/CardHeader";
import { SearchBar } from "../../components/Navbars/SearchBar/SearchBar";
import { Store, useGetStoresQuery } from "../../generated/graphql";
import { Link } from "react-router-dom";
import { useContext, useEffect, useState } from "react";
import { StoreListCard } from "../../components/Card/StoreCard";
import { MeContext } from "../../components/Wrapper";

const StoresPage = () => {
  const [{ data, fetching, error }] = useGetStoresQuery({
    requestPolicy: "cache-and-network",
  });
  const me = useContext(MeContext);
  const [stores, setStores] = useState<Store[]>([]);
  const textColor = useColorModeValue("gray.700", "white");
  const bgProfile = useColorModeValue("hsla(0,0%,100%,.8)", "navy.800");
  const borderProfileColor = useColorModeValue("white", "transparent");

  useEffect(() => {
    if (data) {
      setStores(data.getStores as Store[]);
    }
  }, [data]);

  if (fetching) return <p>Loading...</p>;
  if (error) return <p>Error: {error.message}</p>;

  return (
    <Flex direction="column" pt={{ base: "120px", md: "75px" }}>
      <Card overflowX={{ sm: "scroll", xl: "hidden" }} pb="0px">
        <CardHeader p="0px 0px 8px 0px">
          <Flex
            direction={{ sm: "column", md: "row" }}
            mb="12px"
            maxH="330px"
            justifyContent={{ sm: "center", md: "space-between" }}
            align="center"
            backdropFilter="blur(21px)"
            boxShadow="0px 2px 5.5px rgba(0, 0, 0, 0.02)"
            border="1.5px solid"
            borderColor={borderProfileColor}
            bg={bgProfile}
            p="24px"
            borderRadius="20px"
          >
            <Flex
              align="center"
              mb={{ sm: "10px", md: "0px" }}
              direction={{ sm: "column", md: "row" }}
              w={{ sm: "100%" }}
              textAlign={{ sm: "center", md: "start" }}
            >
              <Text>Our Stores</Text>
            </Flex>
            <Flex
              direction={{ sm: "column", lg: "row" }}
              w={{ sm: "100%", md: "50%", lg: "auto" }}
            >
              <SearchBar placeholder="Search stores ..." />

              <Link to={`/${me?.role!.name}/add-store`}>
                <Button p="2px" bg="transparent" variant="no-effects">
                  <Flex
                    align="center"
                    w={{ lg: "135px" }}
                    borderRadius="15px"
                    justifyContent="center"
                    py="10px"
                    cursor="pointer"
                  >
                    <Icon
                      as={FaPlus}
                      color={textColor}
                      fontSize="xs"
                      mr="4px"
                    />
                    <Text fontSize="xs" color={textColor} fontWeight="bold">
                      ADD NEW STORE
                    </Text>
                  </Flex>
                </Button>
              </Link>
            </Flex>
          </Flex>
        </CardHeader>
        <CardBody px="5px">
          <Grid
            templateColumns={{ sm: "1fr", md: "1fr 1fr", xl: "repeat(4, 1fr)" }}
            templateRows={{ sm: "1fr 1fr 1fr auto", md: "1fr 1fr", xl: "1fr" }}
            gap="24px"
          >
            {stores &&
              stores.map((store) => (
                <StoreListCard key={store.id} data={store} />
              ))}
            {stores && stores.length > 0 && (
              <GridItem colSpan={1} m={0}>
                <Link to={`/${me?.role!.name}/add-store`}>
                  <Button
                    p="40px"
                    py={40}
                    bg="transparent"
                    border="1px solid lightgray"
                    borderRadius="15px"
                    minHeight={{ sm: "200px", md: "100%" }}
                  >
                    <Flex
                      direction="column"
                      justifyContent="center"
                      align="center"
                    >
                      <Icon
                        as={FaPlus}
                        mb={10}
                        color={textColor}
                        fontSize="lg"
                        mr="12px"
                      />
                      <Text fontSize="lg" color={textColor} fontWeight="bold">
                        Add more stores
                      </Text>
                    </Flex>
                  </Button>
                </Link>
              </GridItem>
            )}

            {!fetching && !stores && (
              <Link to="/stores/add">
                <Button
                  left={40}
                  m="80px"
                  p="80px"
                  bg="transparent"
                  border="1px solid lightgray"
                  borderRadius="15px"
                  minHeight={{ sm: "200px", md: "100%" }}
                >
                  <Flex
                    direction="column"
                    justifyContent="center"
                    align="center"
                  >
                    <Icon
                      as={FaPlus}
                      mb={10}
                      color={textColor}
                      fontSize="lg"
                      mr="12px"
                    />
                    <Text fontSize="lg" color={textColor} fontWeight="bold">
                      No Stores In DB, Add One Now
                    </Text>
                  </Flex>
                </Button>
              </Link>
            )}
          </Grid>
        </CardBody>
      </Card>
    </Flex>
  );
};

export default StoresPage;
