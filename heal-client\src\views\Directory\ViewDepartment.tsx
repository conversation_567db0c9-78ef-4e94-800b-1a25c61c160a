import { Link, useHistory, useLocation } from "react-router-dom";
import { FaBuilding, FaEllipsisV } from "react-icons/fa";
import {
  Department,
  useGetEmployeesQuery,
  useSetHeadOfDepartmentMutation,
} from "../../generated/graphql";
// Chakra imports
import {
  Flex,
  Icon,
  Table,
  Tbody,
  Text,
  Th,
  Thead,
  Tr,
  Td,
  Box,
  Grid,
  GridItem,
  Button,
  useColorModeValue,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  IconButton,
  useToast,
} from "@chakra-ui/react";
import { EditIcon, StarIcon, ViewIcon } from "@chakra-ui/icons";

interface LocationState {
  department: Department;
}

function ViewDepartment() {
  const toast = useToast({
    position: "top",
  });
  const location = useLocation<{ department: Department }>();
  const [, makeHeadOfDepartment] = useSetHeadOfDepartmentMutation();
  const history = useHistory();
  const { department } = location.state as LocationState;
  if (!department || !department.id || department.id <= 0)
    history.push("departments");
  const [{ data: employees }] = useGetEmployeesQuery({
    requestPolicy: "network-only",
  }); // Fetch data using the hook

  const textColor = useColorModeValue("gray.800", "white");
  const borderColor = useColorModeValue("gray.200", "gray.600");
  const bgColor = useColorModeValue("white", "gray.600");

  return (
    <Flex direction="column" pt={{ base: "120px", md: "75px" }}>
      {department && department.id > 0 && (
        <Box
          p={5}
          bg={bgColor}
          borderRadius="lg"
          boxShadow="md"
          border="1px solid"
          borderColor={borderColor}
          mb={5}
        >
          <Grid templateColumns="repeat(3, 1fr)" gap={6}>
            <GridItem>
              <Text fontSize="2xl" fontWeight="bold" color={textColor}>
                <Icon as={FaBuilding} mr={2} />
                Department Details
              </Text>
            </GridItem>
            <GridItem>
              <Text fontSize="lg" fontWeight="bold" color={textColor}>
                <Box as="span" minWidth="120px" display="inline-block">
                  Name:
                </Box>
                {department.name}
              </Text>
              <Text fontSize="md" color={textColor}>
                <Box as="span" minWidth="120px" display="inline-block">
                  Description:
                </Box>
                {department.description}
              </Text>
            </GridItem>
            <GridItem textAlign="right">
              <Button
                colorScheme="teal"
                onClick={() =>
                  history.push({
                    pathname: "add-subdepartment",
                    state: {
                      department,
                    },
                  })
                }
                mr={3}
              >
                Add Sub-department
              </Button>
              <Button
                colorScheme="blue"
                as={Link}
                to={{
                  pathname: "add-clinic",
                  state: {
                    department,
                  },
                }}
              >
                Add Clinic
              </Button>
            </GridItem>
          </Grid>
        </Box>
      )}

      {/* Employees Table */}
      <Table variant="simple" color={textColor} mb={6}>
        <Thead>
          <Tr>
            <Th pl="0px" borderColor={borderColor} color={textColor}>
              Employee Name
            </Th>
            <Th borderColor={borderColor} color={textColor}>
              Role
            </Th>
            <Th borderColor={borderColor} color={textColor}>
              Email
            </Th>
            <Th borderColor={borderColor}></Th>
          </Tr>
        </Thead>
        <Tbody>
          {department.employees.map((emp) => {
            const employee = employees?.getEmployees.find(
              (e) => e.employee && e?.employee!.id === emp.id
            );
            if (!employee) return null;
            return (
              <Tr key={emp.id}>
                <Td pl="0px" borderColor={borderColor}>
                  {department.headOfDepartment &&
                    emp.id === department.headOfDepartment.id && (
                      <StarIcon mb={1} mr={2} />
                    )}
                  {employee!.firstname + " " + employee!.lastname}
                </Td>
                <Td borderColor={borderColor}>{employee!.role.name}</Td>
                <Td borderColor={borderColor}>{employee!.email}</Td>
                <Td borderColor={borderColor}>
                  <Menu>
                    <MenuButton
                      as={IconButton}
                      aria-label="Options"
                      icon={<FaEllipsisV />}
                      variant="outline"
                    />
                    <MenuList>
                      <MenuItem icon={<EditIcon />}>Manage Employee</MenuItem>
                      <MenuItem
                        icon={<StarIcon />}
                        onClick={async () => {
                          const feedback = await makeHeadOfDepartment({
                            departmentId: department.id,
                            employeeId: emp.id,
                          });
                          if (
                            feedback.error ||
                            feedback.data?.setHeadOfDepartment.error
                          )
                            return toast({
                              title: `Operation was not a success!`,
                              variant: "left-accent",
                              status: "error",
                              isClosable: true,
                            });
                          return toast({
                            title: `Successfully set ${
                              employee.firstname + " " + employee.lastname
                            } as department head!`,
                            variant: "left-accent",
                            status: "success",
                            isClosable: true,
                          });
                        }}
                      >
                        Make Head of Department
                      </MenuItem>
                      <MenuItem icon={<ViewIcon />}>
                        View Performance Report
                      </MenuItem>
                    </MenuList>
                  </Menu>
                </Td>
              </Tr>
            );
          })}
        </Tbody>
      </Table>

      {/* Clinics Table */}
      <Table variant="simple" color={textColor} mt={10}>
        <Thead>
          <Tr>
            <Th pl="0px" borderColor={borderColor} color={textColor}>
              Clinic Name
            </Th>
            <Th borderColor={borderColor} color={textColor}>
              Clinic Type
            </Th>
            <Th borderColor={borderColor} color={textColor}>
              Size
            </Th>
            <Th borderColor={borderColor} color={textColor}>
              Status
            </Th>
            <Th borderColor={borderColor} color={textColor}></Th>
          </Tr>
        </Thead>
        <Tbody>
          {department?.clinics?.map((clinic) => (
            <Tr key={clinic.id}>
              <Td pl="0px" borderColor={borderColor}>
                {clinic.name}
              </Td>
              <Td borderColor={borderColor}>{clinic.clinicType}</Td>
              <Td borderColor={borderColor}>{clinic.size}</Td>
              <Td borderColor={borderColor}>
                {clinic.status === "active" ? (
                  <Text color="green.400" fontWeight="bold">
                    Active
                  </Text>
                ) : (
                  <Text color="red.400" fontWeight="bold">
                    Inactive
                  </Text>
                )}
              </Td>
              <Td borderColor={borderColor}>
                <Button
                  leftIcon={<ViewIcon />}
                  colorScheme="teal"
                  variant="solid"
                  as={Link}
                  to={{
                    pathname: "view-clinic",
                    state: {
                      clinic,
                    },
                  }}
                >
                  View
                </Button>
              </Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
    </Flex>
  );
}

export default ViewDepartment;
