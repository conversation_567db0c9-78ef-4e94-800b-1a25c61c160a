import { getConnection } from "typeorm";
import logger from "./logger";
import { logError } from "./utils";
import { registerEnumType } from "type-graphql";
import { Payment } from "../entities/Payment";

enum PaymentStatus {
  ACTIVE = "active",
  CANCELED = "canceled",
  EXPIRED = "expired",
  PENDING = "pending",
  TRIAL = "trial",
}

registerEnumType(PaymentStatus, {
  name: "PaymentStatus",
  description: "Status of a payment",
});

export async function checkExpiredPayments() {
  try {
    const now = new Date();

    const conn = getConnection();
    const paymentRepo = conn.getRepository(Payment);

    // Find active or trial payments that have expired
    const expiredPayments = await paymentRepo.find({
      where: {
        status: ["active", "trial"],
        endDate: paymentRepo
          .createQueryBuilder()
          .where("endDate < :now", { now })
          .getQuery(),
        deleted: false,
      },
    });

    if (expiredPayments.length > 0) {
      logger.info(`Found ${expiredPayments.length} expired payments to update`);

      // Update all expired payments
      for (const payment of expiredPayments) {
        payment.status = PaymentStatus.EXPIRED;
        await paymentRepo.save(payment);
        logger.info(
          `Updated payment ID ${payment.id} for company ${payment.companyId} to expired status`
        );
      }
    } else {
      logger.info("No expired payments found");
    }
  } catch (error) {
    logger.error("Error checking expired payments:", error);
    logError(
      "", // No specific company ID for this system-level error
      "Failed to check expired payments",
      "SUBSCRIPTION_CHECK_ERROR",
      JSON.stringify(error),
      "high",
      "payment-check",
      ""
    );
  }
}

/**
 * Sends renewal reminders for payments that are about to expire
 * Can be expanded to include email notifications, etc.
 */
export async function sendPaymentReminders() {
  // Implementation for sending reminders about upcoming expirations
  // This is a placeholder for future implementation
}
