import {
  Button,
  Icon,
  Td,
  Text,
  Tr,
  useColorModeValue,
} from "@chakra-ui/react";
import { FaEdit, FaTrashAlt } from "react-icons/fa";
import { Item } from "../../../generated/graphql";
import { formatToMoney } from "../../../utils/Helpers";

const ServicesTableRow = (props: {
  service: Item;
  role: string;
  isLast: boolean;
  history: any;
  callOnClick: any;
}) => {
  const textColor = useColorModeValue("gray.700", "white");
  const borderColor = useColorModeValue("gray.200", "gray.600");

  const { service, role, history, callOnClick } = props;

  return (
    <Tr>
      <Td borderColor={borderColor}>
        <Text color={textColor} fontSize="sm" fontWeight="bold">
          {service.name}
        </Text>
      </Td>
      <Td borderColor={borderColor}>
        <Text color={textColor} fontSize="sm">
          {service.description}
        </Text>
      </Td>
      <Td borderColor={borderColor}>
        <Text color={textColor} fontSize="sm">
          {service.reference}
        </Text>
      </Td>
      <Td borderColor={borderColor}>
        <Text color={textColor} fontSize="sm">
          {formatToMoney(service.sellingPrice)}
        </Text>
      </Td>
      <Td borderColor={borderColor}>
        <Button
          colorScheme="blue"
          size="sm"
          onClick={() =>
            history.push({
              pathname: `/${role}/manage-service`,
              state: { service },
            })
          }
        >
          <Icon as={FaEdit} color={textColor} fontSize="sm" mr="4px" />
          Edit
        </Button>
        <Button
          colorScheme="red"
          size="sm"
          ml={2}
          onClick={() => callOnClick(service.id)}
        >
          <Icon as={FaTrashAlt} color={textColor} fontSize="sm" mr="4px" />
          Delete
        </Button>
      </Td>
    </Tr>
  );
};

export default ServicesTableRow;
