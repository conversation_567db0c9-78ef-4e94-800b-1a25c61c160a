import { isAuth } from "../middleware/isAuth";
import {
  Arg,
  Mutation,
  Resolver,
  UseMiddleware,
  Query,
  Ctx,
  Field,
  ObjectType,
} from "type-graphql";
import { Permission } from "../entities/Permission";
import { BooleanResponse, FieldError } from "./user";
import { Role } from "../entities/Role";
import { User } from "../entities/User";
import { getConnection, Like } from "typeorm";
import { MyContext } from "../types";
import { logError } from "../utils/utils";

@ObjectType()
export class PermissionResponse {
  @Field()
  status: boolean;
  @Field(() => Permission, { nullable: true })
  permission?: Permission;
  @Field(() => FieldError, { nullable: true })
  error?: FieldError;
}

@Resolver(Permission)
export class PermissionResolver {
  @Mutation(() => PermissionResponse)
  @UseMiddleware(isAuth)
  async addPermission(
    @Arg("name") name: string,
    @Arg("roleId", () => String, { nullable: true }) roleId: string,
    @Arg("userId", () => String, { nullable: true }) userId: string,
    @Ctx() { req }: MyContext
  ): Promise<PermissionResponse> {
    const companyId = req.session.companyId;
    const requestUserId = req.session.userId;
    let thePermission: Permission;
    try {
      if (roleId) {
        const role = await Role.findOne({
          where: {
            id: roleId,
            companyId: req.session.companyId,
          },
        });

        if (!role) {
          logError(
            companyId,
            "Role not found",
            "ADD_PERMISSION_ROLE_NOT_FOUND",
            JSON.stringify({ roleId }),
            "medium",
            `Add permission failed - role not found: ${roleId}`,
            requestUserId
          );
          return {
            status: false,
            error: {
              target: "general",
              message: "Role does not exist or access denied!",
            },
          };
        }
        const existingPerms = await Permission.find({
          where: {
            companyId: req.session.companyId,
            name: Like(`${name.split(">")[0]}>%`),
          },
          relations: ["roles"],
        });
        if (existingPerms) {
          let ourPerms = existingPerms.filter((tempPerm) => {
            let boolVar = false;
            tempPerm.roles.forEach((r) => {
              if (r.id === role.id && tempPerm.name !== name) boolVar = true;
            });
            return boolVar;
          });
          ourPerms.forEach(async (p) => {
            let newRoles: Role[] = [];
            p.roles.forEach((r) => {
              if (r.name !== role.name) newRoles.push(r);
            });
            p.roles = newRoles;
            await p.save();
          });
        }
        const perm = await Permission.findOne({
          where: { companyId: req.session.companyId, name },
          relations: ["roles"],
        });
        if (perm) {
          await Permission.createQueryBuilder()
            .relation(Permission, "roles")
            .of(perm)
            .add(role);
          thePermission = perm;
        } else if (!perm) {
          thePermission = await Permission.create({
            name,
            companyId: req.session.companyId,
            roles: [role],
          }).save();
        }
      } else if (userId) {
        const user = await User.findOne({
          where: {
            id: userId,
            companyId: req.session.companyId,
          },
        });
        if (!user) {
          logError(
            companyId,
            "User not found",
            "ADD_PERMISSION_USER_NOT_FOUND",
            JSON.stringify({ userId }),
            "medium",
            `Add permission failed - user not found: ${userId}`,
            requestUserId
          );
          return {
            status: false,
            error: {
              target: "general",
              message: "User does not exist or access denied!",
            },
          };
        }
        const existingPerms = await Permission.find({
          where: {
            companyId: req.session.companyId,
            name: Like(`${name.split(">")[0]}>%`),
          },
          relations: ["users"],
        });
        if (existingPerms) {
          let ourPerms = existingPerms.filter((tempPerm) => {
            let boolVar = false;
            tempPerm.users.forEach((u) => {
              if (u.id === user.id && tempPerm.name !== name) boolVar = true;
            });
            return boolVar;
          });
          ourPerms.forEach(async (p) => {
            let newUsers: User[] = [];
            p.users.forEach((u) => {
              if (u.id !== user.id) newUsers.push(u);
            });
            p.users = newUsers;
            await p.save();
          });
        }
        const perm = await Permission.findOne({
          where: { companyId: req.session.companyId, name },
          relations: ["users"],
        });
        if (perm) {
          //use query builder to save permission
          await Permission.createQueryBuilder()
            .relation(Permission, "users")
            .of(perm)
            .add(user);
          thePermission = perm;
        } else if (!perm) {
          console.log("we create mission with this name and company", perm);
          thePermission = await Permission.create({
            name,
            companyId: req.session.companyId,
            users: [user],
          }).save();
        }
      } else {
        logError(
          companyId,
          "Missing required parameters",
          "ADD_PERMISSION_MISSING_PARAMS",
          JSON.stringify({ roleId, userId }),
          "medium",
          "Add permission failed - no user or role provided",
          requestUserId
        );
        return {
          status: false,
          error: {
            target: "general",
            message: "User or Role must be provided!",
          },
        };
      }
      return { status: true, permission: thePermission! };
    } catch (err) {
      logError(
        companyId,
        err.message,
        "ADD_PERMISSION_ERROR",
        JSON.stringify({ error: err, name, roleId, userId }),
        "high",
        "Failed to add permission",
        requestUserId
      );
      console.error(err.message);
      return {
        status: false,
        error: { target: "general", message: "Failed to add permission" },
      };
    }
  }

  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async editPermission(
    @Arg("id", () => String) id: string,
    @Arg("name") name: string,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    if (!name || name === "") {
      logError(
        companyId,
        "Empty permission name",
        "EDIT_PERMISSION_EMPTY_NAME",
        JSON.stringify({ id, name }),
        "medium",
        "Edit permission failed - empty name provided",
        userId
      );
      return {
        status: false,
        error: { target: "general", message: "name can not be empty!" },
      };
    }
    const permission = await Permission.findOne({
      where: { id, companyId: req.session.companyId },
    });
    if (!permission) {
      logError(
        companyId,
        "Permission not found",
        "EDIT_PERMISSION_NOT_FOUND",
        JSON.stringify({ id }),
        "medium",
        `Edit permission failed - permission not found: ${id}`,
        userId
      );
      return {
        status: false,
        error: { target: "general", message: "permission does not exist!" },
      };
    }
    try {
      permission.name = name;
      await permission.save();
    } catch (err) {
      logError(
        companyId,
        err.message,
        "EDIT_PERMISSION_ERROR",
        JSON.stringify({ error: err, id, name }),
        "high",
        "Failed to edit permission",
        userId
      );
      console.error(err.message);
      return {
        status: false,
        error: { target: "general", message: err.message },
      };
    }
    return { status: true };
  }

  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async removePermission(
    @Arg("name") name: string,
    @Arg("roleId", () => String, { nullable: true }) roleId: string,
    @Arg("userId", () => String, { nullable: true }) userId: string,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const companyId = req.session.companyId;
    const requestUserId = req.session.userId;

    try {
      await getConnection().transaction(async (tm) => {
        if (roleId) {
          // Remove permission from a role
          const role = await Role.findOne({
            where: {
              id: roleId,
              companyId: req.session.companyId,
            },
          });
          if (!role) {
            logError(
              companyId,
              "Role not found",
              "REMOVE_PERMISSION_ROLE_NOT_FOUND",
              JSON.stringify({ roleId }),
              "medium",
              `Remove permission failed - role not found: ${roleId}`,
              requestUserId
            );
            return {
              status: false,
              error: { target: "general", message: "Role does not exist!" },
            };
          }

          const perm = await Permission.findOne({
            where: { companyId: req.session.companyId, name },
            relations: ["roles"],
          });
          if (perm) {
            // Filter out the role from the permission's roles
            const filteredRoles = perm.roles.filter((r) => r.id !== role.id);
            await tm
              .createQueryBuilder()
              .delete()
              .from("role_permission")
              .where(
                "permissionId = :permissionId AND companyId_1 = :companyId",
                {
                  permissionId: perm.id,
                  companyId,
                }
              )
              .execute();
            const insertValues = filteredRoles.map((role) => ({
              permissionId: perm.id,
              roleId: role.id,
              companyId_1: companyId,
              companyId_2: companyId,
            }));

            await tm
              .createQueryBuilder()
              .insert()
              .into("role_permission")
              .values(insertValues)
              .execute();

            return { status: true };
          } else {
            logError(
              companyId,
              "Permission not found",
              "REMOVE_PERMISSION_NOT_FOUND",
              JSON.stringify({ name, roleId }),
              "medium",
              `Remove permission failed - permission not found: ${name}`,
              requestUserId
            );
            return {
              status: false,
              error: {
                target: "general",
                message: "Permission does not exist!",
              },
            };
          }
        } else if (userId) {
          // Remove permission from a user
          const user = await User.findOne({
            where: {
              id: userId,
              companyId: req.session.companyId,
            },
          });
          if (!user) {
            logError(
              companyId,
              "User not found",
              "REMOVE_PERMISSION_USER_NOT_FOUND",
              JSON.stringify({ userId }),
              "medium",
              `Remove permission failed - user not found: ${userId}`,
              requestUserId
            );
            return {
              status: false,
              error: { target: "general", message: "User does not exist!" },
            };
          }

          const perm = await Permission.findOne({
            where: { companyId: req.session.companyId, name },
            relations: ["users"],
          });
          if (perm) {
            // Filter out the user from the permission's users
            const filteredUsers = perm.users.filter((u) => u.id !== user.id);
            await tm
              .createQueryBuilder()
              .delete()
              .from("user_permission")
              .where(
                "permissionId = :permissionId AND companyId_1 = :companyId",
                {
                  permissionId: perm.id,
                  companyId,
                }
              )
              .execute();
            const insertValues = filteredUsers.map((user) => ({
              permissionId: perm.id,
              userId: user.id,
              companyId_1: companyId,
              companyId_2: companyId,
            }));

            await tm
              .createQueryBuilder()
              .insert()
              .into("user_permission")
              .values(insertValues)
              .execute();

            return { status: true };
          } else {
            logError(
              companyId,
              "Permission not found",
              "REMOVE_PERMISSION_NOT_FOUND",
              JSON.stringify({ name, userId }),
              "medium",
              `Remove permission failed - permission not found: ${name}`,
              requestUserId
            );
            return {
              status: false,
              error: {
                target: "general",
                message: "Permission does not exist!",
              },
            };
          }
        } else {
          logError(
            companyId,
            "Missing required parameters",
            "REMOVE_PERMISSION_MISSING_PARAMS",
            JSON.stringify({ roleId, userId }),
            "medium",
            "Remove permission failed - no user or role provided",
            requestUserId
          );
          return {
            status: false,
            error: {
              target: "general",
              message: "User or Role must be provided!",
            },
          };
        }
      });
    } catch (err) {
      logError(
        companyId,
        err.message,
        "REMOVE_PERMISSION_ERROR",
        JSON.stringify({ error: err, name, roleId, userId }),
        "high",
        "Failed to remove permission",
        requestUserId
      );
      console.error(err.message);
      return {
        status: false,
        error: { target: "general", message: err.message },
      };
    }
    return { status: true };
  }

  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async deletePermission(
    @Arg("id", () => String) id: string,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    try {
      const result = await Permission.delete({
        id,
        companyId: req.session.companyId,
      });
      if (result.affected === 0) {
        logError(
          companyId,
          "Permission not found",
          "DELETE_PERMISSION_NOT_FOUND",
          JSON.stringify({ id }),
          "medium",
          `Delete permission failed - permission not found: ${id}`,
          userId
        );
        return {
          status: false,
          error: { target: "general", message: "Permission not found" },
        };
      }
    } catch (err) {
      logError(
        companyId,
        err.message,
        "DELETE_PERMISSION_ERROR",
        JSON.stringify({ error: err, id }),
        "high",
        "Failed to delete permission",
        userId
      );
      console.error(err.message);
      return {
        status: false,
        error: { target: "general", message: err.message },
      };
    }
    return { status: true };
  }

  @Query(() => [Permission])
  async getPermissions(@Ctx() { req }: MyContext): Promise<Permission[]> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    try {
      return await Permission.find({
        where: { companyId: req.session.companyId },
        relations: ["roles", "users"],
        order: { name: "ASC" },
      });
    } catch (err) {
      logError(
        companyId,
        err.message,
        "GET_PERMISSIONS_ERROR",
        JSON.stringify({ error: err }),
        "high",
        "Failed to fetch permissions",
        userId
      );
      console.error(err.message);
      return [];
    }
  }
}
