query getEmployees {
  getEmployees {
    ...Me
  }
}

query getDepartments {
  getDepartments {
    id
    name
    type
    description
    status
    parentId
    headOfDepartment {
      id
    }
    employees {
      id
    }
    clinics {
      id
      name
      clinicType
      size
      status
      description
    }
  }
}

query getClinics {
  getClinics {
    id
    name
    description
    status
    clinicType
    size
    department {
      id
      name
    }
    leader {
      id
    }
  }
}

query getClinic($id: Int!) {
  getClinic(id: $id) {
    id
    name
    description
    status
    clinicType
    size
    department {
      id
      name
    }
    leader {
      id
    }
  }
}
