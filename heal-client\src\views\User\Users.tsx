// Chakra imports
import {
  //   Avatar,
  //   Box,
  Button,
  Flex,
  Grid,
  GridItem,
  Icon,
  //   Image,
  Text,
  //   useColorMode,
  // useColorMode,
  useColorModeValue,
  useToast,
} from "@chakra-ui/react";
import {
  //   FaCube,
  //   FaFacebook,
  //   FaInstagram,
  FaPlus,
  //   FaTwitter,
} from "react-icons/fa";
// Custom components
import Card from "../../components/Card/Card";
import CardBody from "../../components/Card/CardBody";
import CardHeader from "../../components/Card/CardHeader";
import { SearchBar } from "../../components/Navbars/SearchBar/SearchBar";
import {
  useDeleteTypeMutation,
  useGetUsersQuery,
} from "../../generated/graphql";
import { Link } from "react-router-dom";
import DeleteConfirm from "../../components/DeleteConfirmation";
import { useContext, useState } from "react";
import UserListCard from "../../components/Card/UserCard";
import { MeContext } from "../../components/Wrapper";

// import ImageArchitect1 from "../../assets/img/ImageArchitect1.png";
// import ImageArchitect2 from "../../assets/img/ImageArchitect2.png";
// import ImageArchitect3 from "../../assets/img/ImageArchitect3.png";

function Users() {
  const [{ data, fetching }, getUsersAsync] = useGetUsersQuery({
    requestPolicy: "cache-and-network",
  });
  const me = useContext(MeContext);
  const [openDelete, setopenDelete] = useState({ open: false, id: -1000000 });
  //   const history = useHistory();
  const [{ fetching: loadingDelete }, deleteType] = useDeleteTypeMutation();
  const textColor = useColorModeValue("gray.700", "white");
  const bgProfile = useColorModeValue("hsla(0,0%,100%,.8)", "navy.800");
  const borderProfileColor = useColorModeValue("white", "transparent");

  const toast = useToast({
    position: "top",
  });

  const callDeleteType: any = async (id: number) => {
    console.log("delete with id: ", id);

    const { error } = await deleteType({ id });
    setopenDelete({ open: false, id: -1000000 });
    if (error)
      toast({
        title: "Type delete failed!",
        variant: "left-accent",
        status: "error",
        isClosable: true,
      });
    else {
      await getUsersAsync({ requestPolicy: "network-only" });
      toast({
        title: "Type delete successful!",
        variant: "left-accent",
        status: "success",
        isClosable: true,
      });
    }
  };

  return (
    <Flex direction="column" pt={{ base: "120px", md: "75px" }}>
      <DeleteConfirm
        loading={loadingDelete}
        item="Type"
        open={openDelete.open}
        id={openDelete.id!}
        feedback={callDeleteType}
        nofeedback={() => setopenDelete({ open: false, id: -1000000 })}
      />

      <Card overflowX={{ sm: "scroll", xl: "hidden" }} pb="0px">
        <CardHeader p="0px 0px 8px 0px">
          <Flex
            direction={{ sm: "column", md: "row" }}
            mb="12px"
            maxH="330px"
            justifyContent={{ sm: "center", md: "space-between" }}
            align="center"
            backdropFilter="blur(21px)"
            boxShadow="0px 2px 5.5px rgba(0, 0, 0, 0.02)"
            border="1.5px solid"
            borderColor={borderProfileColor}
            bg={bgProfile}
            p="24px"
            borderRadius="20px"
          >
            <Flex
              align="center"
              mb={{ sm: "10px", md: "0px" }}
              direction={{ sm: "column", md: "row" }}
              w={{ sm: "100%" }}
              textAlign={{ sm: "center", md: "start" }}
            >
              <Text>All Users</Text>
            </Flex>
            <Flex
              direction={{ sm: "column", lg: "row" }}
              w={{ sm: "100%", md: "50%", lg: "auto" }}
            >
              <SearchBar placeholder="Search companies ..." />

              <Link to={`/${me?.role.name}/add-user`}>
                <Button p="2px" bg="transparent" variant="no-effects">
                  <Flex
                    align="center"
                    w={{ lg: "135px" }}
                    borderRadius="15px"
                    justifyContent="center"
                    py="10px"
                    cursor="pointer"
                  >
                    <Icon
                      as={FaPlus}
                      color={textColor}
                      fontSize="xs"
                      mr="4px"
                    />
                    <Text fontSize="xs" color={textColor} fontWeight="bold">
                      ADD NEW USER
                    </Text>
                  </Flex>
                </Button>
              </Link>
            </Flex>
          </Flex>
        </CardHeader>
        <CardBody px="5px">
          <Grid
            templateColumns={{ sm: "1fr", md: "1fr 1fr", xl: "repeat(4, 1fr)" }}
            templateRows={{ sm: "1fr 1fr 1fr auto", md: "1fr 1fr", xl: "1fr" }}
            gap="24px"
          >
            {data?.getUsers.map((i) => {
              return (
                <UserListCard
                  key={i.id}
                  // id={i.id}
                  fullName={i.firstname + " " + i.lastname}
                  role={i.role.name}
                  phone={i.phone}
                />
              );
            })}
            {data && data?.getUsers.length > 0 && (
              <GridItem colSpan={1} m={0}>
                <Link to="/admin/add-user">
                  <Button
                    //   left={40}
                    // m="80px"
                    p="40px"
                    py={40}
                    bg="transparent"
                    border="1px solid lightgray"
                    borderRadius="15px"
                    minHeight={{ sm: "200px", md: "100%" }}
                  >
                    <Flex
                      direction="column"
                      justifyContent="center"
                      align="center"
                    >
                      <Icon
                        as={FaPlus}
                        mb={10}
                        color={textColor}
                        fontSize="lg"
                        mr="12px"
                      />
                      <Text fontSize="lg" color={textColor} fontWeight="bold">
                        Add more users
                      </Text>
                    </Flex>
                  </Button>
                </Link>
              </GridItem>
            )}

            {!fetching && !data?.getUsers && (
              <Link to={`/${me?.role.name}/add-user`}>
                <Button
                  left={40}
                  m="80px"
                  p="80px"
                  bg="transparent"
                  border="1px solid lightgray"
                  borderRadius="15px"
                  minHeight={{ sm: "200px", md: "100%" }}
                >
                  <Flex
                    direction="column"
                    justifyContent="center"
                    align="center"
                  >
                    <Icon
                      as={FaPlus}
                      mb={10}
                      color={textColor}
                      fontSize="lg"
                      mr="12px"
                    />
                    <Text fontSize="lg" color={textColor} fontWeight="bold">
                      No Users In DB, Add One Now
                    </Text>
                  </Flex>
                </Button>
              </Link>
            )}
          </Grid>
        </CardBody>
      </Card>
    </Flex>
  );
}

export default Users;
