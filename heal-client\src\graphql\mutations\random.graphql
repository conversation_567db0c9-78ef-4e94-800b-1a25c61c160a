# Types mutations
mutation deleteType($id: Float!) {
  deleteType(id: $id) {
    ...BooleanResponse
  }
}

mutation addType($args: TypeArgs!) {
  addType(args: $args) {
    status
    error {
      target
      message
    }
    data {
      id
      name
      description
    }
  }
}

mutation editType($id: Float!, $args: TypeEditArgs!) {
  editType(id: $id, args: $args) {
    ...BooleanResponse
  }
}

# Permission mutations
mutation addPermission($name: String!, $userId: Float, $roleId: Float) {
  addPermission(name: $name, userId: $userId, roleId: $roleId) {
    ...BooleanResponse
  }
}

mutation editPermission($id: Float!, $name: String!) {
  editPermission(id: $id, name: $name) {
    ...BooleanResponse
  }
}

mutation deletePermission($id: Float!) {
  deletePermission(id: $id) {
    ...BooleanResponse
  }
}

# Feature mutations
mutation addFeature($name: String!, $companyId: Float!) {
  addFeature(name: $name, companyId: $companyId) {
    ...BooleanResponse
  }
}

mutation editFeature($id: Float!, $name: String!) {
  editFeature(id: $id, name: $name) {
    ...BooleanResponse
  }
}

mutation deleteFeature($id: Float!) {
  deleteFeature(id: $id) {
    ...BooleanResponse
  }
}

# Roles mutations
mutation addRole($name: String!) {
  addRole(name: $name) {
    ...BooleanResponse
  }
}

mutation editRole($id: Float!, $args: RoleArgs!) {
  editRole(id: $id, args: $args) {
    ...BooleanResponse
  }
}

mutation deleteRole($id: Float!) {
  deleteRole(id: $id) {
    ...BooleanResponse
  }
}

# Category mutations
mutation addCategory($args: CategoryArgs!) {
  addCategory(args: $args) {
    ...BooleanResponse
  }
}

mutation editCategory($id: Float!, $args: CategoryArgs!) {
  editCategory(id: $id, args: $args) {
    ...BooleanResponse
  }
}

mutation editCategoryByName($name: String!, $args: CategoryArgs!) {
  editCategoryByName(name: $name, args: $args) {
    ...BooleanResponse
  }
}

mutation deleteCategory($id: Float!) {
  deleteCategory(id: $id) {
    ...BooleanResponse
  }
}

# adress mutation for editing
# mutation editAddress(
#   $id: Float
#   $addressId: Float!
#   $target: String!
#   $params: AddressType
# ) {
#   editAddress(
#     id: $id
#     addressId: $addressId
#     target: $target
#     params: $params
#   ) {
#     ...BooleanResponse
#   }
# }

mutation addAddress($id: Float!, $target: String!, $params: AddressType!) {
  addAddress(id: $id, target: $target, params: $params) {
    ...BooleanResponse
  }
}
