import React, { useContext, useState } from "react";
import { useForm } from "react-hook-form";
// Chakra imports
import {
  Flex,
  Button,
  FormControl,
  FormLabel,
  Input,
  Text,
  useColorModeValue,
  FormErrorMessage,
  useToast,
  Grid,
  GridItem,
} from "@chakra-ui/react";
import {
  useRegisterMutation,
  useGetRolesQuery,
  useGetCompaniesQuery,
} from "../../generated/graphql";
import { useHistory } from "react-router-dom";
import { HSeparator } from "../../components/Separator/Separator";
import { MeContext } from "../../components/Wrapper";
import { ActionMeta, AsyncSelect, SingleValue } from "chakra-react-select";

interface IAddUserProps {}

const AddUser: React.FC<IAddUserProps> = () => {
  const me = useContext(MeContext);
  const toast = useToast({
    position: "top",
  });
  const [, addUser] = useRegisterMutation();
  const [{ data: roles, fetching: loadingRoles }, getAsyncRoles] =
    useGetRolesQuery();
  const [{ data: companies, fetching: loadingCompanies }, getAsyncCompanies] =
    useGetCompaniesQuery();
  const [error, seterror] = useState("");
  const [roleType, setRoleType] = useState("");
  const history = useHistory();

  interface IFormInput {
    firstname: string;
    middlename: string;
    lastname: string;
    email: string;
    phone: string;
    password: string;
    role: number;
    company: number;
    companyRole: number;
  }

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
    setValue,
    clearErrors,
  } = useForm<IFormInput>();

  async function onSubmit(values: any) {
    seterror("");
    const args = {
      firstname: values.firstname,
      middlename: values.middlename,
      lastname: values.lastname,
      email: values.email,
      phone: values.phone,
      password: values.password,
      companyId: values.company,
      role: values.role,
      companyRole: values.companyRole,
    };
    const { data } = await addUser({ params: args });
    if (data?.register.error) {
      console.log("The error came back: ", data.register.error.message);
      toast({
        title: "User add failed!",
        variant: "left-accent",
        status: "error",
        isClosable: true,
      });
      return seterror(data?.register.error.message);
    } else if (!data?.register.error) {
      reset();
      toast({
        title: "User added successful!",
        variant: "left-accent",
        status: "success",
        isClosable: true,
      });
      if (me?.id)
        return history.push({
          pathname: `/${me?.role!.name}/users`,
        });
    }
  }

  const fetchRoles: any = async () => {
    if (!loadingRoles && roles) {
      return roles.getRoles
        .filter((r) => r.sys === true)
        .map((r) => {
          return {
            label: r.name,
            value: r.id,
            disabled: r.name === "admin" && me?.role!.name === "employee", //disable the admin option if user is not admin
          };
        });
    }
    await getAsyncRoles({ requestPolicy: "network-only" });
    if (roles) {
      return roles?.getRoles
        .filter((r) => r.sys === true)
        .map((r) => {
          return {
            label: r.name,
            value: r.id,
            disabled: r.name === "admin" && me?.role!.name === "employee", //disable the admin option if user is not admin
          };
        });
    } else return [];
  };

  const fetchCompanyRoles: any = async () => {
    if (!loadingRoles && roles) {
      return roles.getRoles
        .filter((r) => r.sys === false)
        .map((r) => {
          return {
            label: r.name,
            value: r.id,
          };
        });
    }
    await getAsyncRoles({ requestPolicy: "network-only" });
    if (roles) {
      return roles?.getRoles
        .filter((r) => r.sys === false)
        .map((r) => {
          return {
            label: r.name,
            value: r.id,
          };
        });
    } else return [];
  };

  const fetchCompanies: any = async () => {
    if (!loadingCompanies && companies) {
      return companies.getCompanies.map((r) => {
        return {
          label: r.name,
          value: r.id,
        };
      });
    }
    await getAsyncCompanies({ requestPolicy: "network-only" });
    if (companies) {
      return companies?.getCompanies.map((r) => {
        return {
          label: r.name,
          value: r.id,
        };
      });
    } else return [];
  };

  // Chakra color mode
  const textColor = useColorModeValue("gray.700", "white");
  const bgForm = useColorModeValue("white", "navy.800");

  function handleChange(
    newValue: SingleValue<{
      label: string;
      value: number;
    }>,
    actionMeta: ActionMeta<{
      label: string;
      value: number;
    }>
  ): void {
    setValue(actionMeta.name as any, newValue!.value);
  }

  // const titleColor = useColorModeValue("gray.700", "blue.500");
  // const colorIcons = useColorModeValue("gray.700", "white");
  // const bgIcons = useColorModeValue("trasnparent", "navy.700");
  // const bgIconsHover = useColorModeValue("gray.50", "whiteAlpha.100");
  return (
    <Flex position="relative" mb="90px">
      <Flex
        // minH={{ md: "1000px" }}
        h={{ sm: "initial", md: "75vh", lg: "85vh" }}
        w="100%"
        maxW="1044px"
        mx="auto"
        justifyContent="space-between"
        mb="60px"
        pt={{ md: "0px" }}
      >
        <Flex
          w="100%"
          h="100%"
          alignItems="center"
          justifyContent="center"
          mb="60px"
          mt={{ base: "50px", md: "50px" }}
        >
          <Flex
            zIndex="2"
            direction="column"
            w="690px"
            background="transparent"
            borderRadius="15px"
            p="40px"
            mx={{ base: "100px" }}
            m={{ base: "20px", md: "auto" }}
            bg={bgForm}
            boxShadow={useColorModeValue(
              "0px 5px 14px rgba(0, 0, 0, 0.05)",
              "unset"
            )}
          >
            <Text
              fontSize="xl"
              color={textColor}
              fontWeight="bold"
              textAlign="center"
              mb="12px"
            >
              Add User
            </Text>

            <HSeparator mt="6px" mb="6px" />
            <Text textAlign="center" mt="0px" mb="0px">
              User Details:
            </Text>
            <HSeparator mb="22px" mt="6px" />
            {error && (
              <Text pb="15px" m={"auto"} color="red.500" textColor="red.300">
                {error}
              </Text>
            )}
            <form onSubmit={handleSubmit(onSubmit)}>
              <Grid templateColumns="repeat(6, 1fr)" gap={6}>
                <FormControl
                  as={GridItem}
                  colSpan={3}
                  mb={5}
                  isInvalid={errors.firstname as any}
                >
                  <FormLabel htmlFor="firstname" fontSize="xs">
                    Firstname
                  </FormLabel>
                  <Input
                    variant="filled"
                    ms="4px"
                    type="text"
                    fontSize="xs"
                    placeholder="User Firstname"
                    mb="4px"
                    size="lg"
                    id="firstname"
                    {...register("firstname", {
                      required: "User firstname is required",
                      pattern: {
                        value: /^[a-zA-Z- -0-9]+$/,
                        message:
                          "User firstname can not have special characters",
                      },
                    })}
                  />
                  <FormErrorMessage mb="10px" fontSize="xs">
                    {errors.firstname && (errors.firstname.message as any)}
                  </FormErrorMessage>
                </FormControl>
                <FormControl
                  as={GridItem}
                  colSpan={3}
                  mb={5}
                  isInvalid={errors.middlename as any}
                >
                  <FormLabel htmlFor="middlename" fontSize="xs">
                    Middlename
                  </FormLabel>
                  <Input
                    variant="filled"
                    ms="4px"
                    type="text"
                    fontSize="xs"
                    placeholder="User middlename is required"
                    mb="4px"
                    size="lg"
                    id="middlename"
                    {...register("middlename", {
                      required: false,
                      pattern: {
                        value: /^[a-zA-Z- -0-9]+$/,
                        message:
                          "User middlename can not have special characters",
                      },
                    })}
                  />
                  <FormErrorMessage mb="10px" fontSize="xs">
                    {errors.middlename && (errors.middlename.message as any)}
                  </FormErrorMessage>
                </FormControl>
                <FormControl
                  as={GridItem}
                  colSpan={3}
                  mb={5}
                  isInvalid={errors.lastname as any}
                >
                  <FormLabel htmlFor="lastname" fontSize="xs">
                    Lastname
                  </FormLabel>
                  <Input
                    variant="filled"
                    ms="4px"
                    type="text"
                    fontSize="xs"
                    placeholder="User Lastname"
                    mb="4px"
                    size="lg"
                    id="lastname"
                    {...register("lastname", {
                      required: "User lastname is required",
                      pattern: {
                        value: /^[a-zA-Z- -0-9]+$/,
                        message:
                          "User lastname can not have special characters",
                      },
                    })}
                  />
                  <FormErrorMessage mb="10px" fontSize="xs">
                    {errors.lastname && (errors.lastname.message as any)}
                  </FormErrorMessage>
                </FormControl>
                <FormControl
                  as={GridItem}
                  colSpan={3}
                  mb={5}
                  isInvalid={errors.email as any}
                >
                  <FormLabel htmlFor="email" fontSize="xs">
                    Email
                  </FormLabel>
                  <Input
                    variant="filled"
                    ms="4px"
                    type="email"
                    fontSize="xs"
                    placeholder="User Email"
                    mb="4px"
                    size="lg"
                    id="email"
                    {...register("email", {
                      required: "User email is required",
                      pattern: {
                        value: /^\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/,
                        message: "User email is incorrectly formatted",
                      },
                    })}
                  />

                  <FormErrorMessage mb="10px" fontSize="xs">
                    {errors.email && (errors.email.message as any)}
                  </FormErrorMessage>
                </FormControl>
                <FormControl
                  as={GridItem}
                  colSpan={3}
                  mb={5}
                  isInvalid={errors.phone as any}
                >
                  <FormLabel htmlFor="phone" fontSize="xs">
                    Phone
                  </FormLabel>
                  <Input
                    variant="filled"
                    ms="4px"
                    type="text" // Change type to text to allow spaces
                    fontSize="xs"
                    maxLength={12} // Limit the total length to 12 characters
                    placeholder="User Phone"
                    mb="4px"
                    size="lg"
                    id="phone"
                    {...register("phone", {
                      required: "User phone is required",
                      pattern: {
                        value: /^0(\d{3})\s?(\d{3})\s?(\d{3})$/,
                        message:
                          "User phone must be in the format 0xxx xxx xxx",
                      },
                    })}
                    onChange={(e) => {
                      let value = e.target.value.replace(/\D/g, ""); // Remove non-digit characters
                      // Ensure the first character is "0"
                      value = value.replace(/^0*(\d*)/, "0$1");
                      // Limit to 12 characters, including spaces
                      value = value.slice(0, 12);
                      const formattedValue = value.replace(
                        /(\d{1})(\d{3})(\d{3})(\d{3})/,
                        "$1$2 $3 $4"
                      ); // Add spaces between numbers
                      e.target.value = formattedValue;
                    }}
                  />

                  <FormErrorMessage fontSize="xs">
                    {errors.phone && (errors.phone.message as any)}
                  </FormErrorMessage>
                </FormControl>
                <FormControl
                  as={GridItem}
                  colSpan={3}
                  mb={5}
                  isInvalid={errors.password as any}
                >
                  <FormLabel htmlFor="password" fontSize="xs">
                    Password
                  </FormLabel>
                  <Input
                    variant="filled"
                    fontSize="xs"
                    ms="4px"
                    type="password"
                    placeholder="User Password"
                    mb="4px"
                    size="lg"
                    id="password"
                    minLength={6}
                    maxLength={20}
                    {...register("password", {
                      required: "Password is required",
                    })}
                  />
                  <FormErrorMessage fontSize="xs">
                    {errors.password && (errors.password.message as any)}
                  </FormErrorMessage>
                </FormControl>
              </Grid>

              <HSeparator mt="6px" mb="6px" />
              <Text textAlign="center" mt="6px" mb="6px">
                Admin Configuration:
              </Text>
              <HSeparator mb="22px" mt="6px" />

              <Grid templateColumns="repeat(6, 1fr)" gap={6}>
                <FormControl
                  as={GridItem}
                  colSpan={3}
                  mt="10px"
                  alignItems="center"
                  mb="20px"
                  isInvalid={errors.company as any}
                >
                  <AsyncSelect
                    variant="flushed"
                    {...register("company", {
                      required: "Choose a company for the user",
                    })}
                    name="company"
                    id="company"
                    size="sm"
                    defaultOptions={true}
                    cacheOptions={true}
                    isLoading={loadingCompanies}
                    onChange={handleChange}
                    loadOptions={fetchCompanies}
                    placeholder="Select a company"
                    closeMenuOnSelect={true}
                    escapeClearsValue={true}
                    hideSelectedOptions={true}
                  />
                  <FormErrorMessage fontSize="xs" mt={5}>
                    {errors.company && (errors.company.message as any)}
                  </FormErrorMessage>
                </FormControl>
                <FormControl
                  as={GridItem}
                  colSpan={3}
                  mt="10px"
                  alignItems="center"
                  mb="20px"
                  isInvalid={errors.role as any}
                >
                  <AsyncSelect
                    variant="flushed"
                    {...register("role")}
                    name="role"
                    id="role"
                    size="sm"
                    defaultOptions={true}
                    cacheOptions={true}
                    isLoading={loadingRoles}
                    loadOptions={fetchRoles}
                    placeholder="Select a role"
                    closeMenuOnSelect={true}
                    // onChange={handleChange}
                    escapeClearsValue={true}
                    hideSelectedOptions={true}
                    isOptionDisabled={(option: any) => option.disabled === true}
                    onChange={(event: any) => {
                      setRoleType(event.label);
                      setValue("role", event.value);
                      if (event.label === "admin") {
                        clearErrors("companyRole");
                        setValue("companyRole", 0);
                      }
                    }}
                  />
                </FormControl>
                <FormControl
                  as={GridItem}
                  colSpan={6}
                  mt="10px"
                  alignItems="center"
                  mb="20px"
                  isInvalid={errors.companyRole as any}
                >
                  <AsyncSelect
                    variant="flushed"
                    {...register("companyRole", {
                      required:
                        roleType !== "admin"
                          ? "Choose a role for the employee"
                          : undefined, // Make required based on roleType
                    })}
                    name="companyRole"
                    id="companyRole"
                    size="sm"
                    isDisabled={roleType === "" || roleType === "admin"} // Disable if roleType is admin or empty
                    defaultOptions={true}
                    cacheOptions={true}
                    isLoading={loadingRoles}
                    onChange={handleChange}
                    loadOptions={fetchCompanyRoles}
                    placeholder="Select a company role"
                    closeMenuOnSelect={true}
                    escapeClearsValue={true}
                    hideSelectedOptions={true}
                  />
                  <FormErrorMessage fontSize="xs" mt={5}>
                    {errors.companyRole && (errors.companyRole.message as any)}
                  </FormErrorMessage>
                </FormControl>
              </Grid>

              <Button
                fontSize="14px"
                variant="dark"
                fontWeight="bold"
                w="100%"
                h="45"
                mb="24px"
                mt={4}
                colorScheme="teal"
                isLoading={isSubmitting}
                type="submit"
              >
                Add User
              </Button>
            </form>
          </Flex>
        </Flex>
      </Flex>
    </Flex>
  );
};

export default AddUser;
