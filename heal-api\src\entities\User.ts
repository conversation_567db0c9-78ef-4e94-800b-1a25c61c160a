import { Field, ObjectType } from "type-graphql";
import {
  Column,
  Entity,
  ManyToOne,
  ManyToMany,
  OneToOne,
  JoinColumn,
} from "typeorm";
import { Role } from "./Role";
import { Permission } from "./Permission";
import { AuditBaseEntity } from "./AuditEntity";
import { Category } from "./Category";
import { Employee } from "./Employee";
import { Company } from "./Company";

@ObjectType()
@Entity()
export class User extends AuditBaseEntity {
  @Field(() => String)
  @Column({ type: "text" })
  firstname!: string;

  @Field(() => String)
  @Column({ type: "text" })
  middlename: string;

  @Field(() => String)
  @Column({ type: "text" })
  lastname!: string;

  @Field(() => String, { nullable: true })
  @Column({ type: "text", unique: true, nullable: false })
  email: string;

  @Field(() => String)
  @Column({ type: "text", unique: true })
  phone: string;

  @Field(() => String)
  @Column({
    type: "enum",
    enum: ["male", "female", "other"],
    default: "other",
  })
  gender: string;

  @Field(() => String, { nullable: true })
  @Column({ type: "text", nullable: true })
  dateOfBirth: string;

  @Field(() => String, { nullable: true })
  @Column({ type: "text", nullable: true })
  image: string;

  @Field(() => String, { nullable: true })
  @Column({ type: "text", nullable: true })
  address: string;

  @Field(() => Company)
  @ManyToOne(() => Company, (company) => company.users)
  @JoinColumn({ name: "companyId" })
  company: Company;

  @Field(() => String)
  @Column({ type: "uuid" })
  roleId: string;

  @Field(() => Role)
  @ManyToOne(() => Role, (role) => role.users)
  @JoinColumn([
    { name: "roleId", referencedColumnName: "id" },
    { name: "companyId", referencedColumnName: "companyId" },
  ])
  role: Role;

  @Field(() => Employee, { nullable: true })
  @OneToOne(() => Employee, (employee) => employee.user, { nullable: true })
  employee: Employee;

  @Column({ type: "text", default: "talisia" })
  password: string;

  @Field(() => [Category], { nullable: true })
  @ManyToMany(() => Category, (category) => category.user)
  status: Category[];

  @Field(() => [Permission], { nullable: true })
  @ManyToMany(() => Permission, (permission) => permission.users)
  permissions: Permission[];
}
