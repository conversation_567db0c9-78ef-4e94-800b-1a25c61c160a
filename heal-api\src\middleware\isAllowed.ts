import { MyContext } from "src/types";
import { MiddlewareFn } from "type-graphql";

export const isAllowed = (allowedRoles: string[]): MiddlewareFn<MyContext> => {
  return async ({ context }: { context: MyContext }, next) => {
    const userRole = context.req.session.role;
    if (!userRole || !allowedRoles.includes(userRole)) {
      throw new Error("Not authorized to access this resource");
    }
    return next();
  };
};
