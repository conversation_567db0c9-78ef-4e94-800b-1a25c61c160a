import { defineConfig, transformWithEsbuild, Plugin } from "vite";
import { resolve } from "path";
import react from "@vitejs/plugin-react";
import viteTsconfigPaths from "vite-tsconfig-paths";
import svgrPlugin from "vite-plugin-svgr";
import checker from "vite-plugin-checker";
import handlebars from "vite-plugin-handlebars";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react(),
    // Workaround to allow to use react in js files instead of only jsx will probably be useless later as we restructure
    {
      name: "load+transform-js-files-as-jsx",
      async transform(code, id) {
        if (!id.match(/src\/.*\.js$/)) {
          return null;
        }

        // Use the exposed transform from vite, instead of directly
        // transforming with esbuild
        return transformWithEsbuild(code, id, {
          loader: "jsx",
          jsx: "automatic", // 👈 this is important
        });
      },
    },
    checker({
      overlay: { initialIsOpen: false },
      typescript: true,
      eslint: {
        lintCommand: 'eslint "./src/**/*.{ts,tsx}"',
      },
    }),
    viteTsconfigPaths(),
    svgrPlugin(),
    handlebars({
      context: {}, // Specify your Handlebars context object if needed
      reloadOnPartialChange: true, // Set to true if you want to reload templates on partial changes
      compileOptions: {}, // Specify Handlebars compile options if needed
      runtimeOptions: {}, // Specify Handlebars runtime options if needed
      partialDirectory: resolve(__dirname, "src/partials"),
      helpers: {}, // Specify Handlebars helpers if needed
    }) as Plugin,
  ],
  server: {
    port: 3000,
    proxy: {
      "/api-server/": "...",
      "/authorization/": "...",
    },
  },
  // Workaround before renaming .js to .jsx
  optimizeDeps: {
    esbuildOptions: {
      loader: {
        ".js": "jsx",
      },
    },
  },
});
