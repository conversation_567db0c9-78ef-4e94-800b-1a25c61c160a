import React, { useEffect, useState } from "react";
import AsyncSelect from "react-select/async";
import { Item } from "../../../generated/graphql";
import { Box, Text } from "@chakra-ui/react";
import { formatToMoney } from "../../../utils/Helpers";

interface UnitInputProps {
  selectedItem: Item | undefined;
  onChange: (value: any) => void;
  price?: boolean;
}

const UnitInput: React.FC<UnitInputProps> = ({
  selectedItem,
  onChange,
  price,
}) => {
  const [unitOptions, setUnitOptions] = useState<
    { label: string; value: string; price?: number }[]
  >([]);

  useEffect(() => {
    if (selectedItem) {
      let units = [];
      if (selectedItem.unit)
        units.push({
          unit: selectedItem.unit,
          price: selectedItem.sellingPrice,
        });
      if (selectedItem.pieceUnit)
        units.push({
          unit: selectedItem.pieceUnit,
          price: selectedItem.pieceSellingPrice,
        });
      if (selectedItem.subPieceUnit)
        units.push({
          unit: selectedItem.subPieceUnit,
          price: selectedItem.subPieceSellingPrice,
        });

      const unitsToReturn = units.map((u) => ({
        label: u.unit,
        value: u.unit,
        price: u.price,
      }));
      setUnitOptions(unitsToReturn);
    }
  }, [selectedItem]);

  const loadUnits = async (searchInput: string) => {
    if (selectedItem) {
      let units = [];
      if (selectedItem.unit)
        units.push({
          unit: selectedItem.unit,
          price: selectedItem.sellingPrice,
        });
      if (selectedItem.pieceUnit)
        units.push({
          unit: selectedItem.pieceUnit,
          price: selectedItem.pieceSellingPrice,
        });
      if (selectedItem.subPieceUnit)
        units.push({
          unit: selectedItem.subPieceUnit,
          price: selectedItem.subPieceSellingPrice,
        });

      const filteredUnits = units
        .filter((u) =>
          u.unit.toLocaleLowerCase().includes(searchInput.toLocaleLowerCase())
        )
        .map((u) => ({
          label: u.unit,
          value: u.unit,
          price: u.price,
        }));

      return filteredUnits;
    }
    return [];
  };

  const customOption = (props: any) => {
    const { innerRef, innerProps, data } = props;
    return (
      <Box
        ref={innerRef}
        {...innerProps}
        display="flex"
        justifyContent="space-between"
      >
        <Text>{data.label}</Text>
        {price && data.price && (
          <Text ml={2}>{formatToMoney(data.price.toFixed(2))}</Text>
        )}
      </Box>
    );
  };

  return (
    <AsyncSelect
      variant="flushed"
      isClearable
      isSearchable={true}
      defaultOptions={unitOptions}
      loadOptions={loadUnits}
      size="sm"
      colorScheme="navy"
      placeholder="Select unit"
      closeMenuOnSelect={true}
      onChange={onChange}
      escapeClearsValue={true}
      hideSelectedOptions={true}
      styles={{
        container: (base) => ({
          ...base,
          width: "100%",
          color: "navy",
        }),
      }}
      components={{ Option: customOption }}
    />
  );
};

export default UnitInput;
