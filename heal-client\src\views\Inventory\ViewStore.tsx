import { useContext, useState } from "react";
import { useHistory, useLocation } from "react-router-dom";
import { FaStore } from "react-icons/fa";
import {
  Item,
  Store,
  useDeleteItemMutation,
  useGetStoreItemsQuery,
} from "../../generated/graphql";
// Chakra imports
import {
  Flex,
  Icon,
  Table,
  Tbody,
  Text,
  Th,
  Thead,
  Tr,
  Box,
  Grid,
  GridItem,
  useColorModeValue,
  useToast,
} from "@chakra-ui/react";
// Custom components
import { MeContext } from "../../components/Wrapper";
import DeleteConfirm from "../../components/DeleteConfirmation";
import ItemsTableRow from "../../components/Tables/ItemsRow";

function ViewStore() {
  const me = useContext(MeContext);
  const location = useLocation<{ store: Store }>();
  const { store } = location.state;
  const [{ data, fetching }, getStoreItemsAsync] = useGetStoreItemsQuery({
    variables: {
      storeId: store.id,
    },
    requestPolicy: "network-only",
  });
  const [openDelete, setopenDelete] = useState({ open: false, id: -1000000 });
  const history = useHistory();
  const [{ fetching: loadingDelete }, deleteItem] = useDeleteItemMutation();

  const textColor = useColorModeValue("gray.700", "white");
  const bgColor = useColorModeValue("white", "gray.700");
  const borderColor = useColorModeValue("gray.200", "gray.600");
  const toast = useToast({
    position: "top",
  });

  const callDeleteItem: any = async (id: number) => {
    console.log("delete with id: ", id);

    const { error } = await deleteItem({ id });
    setopenDelete({ open: false, id: -1000000 });
    if (error)
      toast({
        title: "Item delete failed!",
        variant: "left-accent",
        status: "error",
        isClosable: true,
      });
    else {
      await getStoreItemsAsync({ requestPolicy: "network-only" });
      toast({
        title: "Item deleted successful!",
        variant: "left-accent",
        status: "success",
        isClosable: true,
      });
    }
  };

  return (
    <Flex direction="column" pt={{ base: "120px", md: "75px" }}>
      <DeleteConfirm
        loading={loadingDelete}
        item="Item"
        open={openDelete.open}
        id={openDelete.id!}
        feedback={callDeleteItem}
        nofeedback={() => setopenDelete({ open: false, id: -1000000 })}
      />

      <Flex direction="column" pt={{ base: "120px", md: "75px" }}>
        <Box
          p={5}
          bg={bgColor}
          borderRadius="lg"
          boxShadow="md"
          border="1px solid"
          borderColor={borderColor}
          mb={5}
        >
          <Grid templateColumns="repeat(3, 1fr)" gap={6}>
            <GridItem>
              <Text fontSize="2xl" fontWeight="bold" color={textColor}>
                <Icon as={FaStore} mr={2} />
                Store Details
              </Text>
            </GridItem>
            <GridItem>
              <Text fontSize="lg" fontWeight="bold" color={textColor}>
                <Box as="span" minWidth="120px" display="inline-block">
                  Name:
                </Box>
                {store.name}
              </Text>
              <Text fontSize="md" color={textColor}>
                <Box as="span" minWidth="120px" display="inline-block">
                  Address:
                </Box>
                {store.address}
              </Text>
              <Text fontSize="md" color={textColor}>
                <Box as="span" minWidth="120px" display="inline-block">
                  Primary Store:
                </Box>
                {store.primary ? "Yes" : "No"}
              </Text>
            </GridItem>
          </Grid>
        </Box>
      </Flex>
      <Table variant="simple" color={textColor}>
        <Thead>
          <Tr my=".8rem" pl="0px" color="gray.400">
            <Th pl="0px" borderColor={borderColor} color="gray.400">
              Trade Name
            </Th>
            <Th pl="0px" borderColor={borderColor} color="gray.400">
              Generic Name
            </Th>
            <Th borderColor={borderColor} color="gray.400">
              Item type
            </Th>
            <Th borderColor={borderColor} color="gray.400">
              Stock
            </Th>
            <Th borderColor={borderColor}></Th>
          </Tr>
        </Thead>
        <Tbody>
          {!fetching &&
            data?.getStoreItems &&
            data?.getStoreItems.map((item, index, arr) => {
              console.log("store items: ", item);
              return (
                <ItemsTableRow
                  callOnClick={() => setopenDelete({ open: true, id: item.id })}
                  role={me?.role!.name!}
                  history={history}
                  isLast={index === arr.length - 1 ? true : false}
                  key={item.id}
                  item={item as Item}
                />
              );
            })}
        </Tbody>
      </Table>
    </Flex>
  );
}

export default ViewStore;
