import { Field, ObjectType } from "type-graphql";
import {
  Column,
  <PERSON>tity,
  Join<PERSON><PERSON>umn,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  OneToOne,
} from "typeorm";
import { AuditBaseEntity } from "../AuditEntity";
import { Employee } from "../Employee";
import { Store } from "./Store";
import { Transfer } from "./StockTransfer";
import { Item } from "../Item";
import { Bill } from "../Bill";

@ObjectType()
@Entity()
// this model holds the information for
export class Inventory extends AuditBaseEntity {
  @Field(() => String, { nullable: true })
  @Column({ type: "text", nullable: true })
  details: string; // Optional field to store reason for transfer OR transfer details

  @Field(() => String, { nullable: true })
  @Column({ type: "text", nullable: true })
  customerTag: string; // Optional field to store reason for transfer OR transfer details

  @Field(() => [Item])
  @ManyToMany(() => Item, (item) => item.inventoryTransfers)
  @JoinTable({
    name: "inventory_items",
    joinColumns: [
      { name: "inventoryId", referencedColumnName: "id" },
      { name: "companyId", referencedColumnName: "companyId" },
    ],
    inverseJoinColumns: [
      { name: "itemId", referencedColumnName: "id" },
      { name: "companyId", referencedColumnName: "companyId" },
    ],
  })
  items!: Item[];

  @Field(() => String)
  @Column({
    type: "enum",
    enum: [
      "purchase",
      "dispatch",
      "bill",
      "sale",
      "transfer",
      "writeOff",
      "returnToVendor",
    ], //enum in postgres is case sensitive so mind the caps
    default: "transfer",
  })
  type!: string; //inventory action type, what is done to our inventory

  @Field()
  @Column({ default: false })
  granted: boolean; // used in inventory transfer to show complete permissions granted for the action to be comnpleted

  @Field()
  @Column({ default: false })
  received: boolean; // used in stock purchase and inventory transfer

  @Field()
  @Column({ default: false })
  dispatched: boolean; // used in inventory transfer

  @Field(() => String)
  @Column({ type: "timestamp" })
  transferDate: Date; // purchase date/ transfer date / dispatch date / writeoff date / return to vendor date

  @Field(() => String, { nullable: true })
  @Column({ type: "timestamp", nullable: true })
  returnDate: Date; // transfer return date for capital goods

  @Field(() => String, { nullable: true })
  @Column({ type: "timestamp", nullable: true })
  startDate: Date; // transfer return date for capital goods

  @Field({ nullable: true })
  @Column({ type: "uuid", nullable: true })
  sourceStoreId: string; // where the goods come from

  @Field(() => Store, { nullable: true })
  @ManyToOne(() => Store, (store) => store.stockOut, { nullable: true })
  @JoinColumn([
    { name: "sourceStoreId", referencedColumnName: "id" },
    { name: "companyId", referencedColumnName: "companyId" },
  ])
  sourceStore!: Store;

  @Field({ nullable: true })
  @Column({ type: "uuid", nullable: true })
  destinationStoreId: string; // where the goods are going

  @Field(() => Store, { nullable: true })
  @ManyToOne(() => Store, (store) => store.stockIn, { nullable: true })
  @JoinColumn([
    { name: "destinationStoreId", referencedColumnName: "id" },
    { name: "companyId", referencedColumnName: "companyId" },
  ])
  destinationStore!: Store;

  @Field({ nullable: true })
  @Column({ type: "uuid", nullable: true })
  keeperId: string; // who releases  the items from the store

  @Field(() => Employee, { nullable: true })
  @ManyToOne(() => Employee, (employee) => employee.served_stock, {
    nullable: true,
  })
  @JoinColumn([
    { name: "keeperId", referencedColumnName: "id" },
    { name: "companyId", referencedColumnName: "companyId" },
  ])
  keeper: Employee; // who is issuing the transfer

  @Field({ nullable: true })
  @Column({ type: "uuid", nullable: true })
  consumerId: string; // who receives the items from store or from supplier (receiver of stock transfer action)

  @Field(() => Employee, { nullable: true })
  @ManyToOne(() => Employee, (employee) => employee.received_stock, {
    nullable: true,
  })
  @JoinColumn([
    { name: "consumerId", referencedColumnName: "id" },
    { name: "companyId", referencedColumnName: "companyId" },
  ])
  consumer: Employee; // who is receiving the items / using items

  @Field({ nullable: true })
  @Column({ type: "uuid", nullable: true })
  approverId: string; // who approves the items transfer from the store

  @Field(() => Employee, { nullable: true })
  @ManyToOne(() => Employee, (employee) => employee.approved_stock, {
    nullable: true,
  })
  @JoinColumn([
    { name: "approverId", referencedColumnName: "id" },
    { name: "companyId", referencedColumnName: "companyId" },
  ])
  approver: Employee; // who is approving the transfer

  @Field(() => [Transfer])
  @OneToMany(() => Transfer, (transfer) => transfer.inventoryTransfer)
  transfers!: Transfer[];

  @Field(() => Bill, { nullable: true })
  @OneToOne(() => Bill, (bill) => bill.inventoryTransfer, { nullable: true })
  bill: Bill;
}
