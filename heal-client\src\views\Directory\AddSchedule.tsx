import React from "react";
import {
  Button,
  Flex,
  FormControl,
  FormLabel,
  Grid,
  GridItem,
  Select,
  Text,
  useToast,
  useColorModeValue,
  Box,
  Input,
} from "@chakra-ui/react";
import { useForm, Controller } from "react-hook-form";
import { useLocation, useHistory } from "react-router-dom";
import {
  Clinic,
  Employee,
  useAddSchedulesMutation,
} from "../../generated/graphql"; // GraphQL mutation

// Time options
const timeOptions = [
  { id: "null", t: "Select" },
  { id: "1", t: "01:00:00" },
  { id: "2", t: "02:00:00" },
  { id: "3", t: "03:00:00" },
  { id: "4", t: "04:00:00" },
  { id: "5", t: "05:00:00" },
  { id: "6", t: "06:00:00" },
  { id: "7", t: "07:00:00" },
  { id: "8", t: "08:00:00" },
  { id: "9", t: "09:00:00" },
  { id: "10", t: "10:00:00" },
  { id: "11", t: "11:00:00" },
  { id: "12", t: "12:00:00" },
  { id: "13", t: "13:00:00" },
  { id: "14", t: "14:00:00" },
  { id: "15", t: "15:00:00" },
  { id: "16", t: "16:00:00" },
  { id: "17", t: "17:00:00" },
  { id: "18", t: "18:00:00" },
  { id: "19", t: "19:00:00" },
  { id: "20", t: "20:00:00" },
  { id: "21", t: "21:00:00" },
  { id: "22", t: "22:00:00" },
  { id: "23", t: "23:00:00" },
  { id: "00", t: "00:00:00" },
];

// Days of the week
const daysOfWeek = [
  "MONDAY",
  "TUESDAY",
  "WEDNESDAY",
  "THURSDAY",
  "FRIDAY",
  "SATURDAY",
  "SUNDAY",
];

// Form input type
interface ScheduleFormInput {
  day: string;
  startTime: string;
  endTime: string;
  description: string; // Add description field
}

interface LocationState {
  clinic?: Clinic;
  employee?: Employee;
}

const AddSchedules: React.FC = () => {
  const toast = useToast({
    position: "top",
  });
  const location = useLocation<{ clinic?: Clinic; employee?: Employee }>();
  const history = useHistory();

  if (!location.state) history.push("departments");

  const { clinic, employee } = location.state as LocationState;
  if (
    (!clinic || !clinic.id || clinic.id <= 0) &&
    (!employee || !employee.id || employee.id <= 0)
  )
    history.push("departments");

  const [, addSchedules] = useAddSchedulesMutation(); // Mutation for adding schedules

  // React Hook Form setup
  const {
    handleSubmit,
    control,
    formState: { errors },
  } = useForm({
    defaultValues: {
      schedules: daysOfWeek.map((day) => ({
        day,
        startTime: "Select",
        endTime: "Select",
        description: "", // Initialize description as an empty string
      })),
    },
  });

  // Submit handler for saving schedules
  const onSubmit = async (data: { schedules: ScheduleFormInput[] }) => {
    if (!clinic?.id && !employee?.id) {
      toast({
        title: "Error",
        description: "No clinic or employee specified",
        status: "error",
        isClosable: true,
      });
      return;
    }

    const formattedSchedules = data.schedules
      //   .filter(
      //     (schedule) =>
      //       schedule.startTime !== "Select" && schedule.endTime !== "Select"
      //   )
      .map((schedule) => ({
        day: schedule.day,
        onTime:
          schedule.startTime !== "Select" ? schedule.startTime : "00:00:00", // Already in HH:MM:SS format
        offTime: schedule.endTime !== "Select" ? schedule.endTime : "00:00:00", // Already in HH:MM:SS format
        description: schedule.description || `${schedule.day} shift`, // Use the input description or fallback
      }));

    if (formattedSchedules.length === 0) {
      toast({
        title: "Error",
        description: "Please fill in at least one day schedule",
        status: "error",
        isClosable: true,
      });
      return;
    }

    try {
      const { data } = await addSchedules({
        args: {
          clinicId: clinic?.id,
          employeeId: employee?.id,
          schedules: formattedSchedules,
        },
      });

      if (data?.addSchedules.status) {
        toast({
          title: "Success",
          description: "Schedules added successfully",
          status: "success",
          isClosable: true,
        });
        history.push({
          pathname: "departments",
        }); // Redirect to schedule list or other appropriate page
      } else {
        toast({
          title: "Error",
          description:
            data?.addSchedules.error?.message || "Something went wrong",
          status: "error",
          isClosable: true,
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save schedules",
        status: "error",
        isClosable: true,
      });
    }
  };

  const textColor = useColorModeValue("gray.700", "white");
  const bgForm = useColorModeValue("white", "gray.600");

  return (
    <Flex
      w="100%"
      h="100%"
      alignItems="center"
      justifyContent="center"
      mb="60px"
      mt={{ base: "50px", md: "50px" }}
    >
      <Flex
        direction="column"
        w="80%"
        background="transparent"
        borderRadius="15px"
        p="40px"
        mx={{ base: "100px" }}
        m={{ base: "20px", md: "auto" }}
        bg={bgForm}
        boxShadow={useColorModeValue(
          "0px 5px 14px rgba(0, 0, 0, 0.05)",
          "unset"
        )}
      >
        <Text
          fontSize="xl"
          color={textColor}
          fontWeight="bold"
          textAlign="center"
          mb="12px"
        >
          Add {clinic ? "Clinic" : "Employee"} Schedules
        </Text>

        <form onSubmit={handleSubmit(onSubmit)}>
          <Grid templateColumns="repeat(3, 1fr)" gap={6}>
            {daysOfWeek.map((day, index) => (
              <GridItem key={index} colSpan={3}>
                <Flex alignItems="center" mt={4}>
                  <Box
                    minWidth="150px"
                    display="flex"
                    justifyContent="flex-end"
                    mr={4}
                  >
                    <Text fontWeight="bold">{day}</Text>
                  </Box>

                  <FormControl
                    isInvalid={!!errors.schedules?.[index]?.startTime}
                    mr={4}
                  >
                    <FormLabel
                      htmlFor={`startTime-${index}`}
                      whiteSpace="nowrap"
                    >
                      Start Time
                    </FormLabel>
                    <Controller
                      name={`schedules.${index}.startTime`}
                      control={control}
                      render={({ field }) => (
                        <Select {...field} placeholder="Select Start Time">
                          {timeOptions.map((t) => (
                            <option key={t.id} value={t.t}>
                              {t.t}
                            </option>
                          ))}
                        </Select>
                      )}
                    />
                  </FormControl>

                  <FormControl isInvalid={!!errors.schedules?.[index]?.endTime}>
                    <FormLabel htmlFor={`endTime-${index}`} whiteSpace="nowrap">
                      End Time
                    </FormLabel>
                    <Controller
                      name={`schedules.${index}.endTime`}
                      control={control}
                      render={({ field }) => (
                        <Select {...field} placeholder="Select End Time">
                          {timeOptions.map((t) => (
                            <option key={t.id} value={t.t}>
                              {t.t}
                            </option>
                          ))}
                        </Select>
                      )}
                    />
                  </FormControl>

                  {/* Description Input */}
                  <FormControl mr={4}>
                    <FormLabel
                      htmlFor={`description-${index}`}
                      whiteSpace="nowrap"
                    >
                      Description
                    </FormLabel>
                    <Controller
                      name={`schedules.${index}.description`}
                      control={control}
                      render={({ field }) => (
                        <Input {...field} placeholder="Enter Description" />
                      )}
                    />
                  </FormControl>
                </Flex>
              </GridItem>
            ))}
          </Grid>

          <Button mt="6" colorScheme="teal" w="full" type="submit">
            Save Schedules
          </Button>
        </form>
      </Flex>
    </Flex>
  );
};

export default AddSchedules;
