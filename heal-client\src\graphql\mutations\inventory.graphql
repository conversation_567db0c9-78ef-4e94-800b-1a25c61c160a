# import items by purchase
mutation importItem($args: ImportInput!) {
  importItem(args: $args) {
    ...BooleanResponse
  }
}

# add item
mutation addItem($args: ItemInput!) {
  addItem(args: $args) {
    ...BooleanResponse
  }
}

# add service
mutation addService($args: ServiceInput!) {
  addService(args: $args) {
    ...BooleanResponse
  }
}

# write off item in inventory
mutation writeOffItems($args: [WriteOffInput!]!) {
  writeOffItems(args: $args) {
    ...BooleanResponse
  }
}

# dispatch items
mutation dispatchItems($args: [DispatchInput!]!) {
  dispatchItems(args: $args) {
    ...BooleanResponse
  }
}

# sale items
mutation quickSale($args: [SaleInput!]!) {
  quickSale(args: $args) {
    ...BooleanResponse
  }
}

# sale cashier approval
mutation clearBill($saleId: Float!) {
  clearBill(saleId: $saleId) {
    ...BooleanResponse
  }
}

# transfer items
mutation transferItems($args: [DispatchInput!]!) {
  transferItems(args: $args) {
    ...BooleanResponse
  }
}

# add items
mutation addItemsFromExcel($args: BulkItemInput!) {
  addItemsFromExcel(args: $args) {
    ...BooleanResponse
  }
}

# edit item
mutation editItem($args: ItemInput!, $id: Float!) {
  editItem(id: $id, args: $args) {
    ...BooleanResponse
  }
}

# edit service
mutation editService($args: ServiceInput!, $id: Float!) {
  editService(id: $id, args: $args) {
    ...BooleanResponse
  }
}

#delete item
mutation deleteItem($id: Float!) {
  deleteItem(id: $id) {
    ...BooleanResponse
  }
}

# approve transfer
mutation changeInventoryApprovalStatus($inventoryId: Float!) {
  changeInventoryApprovalStatus(inventoryId: $inventoryId) {
    ...BooleanResponse
  }
}

# dispatch transfer
mutation changeInventoryDispatchedStatus($inventoryId: Float!) {
  changeInventoryDispatchedStatus(inventoryId: $inventoryId) {
    ...BooleanResponse
  }
}

# sale items
mutation changeInventorySoldStatus($inventoryId: Float!) {
  changeInventorySoldStatus(inventoryId: $inventoryId) {
    ...BooleanResponse
  }
}

# receive transfer
mutation changeInventoryReceivedStatus($inventoryId: Float!) {
  changeInventoryReceivedStatus(inventoryId: $inventoryId) {
    ...BooleanResponse
  }
}
