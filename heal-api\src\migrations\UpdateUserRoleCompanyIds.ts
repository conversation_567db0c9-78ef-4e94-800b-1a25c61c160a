import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateUserRoleCompanyIds1234567890123
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // First, update existing records to ensure company IDs match
    await queryRunner.query(`
      UPDATE "user" u
      SET "companyId" = (
          SELECT r."companyId"
          FROM role r
          WHERE r.id = u."roleId"
      )
      WHERE u."companyId" IS NULL
      OR u."companyId" != (
          SELECT r."companyId"
          FROM role r
          WHERE r.id = u."roleId"
      );
    `);

    // After data is fixed, add the constraint
    await queryRunner.query(`
      ALTER TABLE "user"
      ADD CONSTRAINT "FK_user_role_company"
      FOREIGN KEY ("roleId", "companyId")
      REFERENCES "role"("id", "companyId")
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "user"
      DROP CONSTRAINT "FK_user_role_company"
    `);
  }
}
