import { Field, ObjectType } from "type-graphql";
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToMany,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { Employee } from "./Employee";
import { Feature } from "./Feature";
import { User } from "./User";
import { Department } from "./Department";
import { SyncHistory } from "./SyncHistory";
import { Payment } from "./Payment";

@ObjectType()
@Entity()
export class Company extends BaseEntity {
  @Field(() => String)
  @PrimaryGeneratedColumn("uuid") // ← generates UUID automatically
  id!: string;

  @Field(() => String)
  @Column({ type: "uuid", nullable: true })
  companyId!: string;

  @Field(() => String)
  @CreateDateColumn({ type: "timestamp" })
  createdAt = new Date();

  @Field(() => String)
  @UpdateDateColumn({ type: "timestamp" })
  updatedAt = new Date();

  @Field(() => Boolean)
  @Column({ type: "boolean", default: false })
  deleted: boolean;

  @Field(() => [Employee])
  @OneToMany(() => Employee, (employee) => employee.company)
  employees: Employee[];

  @Field(() => [Department])
  @OneToMany(() => Department, (department) => department.company)
  departments: Department[];

  @Field(() => Boolean)
  @Column({ default: false })
  isParent: boolean;

  @Field(() => Boolean)
  @Column({ default: false })
  isBranch: boolean;

  @Field(() => String, { nullable: true })
  @Column({ type: "uuid", nullable: true })
  parentId: string;

  @Field(() => [String], { nullable: true })
  @Column({ type: "uuid", array: true, nullable: true })
  branches: string[];

  @Field(() => String)
  @Column({ unique: true, type: "text" })
  name: string;

  @Field(() => String, { nullable: true })
  @Column({ type: "text", nullable: true })
  syncUrl: string;

  @Field(() => String)
  @Column({ type: "text" })
  tinNumber: string;

  @Field(() => String)
  @Column({ type: "text" })
  registrationNumber: string;

  @Field(() => String)
  @Column({ type: "text" })
  type: string;

  @Field(() => String, { nullable: true })
  @Column({ type: "text", nullable: true })
  phone: string;

  @Field(() => String, { nullable: true })
  @Column({ type: "text", nullable: true })
  email: string;

  @Field(() => String, { nullable: true })
  @Column({ type: "text", nullable: true })
  poBox: string;

  @Field(() => String, { nullable: true })
  @Column({ type: "text", nullable: true })
  logo: string;

  @Field(() => String, { nullable: true })
  @Column({ type: "text", nullable: true })
  website: string;

  @Field(() => [User], { nullable: true })
  @OneToMany(() => User, (user) => user.company)
  users: User[];

  @Field(() => String)
  @Column({ type: "text" })
  location: string;

  @Field(() => [Feature], { nullable: true })
  @ManyToMany(() => Feature, (feature) => feature.companies)
  features: Feature[];

  @Field(() => [SyncHistory], { nullable: true })
  @OneToMany(() => SyncHistory, (syncHistory) => syncHistory.company)
  syncHistory: SyncHistory[];

  @Field(() => [Payment], { nullable: true })
  @OneToMany(() => Payment, (payment) => payment.company)
  payments: Payment[];
}
