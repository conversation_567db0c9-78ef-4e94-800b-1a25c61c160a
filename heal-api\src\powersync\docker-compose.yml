version: "3.8"
services:
  api:
    image: samxtu/talisia-api:latest
    env_file: ./.env
    ports:
      - "${HTTPS_PORT:-5823}:5823"
    environment:
      - NODE_ENV=${NODE_ENV:-production}
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - DB_TYPE=${DB_TYPE}
      - COOKIE_NAME=${COOKIE_NAME}
      - CUSTOM_SECRET=${CUSTOM_SECRET}
      - MY_SENDER_EMAIL=${MY_SENDER_EMAIL}
      - MY_SENDER_EMAIL_PASSWORD=${MY_SENDER_EMAIL_PASSWORD}
      - MY_SSL_EMAIL_SERVER=${MY_SSL_EMAIL_SERVER}
      - FRONT_END_ORIGIN=${FRONTEND_WEB_ORIGIN}
    volumes:
      - ./certificates:/usr/app/certificates
    depends_on:
      - postgres
      - redis
    healthcheck:
      test:
        [
          "CMD",
          "wget",
          "--no-verbose",
          "--tries=1",
          "--spider",
          "http://localhost:5823/health",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    restart: unless-stopped

  postgres:
    image: postgres:latest
    env_file: ./.env
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  redis:
    image: redis:alpine
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  powersync:
    image: journeyapps/powersync-service:latest
    ports:
      - "${POWERSYNC_PORT:-8080}:8080"
    environment:
      # PowerSync environment variables for config substitution
      - PS_POSTGRES_USER=${POSTGRES_USER}
      - PS_POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - PS_POSTGRES_DB=${POSTGRES_DB}
      - PS_STORAGE_USER=${PS_STORAGE_USER:-powersync_storage_user}
      - PS_STORAGE_PASSWORD=${PS_STORAGE_PASSWORD:-${POSTGRES_PASSWORD}}
      - PS_STORAGE_DB=${PS_STORAGE_DB:-${POSTGRES_DB}}
      - PS_JWT_SECRET=${PS_JWT_SECRET:-TaLiS1a_P0w3rSyNc_JWT_S3cr3t_K3y_2025_Pr0duCt10n_V3ry_L0ng_4nd_S3cur3_K3y}
      - PS_PORT=${POWERSYNC_PORT:-8080}
      - PS_LOG_LEVEL=${POWERSYNC_LOG_LEVEL:-info}
    volumes:
      - ./powersync/config.yaml:/app/powersync.yaml
      - powersync_data:/powersync-data
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test:
        [
          "CMD",
          "wget",
          "--no-verbose",
          "--tries=1",
          "--spider",
          "http://localhost:8080/",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  powersync_data:
