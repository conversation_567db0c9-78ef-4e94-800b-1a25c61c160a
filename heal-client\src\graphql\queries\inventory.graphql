# get all items
query getAllItems {
  getAllItems {
    id
    name
    type
    description
    image
    sellingPrice
    pieceSellingPrice
    subPieceSellingPrice
    reorder
    reference
    internal
    unit
    pieceUnit
    subPieceUnit
    stock
    pieceStock
    subPieceStock
    pieces
    subPieces
  }
}

# get all services
query getAllServices {
  getAllServices {
    id
    name
    description
    sellingPrice
    reference
  }
}

# get all items
query getStoreItems($storeId: Float!) {
  getStoreItems(storeId: $storeId) {
    id
    name
    type
    image
    description
    sellingPrice
    reorder
    reference
    internal
    unit
    pieceUnit
    subPieceUnit
    pieces
    subPieces
    stock
    pieceStock
    subPieceStock
  }
}

# get internal items
query getInternalItems {
  getInternalItems {
    id
    name
    type
    image
    description
    reorder
    reference
    internal
    unit
    stock
    pieceUnit
    pieceStock
    subPieceUnit
    subPieceStock
    pieces
    subPieces
  }
}

# get merchandise items
query getMerchandiseItems {
  getMerchandiseItems {
    id
    name
    type
    image
    description
    sellingPrice
    pieceSellingPrice
    subPieceSellingPrice
    reorder
    reference
    internal
    unit
    stock
    pieceUnit
    pieceStock
    subPieceUnit
    subPieceStock
    pieces
    subPieces
  }
}

# get one item
query getItem($id: Float!) {
  getItem(id: $id) {
    id
    name
    type
    image
    description
    internal
    reorder
    reference
    internal
    unit
    stock
    sellingPrice
    pieceSellingPrice
    subPieceSellingPrice
    pieceStock
    pieceUnit
    subPieceStock
    subPieceUnit
    pieces
    subPieces
    imports {
      id
      importDate
      supplier
      quantity
      importPrice
      sellingPrice
    }
    inventoryTransfers {
      id
      type
      details
      transferDate
      sourceStoreId
      destinationStoreId
    }
    transfers {
      id
      inventoryId
      itemId
      quantity
      pieceQuantity
      subPieceQuantity
    }
  }
}

# get sales
query getSales {
  getSales {
    id
    createdAt
    details
    type
    granted
    dispatched
    sourceStoreId
    keeper {
      id
      userId
    }
    approver {
      id
      userId
    }
    bill {
      amount
      cleared
      paymentType
    }
    transfers {
      id
      itemId
      quantity
      pieceQuantity
      subPieceQuantity
      details
      batch
    }
    items {
      id
      name
      unit
      pieceUnit
      subPieceUnit
      pieces
      subPieces
      sellingPrice
      pieceSellingPrice
      subPieceSellingPrice
      type
      reference
      description
    }
  }
}

# get all company stores
query getStores {
  getStores {
    id
    name
    primary
    address
    storeKeepers {
      id
    }
  }
}

# get all inventory actions/transfers
query getInventoryTransfers($type: String) {
  getInventoryTransfers(type: $type) {
    id
    details
    type
    granted
    received
    transferDate
    sourceStore {
      id
      name
    }
    destinationStore {
      id
      name
    }
  }
}

#get one inventory action/transfer
query getInventoryTransfer($id: Float!) {
  getInventoryTransfer(id: $id) {
    id
    details
    type
    granted
    received
    transferDate
    sourceStore {
      id
      name
    }
    destinationStore {
      id
      name
    }
    keeper {
      id
      userId
    }
    consumer {
      id
      userId
    }
    transfers {
      id
      itemId
      quantity
      pieceQuantity
      subPieceQuantity
    }
    items {
      id
      name
      unit
      pieceUnit
      subPieceUnit
      sellingPrice
      pieceSellingPrice
      subPieceSellingPrice
      type
      pieces
      subPieces
    }
  }
}

# get items transfers
query getItemTransfers($itemId: Float!, $type: String) {
  getItemTransfers(itemId: $itemId, type: $type) {
    id
    details
    quantity
    pieceQuantity
    subPieceQuantity
    batch
    inventoryTransfer {
      id
      updatedAt
      details
      type
      transferDate
      sourceStoreId
      destinationStoreId
    }
    item {
      id
      name
      unit
      pieceUnit
      subPieceUnit
    }
  }
}

#  query write offs
query GetWriteOffsByCompany {
  getWriteOffsByCompany {
    id
    createdAt
    quantity
    pieceQuantity
    subPieceQuantity
    details
    item {
      id
      name
      unit
      pieceUnit
      subPieceUnit
    }
  }
}

# query dispatches
query getDispatches {
  getDispatches {
    id
    updatedAt
    transferDate
    granted
    dispatched
    received
    sourceStore {
      id
      name
    }
    destinationStore {
      id
      name
    }
    items {
      id
      name
      unit
      pieceUnit
      subPieceUnit
    }
    keeperId
    consumerId
    transfers {
      id
      itemId
      quantity
      pieceQuantity
      subPieceQuantity
      batch
    }
  }
}

# query transfers
query getTransfers {
  getTransfers {
    id
    updatedAt
    transferDate
    granted
    dispatched
    received
    sourceStore {
      id
      name
    }
    destinationStore {
      id
      name
    }
    items {
      id
      name
      unit
      pieceUnit
      subPieceUnit
    }
    keeperId
    consumerId
    transfers {
      id
      itemId
      quantity
      pieceQuantity
      subPieceQuantity
      batch
    }
  }
}

#get batches for one item
query getItemBatchStocks($itemId: Float!) {
  getItemBatchStocks(itemId: $itemId) {
    id
    batch
    expireDate
    stock
    pieceStock
    subPieceStock
    storeItemStocks {
      id
      storeId
      stock
      pieceStock
      subPieceStock
    }
  }
}

# get batches for a store with stock
query getBatchStockForStore($itemId: Float, $storeId: Float) {
  getBatchStockForStore(itemId: $itemId, storeId: $storeId) {
    itemId
    batch
    expireDate
    stock
    pieceStock
    subPieceStock
    storeItemStocks {
      storeId
      stock
      pieceStock
      subPieceStock
    }
  }
}

#get stores stocks for one item
query getItemStoreStocks($itemId: Float!) {
  getItemStoreStocks(itemId: $itemId) {
    id
    storeId
    batchId
    stock
    pieceStock
    subPieceStock
    store {
      id
      name
    }
  }
}

query getItemBatchImports($itemId: Float!) {
  getItemBatchImports(itemId: $itemId) {
    id
    importPrice
    batch
  }
}
