import React, { useContext, useEffect, useState } from "react";
import { useForm } from "react-hook-form";

import {
  Flex,
  Button,
  FormControl,
  FormLabel,
  Input,
  Text,
  useColorModeValue,
  FormErrorMessage,
  useToast,
  Grid,
  GridItem,
  Box,
  Icon,
  Stack,
  Spinner,
  Progress,
  Tag,
  TagLabel,
} from "@chakra-ui/react";
import {
  Item,
  useGetAllItemsQuery,
  useImportItemMutation,
} from "../../generated/graphql";
import { useLocation } from "react-router-dom";
import { HSeparator } from "../../components/Separator/Separator";
import FileInput from "../../components/FileInput";
import { MdCheck } from "react-icons/md";
import { formatToMoney } from "../../utils/Helpers";
import AllItemsInput from "./components/AllItemsInput";
import UnitInput from "./components/UnitInput";
import { AppContext } from "../../AppContext";
import { ActionType } from "../../interfaces/Types";

interface IImportItemProps {}

interface IRecentImports {
  name: string;
  quantity: number;
  importPrice: number;
  sellingPrice: number;
  unit: string;
}

interface IRecentImport {
  name: string;
  quantity: number;
  importPrice: number;
  sellingPrice: number;
  unit: string;
  batch: string;
  loading: boolean;
}

interface State {
  item: Item;
}

const ImportItem: React.FC<IImportItemProps> = () => {
  const toast = useToast({
    position: "top",
  });

  const { dispatch, state } = useContext(AppContext);

  const bgColor = useColorModeValue("#F8F9FA", "navy.900");
  const nameColor = useColorModeValue("gray.500", "white");

  const [, importItem] = useImportItemMutation();
  const [{ data: items }] = useGetAllItemsQuery();
  const [error, seterror] = useState("");
  const location = useLocation();
  const [clearFileInput, setClearFileInput] = useState(false);
  const [receiptUrl, setReceiptUrlState] = useState("");
  const [fileLoading, setFileLoading] = useState(false);
  const [fileInputError, setFileInputError] = useState("");
  const [imported, setImported] = useState<IRecentImports[]>([]);
  const [importing, setImporting] = useState<IRecentImport | null>(null);
  const [selectedItem, setSelectedItem] = useState<number | null>(null);

  const [unitType, setUnitType] = useState("");
  const locationState = location.state as State;
  const [itemType, setItemType] = useState(
    locationState?.item?.type ? locationState.item.type : ""
  );

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
    setValue,
  } = useForm();

  useEffect(() => {
    if (
      state.pendingFile?.path === location.pathname &&
      state.pendingFile.uploadingFileUrl
    )
      setReceiptUrlState(state.pendingFile.uploadingFileUrl);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleItemChange = async (value: any) => {
    if (items && value) {
      setSelectedItem(value.value);
      setValue("itemId", value.value);
      const ourItem = items?.getAllItems.find(
        (item) => item.id === value.value
      );
      if (ourItem) setItemType(ourItem.type);
    } else setSelectedItem(null);
    reset();
  };

  const handleUnitChange: any = (value: any) => {
    if (value) {
      setUnitType(value.value);
      setValue("unit", value.value);
    } else {
      setUnitType("");
    }
  };

  const setReceiptUrl: any = (url: string) => {
    setReceiptUrlState(url);
    dispatch({
      type: ActionType.SET_UPLOADING_FILE_URL,
      pendingFile: {
        uploadingFileUrl: url,
        path: location.pathname,
      },
    });
  };

  async function onSubmit(values: any) {
    seterror("");
    const args = {
      itemId: Number(selectedItem),
      supplier: values.supplier,
      importDate: values.importDate,
      expireDate: values.expireDate,
      unit: unitType,
      quantity: Number(values.quantity),
      importPrice: Number(values.importPrice) ? Number(values.importPrice) : 0,
      sellingPrice: Number(values.sellingPrice)
        ? Number(values.sellingPrice)
        : 0,
      pieceSellingPrice: Number(values.pieceSellingPrice)
        ? Number(values.pieceSellingPrice)
        : 0,
      subPieceSellingPrice: Number(values.subPieceSellingPrice)
        ? Number(values.subPieceSellingPrice)
        : 0,
      receipt: receiptUrl,
      batch: values.batch,
    };
    setImporting({
      name: items?.getAllItems.find((item) => item.id === Number(selectedItem))!
        .name
        ? items?.getAllItems.find((item) => item.id === Number(selectedItem))!
            .name
        : "",
      quantity: args.quantity,
      unit: args.unit,
      importPrice: args.importPrice,
      sellingPrice: args.sellingPrice,
      batch: values.batch,
      loading: true,
    });
    const { data } = await importItem({ args });
    if (data?.importItem.error) {
      console.log("The error cam eback: ", data.importItem.error.message);
      return seterror(data?.importItem.error.message);
    } else if (!data?.importItem.error) {
      reset();
      setClearFileInput(true);
      setImporting(null);
      setImported((importedItems) => {
        return [
          {
            name: items?.getAllItems.find(
              (item) => item.id === Number(selectedItem)
            )!.name!,
            quantity: args.quantity,
            unit: args.unit,
            importPrice: args.importPrice,
            batch: args.batch,
            sellingPrice: args.sellingPrice,
          },
          ...importedItems,
        ];
      });
      setImporting(null);
      return toast({
        title: `${
          args.quantity +
          " " +
          items?.getAllItems.find((item) => item.id === args.itemId)?.unit +
          " of " +
          items?.getAllItems.find((item) => item.id === args.itemId)?.name
        } was imported successfully!`,
        variant: "left-accent",
        status: "success",
        isClosable: true,
      });
    }
  }

  // Chakra color mode
  const textColor = useColorModeValue("gray.700", "white");
  const bgForm = useColorModeValue("white", "navy.800");
  const subtitleTextColor = useColorModeValue("black.400", "whiteAlpha.700");

  const fileError = (value: string): void => {
    setFileInputError(value);
  };

  return (
    <Flex position="relative" mb="90px">
      <Flex
        // minH={{ md: "1000px" }}
        h={{ sm: "initial", md: "75vh", lg: "85vh" }}
        w="100%"
        // maxW="1044px"
        mx="auto"
        justifyContent="space-between"
        mb="60px"
        pt={{ md: "0px" }}
      >
        <Flex
          w="100%"
          as={Grid}
          templateColumns="repeat(3, 1fr)"
          h="100%"
          alignItems="center"
          justifyContent="space-between"
          mb="60px"
          mt={{ base: "50px", md: "50px" }}
        >
          <Flex
            zIndex="2"
            direction="column"
            as={GridItem}
            colSpan={2}
            // w="600px"
            minW={600}
            background="transparent"
            borderRadius="15px"
            p="40px"
            mx={{ base: "100px" }}
            m={{ base: "20px", md: "auto" }}
            bg={bgForm}
            boxShadow={useColorModeValue(
              "0px 5px 14px rgba(0, 0, 0, 0.05)",
              "unset"
            )}
          >
            <Text
              fontSize="xl"
              color={textColor}
              fontWeight="bold"
              textAlign="center"
              mb="12px"
            >
              Import Items
            </Text>
            <HSeparator mb="22px" mt="6px" />
            {error && (
              <Text mb="10px" color="red.500" textColor="red.300">
                {error}
              </Text>
            )}
            <form onSubmit={handleSubmit(onSubmit)}>
              <Grid templateColumns="repeat(4, 1fr)" gap={4}>
                <FormControl
                  as={GridItem}
                  colSpan={4}
                  alignItems="center"
                  mb={1}
                  fontSize="xs"
                  isInvalid={errors.item as any}
                >
                  <AllItemsInput
                    onChange={handleItemChange}
                    selectedItemsIds={[]}
                  />
                  <FormErrorMessage fontSize="xs">
                    {errors.item && (errors.item.message as any)}
                  </FormErrorMessage>
                </FormControl>
                {itemType !== "" && (
                  <>
                    <FormControl
                      as={GridItem}
                      colSpan={2}
                      mb={1}
                      isInvalid={errors.importDate as any}
                    >
                      <FormLabel htmlFor="importDate" fontSize="xs">
                        Import Date
                      </FormLabel>
                      <Input
                        variant="filled"
                        ms="4px"
                        type="date"
                        fontSize="xs"
                        placeholder="Import date"
                        mb="2px"
                        size="lg"
                        id="importDate"
                        {...register("importDate", {
                          required: "This is required",
                        })}
                      />
                      <FormErrorMessage mb="10px" fontSize="xs">
                        {errors.importDate &&
                          (errors.importDate.message as any)}
                      </FormErrorMessage>
                    </FormControl>
                    <FormControl
                      as={GridItem}
                      colSpan={2}
                      mb={1}
                      isInvalid={errors.expireDate as any}
                    >
                      <FormLabel htmlFor="expireDate" fontSize="xs">
                        Item{" "}
                        {items?.getAllItems.find(
                          (item) => item.id === Number(selectedItem)
                        )?.type === "consumable"
                          ? "Expire Date"
                          : "Maintenance Date"}
                      </FormLabel>
                      <Input
                        variant="filled"
                        fontSize="xs"
                        ms="4px"
                        type="date"
                        placeholder="Item Description"
                        mb="2px"
                        size="lg"
                        id="expireDate"
                        {...register("expireDate", {
                          required: "This is required",
                        })}
                      />
                      <FormErrorMessage fontSize="xs">
                        {errors.expireDate &&
                          (errors.expireDate.message as any)}
                      </FormErrorMessage>
                    </FormControl>
                    <FormControl
                      as={GridItem}
                      colSpan={2}
                      mb={1}
                      isInvalid={errors.supplier as any}
                    >
                      <FormLabel htmlFor="supplier" fontSize="xs">
                        Item Supplier
                      </FormLabel>
                      <Input
                        variant="filled"
                        fontSize="xs"
                        ms="4px"
                        type="text"
                        placeholder="Eg: Supplier Info"
                        mb="2px"
                        size="lg"
                        id="supplier"
                        {...register("supplier", {
                          required: "This is required",
                        })}
                      />
                      <FormErrorMessage fontSize="xs">
                        {errors.supplier && (errors.supplier.message as any)}
                      </FormErrorMessage>
                    </FormControl>
                    <FormControl
                      as={GridItem}
                      colSpan={2}
                      mb={1}
                      isInvalid={errors.batch as any}
                    >
                      <FormLabel htmlFor="batch" fontSize="xs">
                        Item Batch Number / Lot Number
                      </FormLabel>
                      <Input
                        variant="filled"
                        fontSize="xs"
                        ms="4px"
                        type="text"
                        placeholder="Eg: Batch No. / Lot No."
                        mb="2px"
                        size="lg"
                        id="batch"
                        {...register("batch", {
                          required: "This is required",
                        })}
                      />
                      <FormErrorMessage fontSize="xs">
                        {errors.batch && (errors.batch.message as any)}
                      </FormErrorMessage>
                    </FormControl>
                    <FormControl
                      as={GridItem}
                      colSpan={4}
                      mb={1}
                      isInvalid={
                        (errors.quantity as any) || (errors.unit as any)
                      }
                    >
                      <Grid templateColumns="repeat(2, 1fr)" gap={2}>
                        <Box>
                          <FormLabel htmlFor="quantity" fontSize="xs">
                            Import Quantity
                          </FormLabel>
                          <Input
                            variant="filled"
                            fontSize="xs"
                            ms="4px"
                            type="number"
                            placeholder="Imported quantity"
                            mb="2px"
                            size="lg"
                            id="quantity"
                            {...register("quantity", {
                              required: "This is required",
                            })}
                          />
                          <FormErrorMessage fontSize="xs">
                            {errors.quantity &&
                              (errors.quantity.message as any)}
                          </FormErrorMessage>
                        </Box>
                        <Box>
                          <FormLabel htmlFor="unit" fontSize="xs">
                            Select Unit
                          </FormLabel>
                          <UnitInput
                            onChange={handleUnitChange}
                            selectedItem={
                              items?.getAllItems.find(
                                (item) => item.id === selectedItem
                              ) as Item
                            }
                          />
                          <FormErrorMessage fontSize="xs">
                            {errors.unit && (errors.unit.message as any)}
                          </FormErrorMessage>
                        </Box>
                      </Grid>
                    </FormControl>

                    <FormControl
                      as={GridItem}
                      colSpan={2}
                      mb={1}
                      isInvalid={errors.importPrice as any}
                    >
                      <FormLabel htmlFor="importPrice" fontSize="xs">
                        Import Price Per Item
                      </FormLabel>
                      <Input
                        variant="filled"
                        fontSize="xs"
                        ms="4px"
                        type="number"
                        placeholder="Import price per item"
                        mb="2px"
                        size="lg"
                        id="importPrice"
                        {...register("importPrice", {
                          required: "This is required",
                        })}
                      />
                      <FormErrorMessage fontSize="xs">
                        {errors.importPrice &&
                          (errors.importPrice.message as any)}
                      </FormErrorMessage>
                    </FormControl>
                    {items?.getAllItems.find(
                      (item) => item.id === Number(itemType)
                    )?.internal ? (
                      <Tag size="md" variant="subtle" colorScheme="green">
                        <TagLabel>
                          Imported for internal operations use
                        </TagLabel>
                      </Tag>
                    ) : (
                      <>
                        <FormControl
                          as={GridItem}
                          colSpan={2}
                          mb={1}
                          isInvalid={errors.sellingPrice as any}
                        >
                          <FormLabel htmlFor="sellingPrice" fontSize="xs">
                            Selling Price Per Item
                          </FormLabel>
                          <Input
                            variant="filled"
                            fontSize="xs"
                            ms="4px"
                            type="number"
                            placeholder="Selling price per item"
                            mb="2px"
                            size="lg"
                            id="sellingPrice"
                            {...register("sellingPrice", {
                              required: "This is required",
                            })}
                          />
                          <FormErrorMessage fontSize="xs">
                            {errors.sellingPrice &&
                              (errors.sellingPrice.message as any)}
                          </FormErrorMessage>
                        </FormControl>
                        <FormControl
                          as={GridItem}
                          colSpan={2}
                          mb={1}
                          isInvalid={errors.pieceSellingPrice as any}
                        >
                          <FormLabel htmlFor="pieceSellingPrice" fontSize="xs">
                            Selling Price Per Item Piece
                          </FormLabel>
                          <Input
                            variant="filled"
                            fontSize="xs"
                            ms="4px"
                            type="number"
                            placeholder="Selling price per item piece"
                            mb="2px"
                            size="lg"
                            id="pieceSellingPrice"
                            {...register("pieceSellingPrice")}
                          />
                          <FormErrorMessage fontSize="xs">
                            {errors.pieceSellingPrice &&
                              (errors.pieceSellingPrice.message as any)}
                          </FormErrorMessage>
                        </FormControl>
                        <FormControl
                          as={GridItem}
                          colSpan={2}
                          mb={1}
                          isInvalid={errors.subPieceSellingPrice as any}
                        >
                          <FormLabel
                            htmlFor="subPieceSellingPrice"
                            fontSize="xs"
                          >
                            Selling Price Per Subpiece Item
                          </FormLabel>
                          <Input
                            variant="filled"
                            fontSize="xs"
                            ms="4px"
                            type="number"
                            placeholder="Selling price per subpiece item"
                            mb="2px"
                            size="lg"
                            id="subPieceSellingPrice"
                            {...register("subPieceSellingPrice")}
                          />
                          <FormErrorMessage fontSize="xs">
                            {errors.subPieceSellingPrice &&
                              (errors.subPieceSellingPrice.message as any)}
                          </FormErrorMessage>
                        </FormControl>
                      </>
                    )}

                    <FormControl
                      as={GridItem}
                      colSpan={4}
                      mb={1}
                      isInvalid={fileInputError === "" ? false : true}
                    >
                      <FormLabel htmlFor="receipt" fontSize="xs">
                        Item Import Receipt
                      </FormLabel>

                      <FileInput
                        setFileUrl={setReceiptUrl}
                        defaultUrl={receiptUrl}
                        label="Receipt File"
                        clear={clearFileInput}
                        setError={fileError}
                        defaultClear={(value) => setClearFileInput(value)}
                        setLoading={(value) => setFileLoading(value)}
                      />
                      <FormErrorMessage fontSize="xs">
                        {fileInputError}
                      </FormErrorMessage>
                    </FormControl>
                  </>
                )}
              </Grid>

              <Button
                fontSize="14px"
                variant="dark"
                fontWeight="bold"
                w="100%"
                h="45"
                mb="24px"
                mt={4}
                colorScheme="teal"
                isLoading={isSubmitting}
                isDisabled={itemType === "" || fileLoading}
                type="submit"
              >
                Import Item
              </Button>
            </form>
          </Flex>
          <Flex
            zIndex="2"
            direction="column"
            w={400}
            as={GridItem}
            colSpan={1}
            background="transparent"
            borderRadius="15px"
            p="20px"
            mx={{ base: "100px" }}
            m={{ base: "20px", md: "auto" }}
            bg={bgForm}
            boxShadow={useColorModeValue(
              "0px 5px 14px rgba(0, 0, 0, 0.05)",
              "unset"
            )}
          >
            <Text
              fontSize="xl"
              color={textColor}
              fontWeight="bold"
              textAlign="center"
              mb="12px"
            >
              Recently Imported Items
            </Text>
            <HSeparator mb="22px" mt="6px" />

            <Flex direction="column" w="100%">
              {importing && !importing.loading && (
                <Box p={5} bg={bgColor} my="5px" borderRadius="12px">
                  <Flex justify="space-between" w="100%">
                    <Flex maxWidth="70%" flexDirection="column">
                      <Text color={subtitleTextColor} fontSize="m">
                        Item:
                      </Text>
                      <Text color={nameColor} fontSize="m" fontWeight={"bold"}>
                        {importing.name}
                      </Text>
                    </Flex>
                    <Flex
                      direction={{ sm: "column", md: "column" }}
                      align="flex-start"
                    >
                      <Text color={subtitleTextColor} fontSize="m">
                        Quantity:
                      </Text>
                      <Text color={nameColor} fontSize="m" fontWeight={"bold"}>
                        {importing.quantity + " " + importing.unit}
                      </Text>
                    </Flex>
                  </Flex>
                  <Flex justify={"center"}>
                    <Stack direction={"row"} h={3}>
                      {" "}
                      <Progress
                        size="s"
                        w={10}
                        isIndeterminate
                        style={{ transform: "rotate(180deg)" }}
                      />
                      <Spinner size="s" />
                      <Progress w={10} size="s" isIndeterminate />
                    </Stack>
                  </Flex>
                  <Flex justify="space-between" w="100%">
                    <Flex maxWidth="70%" flexDirection="column">
                      <Text color={subtitleTextColor} fontSize="m">
                        Import Price:
                      </Text>
                      <Text color={nameColor} fontSize="m" fontWeight={"bold"}>
                        {formatToMoney(importing.importPrice)}
                      </Text>
                    </Flex>
                    <Flex
                      direction={{ sm: "column", md: "column" }}
                      align="flex-start"
                    >
                      <Text color={subtitleTextColor} fontSize="m">
                        Selling Price:
                      </Text>
                      <Text color={nameColor} fontSize="m" fontWeight={"bold"}>
                        {formatToMoney(importing.importPrice)}
                      </Text>
                    </Flex>
                  </Flex>
                </Box>
              )}
              {imported.length > 0 ? (
                imported.map((row) => {
                  return (
                    <Box p={5} bg={bgColor} my="5px" borderRadius="12px">
                      <Flex justify="space-between" w="100%">
                        <Flex maxWidth="70%" flexDirection="column">
                          <Text color={subtitleTextColor} fontSize="m">
                            Item:
                          </Text>
                          <Text
                            color={nameColor}
                            fontSize="m"
                            fontWeight={"bold"}
                          >
                            {row.name}
                          </Text>
                        </Flex>
                        <Flex
                          direction={{ sm: "column", md: "column" }}
                          align="flex-start"
                        >
                          <Text color={subtitleTextColor} fontSize="m">
                            Quantity:
                          </Text>
                          <Text
                            color={nameColor}
                            fontSize="m"
                            fontWeight={"bold"}
                          >
                            {row.quantity + " " + row.unit}
                          </Text>
                        </Flex>
                      </Flex>
                      <Flex justify={"center"}>
                        <Box
                          w="25px"
                          h="25px"
                          borderRadius="50%"
                          bg="green.500"
                          position="relative"
                        >
                          <Box
                            w="20px"
                            h="3px"
                            bg="green.500"
                            position="absolute"
                            top="50%"
                            left="-50%"
                          />
                          <Icon
                            as={MdCheck}
                            w="20px"
                            h="20px"
                            color="white"
                            position="absolute"
                            top="50%"
                            left="50%"
                            transform="translate(-50%, -50%)"
                          />
                          <Box
                            w="20px"
                            h="3px"
                            bg="green.500"
                            position="absolute"
                            top="50%"
                            right="-50%"
                          />
                        </Box>
                      </Flex>
                      <Flex justify="space-between" w="100%">
                        <Flex maxWidth="70%" flexDirection="column">
                          <Text color={subtitleTextColor} fontSize="m">
                            Import Price:
                          </Text>
                          <Text
                            color={nameColor}
                            fontSize="m"
                            fontWeight={"bold"}
                          >
                            {formatToMoney(row.importPrice)}
                          </Text>
                        </Flex>
                        <Flex
                          direction={{ sm: "column", md: "column" }}
                          align="flex-start"
                        >
                          <Text color={subtitleTextColor} fontSize="m">
                            Selling Price:
                          </Text>
                          <Text
                            color={nameColor}
                            fontSize="m"
                            fontWeight={"bold"}
                          >
                            {formatToMoney(row.sellingPrice)}
                          </Text>
                        </Flex>
                      </Flex>
                    </Box>
                  );
                })
              ) : (
                <Text pl={3}>You have not imported items on this page!</Text>
              )}
            </Flex>
          </Flex>
        </Flex>
      </Flex>
    </Flex>
  );
};

export default ImportItem;
