import {
  <PERSON>ton,
  I<PERSON>,
  <PERSON>dal,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  ModalOverlay,
} from "@chakra-ui/react";
import React, { useEffect } from "react";
import { FaBitbucket } from "react-icons/fa";
import { BsFillDashCircleFill } from "react-icons/bs";
import { IoMdCheckmarkCircleOutline } from "react-icons/io";

interface IDeleteConfirmProps {
  open: boolean;
  item: string;
  feedback: (id: number) => void;
  nofeedback: () => void;
  id: number;
  loading: boolean;
}

// Controlled delete confirm modal
const DeleteConfirm: React.FunctionComponent<IDeleteConfirmProps> = ({
  open,
  item,
  feedback,
  nofeedback,
  id,
  loading,
}) => {
  useEffect(() => {
    console.log("open confirm", open);
  }, [open]);
  return (
    <Modal onClose={nofeedback} isOpen={open} isCentered>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>
          <Icon as={FaBitbucket} mr={15} />
          Delete {item}
        </ModalHeader>
        <ModalBody style={{ textAlign: "center" }}>
          <p>Are you sure?</p>
        </ModalBody>
        <ModalFooter>
          <Button
            isLoading={loading}
            variant="ghost"
            colorScheme="green"
            onClick={nofeedback}
            mr={25}
          >
            <Icon as={BsFillDashCircleFill} mr={3} /> Cancel
          </Button>
          <Button
            isLoading={loading}
            colorScheme="red"
            onClick={() => feedback(id)}
          >
            <Icon as={IoMdCheckmarkCircleOutline} mr={3} /> Confirm
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default DeleteConfirm;
