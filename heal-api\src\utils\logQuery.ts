import fs from "fs/promises";
import path from "path";
import readline from "readline";
import { createReadStream } from "fs";
import { ObjectType, Field } from "type-graphql";

@ObjectType()
export class LogEntry {
  @Field()
  timestamp!: string;

  @Field()
  level!: string;

  @Field()
  message!: string;

  @Field(() => String, { nullable: true })
  companyId?: string;

  @Field({ nullable: true })
  errorCode?: string;

  @Field(() => String, { nullable: true })
  userId?: string;

  @Field({ nullable: true })
  stackTrace?: string;

  @Field({ nullable: true })
  severity?: string;

  @Field({ nullable: true })
  action?: string;

  constructor(data: any) {
    // Handle timestamp and level from Winston format
    this.timestamp = data.timestamp || new Date().toISOString();
    this.level = data.level || "error";

    // Handle message object structure
    if (typeof data.message === "object") {
      // Extract fields from message object
      const messageObj = data.message;
      this.companyId = messageObj.companyId;
      this.errorCode = messageObj.errorCode;
      this.userId = messageObj.userId;
      this.stackTrace = messageObj.stackTrace;
      this.severity = messageObj.severity;
      this.action = messageObj.action;
      this.message = messageObj.errorMessage || JSON.stringify(messageObj);
    } else {
      // If message is string, use it directly
      this.message = data.message;

      // Try to get other fields from root level
      this.companyId = data.companyId;
      this.errorCode = data.errorCode;
      this.userId = data.userId;
      this.stackTrace = data.stackTrace;
      this.severity = data.severity;
      this.action = data.action;
    }
  }

  [key: string]: any;
}

interface LogQueryOptions {
  startDate?: Date;
  endDate?: Date;
  level?: string;
  companyId?: string;
  errorCode?: string;
  userId?: string;
  severity?: string;
}

export class LogQuery {
  private logDir =
    process.env.NODE_ENV === "production"
      ? "/usr/app/logs"
      : path.join(__dirname, "../../logs");

  async queryLogs(options: LogQueryOptions): Promise<LogEntry[]> {
    const results: LogEntry[] = [];
    const files = await fs.readdir(this.logDir);

    const logFiles = files.filter(
      (file) =>
        file.endsWith(".log") &&
        (!options.startDate ||
          this.getDateFromFilename(file) >= options.startDate) &&
        (!options.endDate || this.getDateFromFilename(file) <= options.endDate)
    );

    for (const file of logFiles) {
      const fileStream = createReadStream(path.join(this.logDir, file));
      const rl = readline.createInterface({
        input: fileStream,
        crlfDelay: Infinity,
      });

      for await (const line of rl) {
        try {
          const parsed = JSON.parse(line);
          const entry = new LogEntry(parsed);
          if (this.matchesFilter(entry, options)) {
            results.push(entry);
          }
        } catch (error) {
          console.error(`Error parsing log line: ${line}`);
        }
      }
    }

    return results;
  }

  private getDateFromFilename(filename: string): Date {
    const match = filename.match(/\d{4}-\d{2}-\d{2}/);
    return match ? new Date(match[0]) : new Date(0);
  }

  private matchesFilter(entry: LogEntry, options: LogQueryOptions): boolean {
    if (options.level && entry.level !== options.level) return false;
    if (options.companyId && entry.companyId !== options.companyId)
      return false;
    if (options.errorCode && entry.errorCode !== options.errorCode)
      return false;
    if (options.userId && entry.userId !== options.userId) return false;
    if (options.severity && entry.severity !== options.severity) return false;

    // Ensure message is a string
    if (typeof entry.message === "object") {
      entry.message = JSON.stringify(entry.message);
    }
    return true;
  }

  async getTodaysErrors(): Promise<LogEntry[]> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return this.queryLogs({ startDate: today, level: "error" });
  }

  async getErrorsByCompany(
    companyId: string,
    days: number = 7
  ): Promise<LogEntry[]> {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    return this.queryLogs({
      startDate,
      companyId,
      level: "error",
    });
  }

  async getErrorsByCode(
    errorCode: string,
    days: number = 7
  ): Promise<LogEntry[]> {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    return this.queryLogs({
      startDate,
      errorCode,
      level: "error",
    });
  }
}
