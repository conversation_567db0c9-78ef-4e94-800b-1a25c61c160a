import { useContext, useEffect, useState } from "react";
import {
  Box,
  Grid,
  GridItem,
  Flex,
  useDisclosure,
  Heading,
  Divider,
} from "@chakra-ui/react";
import ItemDetails from "./components/ItemDetails";
import BatchTable from "./components/BatchTable";
import StoreStockTable from "./components/StoreSTockTable";
import RecentImportsTable from "./components/RecentImports";
import RecentTransfersTable from "./components/RecentTransfers";
import BatchStockModal from "./components/BatchStockModal";
import StoreStockModal from "./components/StoreStockModal";
import {
  BatchStock,
  Import,
  Item,
  StoreItemStock,
  Transfer,
  useGetItemBatchImportsQuery,
  useGetItemBatchStocksQuery,
  useGetItemStoreStocksQuery,
  useGetItemTransfersQuery,
} from "../../generated/graphql";
import { useHistory, useLocation } from "react-router-dom";
import { MeContext } from "../../components/Wrapper";

interface IViewItemState {
  item: any;
  state: { item: Item };
}

const ViewItemPage = () => {
  const [selectedBatch, setSelectedBatch] = useState<BatchStock | null>(null);
  const [selectedStore, setSelectedStore] = useState<StoreItemStock | null>(
    null
  );
  const batchModal = useDisclosure();
  const storeModal = useDisclosure();
  const history = useHistory();
  const { state } = useLocation<IViewItemState>();
  const me = useContext(MeContext);

  const [
    { data: batches, fetching: fetchingBatches, error: errorFetchingBatches },
  ] = useGetItemBatchStocksQuery({
    variables: { itemId: state.item.id },
    requestPolicy: "cache-and-network",
  });

  const [
    { data: stores, fetching: fetchingStores, error: errorFetchingStores },
  ] = useGetItemStoreStocksQuery({
    variables: { itemId: state.item.id },
    requestPolicy: "cache-and-network",
  });

  const [{ data: purchases }] = useGetItemBatchImportsQuery({
    variables: { itemId: state.item.id },
    requestPolicy: "cache-and-network",
  });

  const [
    { data: imports, fetching: fetchingImports, error: errorFetchingImports },
  ] = useGetItemTransfersQuery({
    variables: { itemId: state.item.id, type: "purchase" },
    requestPolicy: "cache-and-network",
  });

  const [
    {
      data: transfers,
      fetching: fetchingTransfers,
      error: errorFetchingTransfers,
    },
  ] = useGetItemTransfersQuery({
    variables: { itemId: state.item.id, type: "transfer" },
    requestPolicy: "cache-and-network",
  });

  useEffect(() => {
    if (!state)
      history.push(
        me?.role!.name ? "/" + me?.role.name + "/stores/" : "/login"
      );
    console.log("state as received: ", state.item);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Flex direction="column" pt={{ base: "120px", md: "75px" }}>
      <ItemDetails item={state.item} />
      <Box mt={5} mb={20}>
        <Grid templateColumns="repeat(1, 1fr)" gap={6}>
          <GridItem w="100%">
            <Heading size="md" mb={4}>
              Batch Information
            </Heading>
            {!errorFetchingBatches &&
              !fetchingBatches &&
              batches &&
              batches?.getItemBatchStocks.length > 0 && (
                <BatchTable
                  item={state.item}
                  batches={batches.getItemBatchStocks as BatchStock[]}
                  onView={(batch: BatchStock) => {
                    setSelectedBatch(batch);
                    batchModal.onOpen();
                  }}
                />
              )}
            {errorFetchingBatches && (
              <>
                <h3>Error fetching batches for this item!</h3>
                <h4>{errorFetchingBatches.message}</h4>
              </>
            )}
          </GridItem>
          <Divider my={6} />
          <GridItem w="100%">
            <Heading size="md" mb={4}>
              Store Stock Information
            </Heading>
            {!errorFetchingStores &&
              !fetchingStores &&
              stores &&
              stores?.getItemStoreStocks.length > 0 && (
                <StoreStockTable
                  item={state.item}
                  stores={stores.getItemStoreStocks as StoreItemStock[]}
                  onView={(store: StoreItemStock) => {
                    setSelectedStore(store);
                    storeModal.onOpen();
                  }}
                />
              )}
            {errorFetchingStores && (
              <>
                <h3>Error fetching stores for this item!</h3>
                <h4>{errorFetchingStores.message}</h4>
              </>
            )}
          </GridItem>
          <Divider my={6} />
          <GridItem w="100%">
            <Heading size="md" mb={4}>
              Recent Imports
            </Heading>
            {!errorFetchingImports &&
              !fetchingImports &&
              batches &&
              imports &&
              imports?.getItemTransfers.length > 0 && (
                <RecentImportsTable
                  item={state.item}
                  batches={batches.getItemBatchStocks as BatchStock[]}
                  imports={imports.getItemTransfers as Transfer[]}
                  purchases={purchases?.getItemBatchImports as Import[]}
                  onView={(batch: BatchStock) => {
                    setSelectedBatch(batch);
                    batchModal.onOpen();
                  }}
                />
              )}
            {errorFetchingImports && (
              <>
                <h3>Error fetching imports for this item!</h3>
                <h4>{errorFetchingImports.message}</h4>
              </>
            )}
          </GridItem>
          <Divider my={6} />
          <GridItem w="100%">
            <Heading size="md" mb={4}>
              Recent Transfers
            </Heading>
            {!errorFetchingTransfers &&
              !fetchingTransfers &&
              batches &&
              transfers &&
              transfers?.getItemTransfers.length > 0 && (
                <RecentTransfersTable
                  transfers={transfers.getItemTransfers as Transfer[]}
                  batches={batches.getItemBatchStocks as BatchStock[]}
                  stores={stores?.getItemStoreStocks as StoreItemStock[]}
                  onView={(batch: BatchStock) => {
                    setSelectedBatch(batch);
                    batchModal.onOpen();
                  }}
                />
              )}
            {errorFetchingTransfers && (
              <>
                <h3>Error fetching imports for this item!</h3>
                <h4>{errorFetchingTransfers.message}</h4>
              </>
            )}
          </GridItem>
        </Grid>
      </Box>
      <BatchStockModal
        isOpen={batchModal.isOpen}
        onClose={batchModal.onClose}
        batch={selectedBatch}
        stores={stores?.getItemStoreStocks as StoreItemStock[]}
        item={state.item}
      />
      <StoreStockModal
        isOpen={storeModal.isOpen}
        onClose={storeModal.onClose}
        store={selectedStore}
        item={state.item}
        batches={batches?.getItemBatchStocks as BatchStock[]}
      />
    </Flex>
  );
};

export default ViewItemPage;
