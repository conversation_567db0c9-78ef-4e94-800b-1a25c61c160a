import loginImage from "../assets/img/login.jpg";
import React from "react";
import * as Yup from "yup";
import { Formik } from "formik";
import { InputField } from "../components/InputField";
import { useLoginMutation } from "../generated/graphql";
import { Link, useHistory } from "react-router-dom";
import {
  Button,
  Flex,
  Box,
  Heading,
  Text,
  VStack,
  Image,
  useColorModeValue,
  useBreakpointValue,
} from "@chakra-ui/react";

interface IAppProps {}

const Login: React.FunctionComponent<IAppProps> = () => {
  const [, login] = useLoginMutation();
  const [general, setGeneral] = React.useState("");
  const history = useHistory();

  const cardBg = useColorModeValue("white", "gray.800");
  const bgGradient = useColorModeValue(
    "linear(to-r, teal.100, blue.200)",
    "linear(to-r, teal.900, blue.800)"
  );
  const formHeadingColor = useColorModeValue("teal.600", "teal.300");

  return (
    <Flex
      align="center"
      justify="center"
      minH="100vh"
      bgGradient={bgGradient}
      p={6}
    >
      <Box
        bg={cardBg}
        boxShadow="2xl"
        borderRadius="xl"
        overflow="hidden"
        maxW={{ base: "90%", md: "60%", lg: "50%" }}
        display={{ base: "block", md: "flex" }}
      >
        <Box flex="1">
          <Image
            src={loginImage}
            alt="Login Image"
            objectFit="cover"
            height="100%"
            width="100%"
          />
        </Box>
        <Box
          p={8}
          flex="1"
          bgGradient={useColorModeValue(
            "linear(to-b, white, gray.50)",
            "linear(to-b, gray.700, gray.800)"
          )}
        >
          <Flex justify="center" align="center" mb={6}>
            <Heading
              as="h2"
              size={useBreakpointValue({ base: "lg", md: "xl" })}
              textAlign="center"
              color={formHeadingColor}
              fontWeight="bold"
              letterSpacing="wide"
            >
              Login
            </Heading>
          </Flex>
          {general ? (
            <Text mb={4} color="red.500" fontWeight="bold">
              {general}
            </Text>
          ) : null}
          <Formik
            initialValues={{ email: "", password: "" }}
            onSubmit={async (values) => {
              const user = await login({ params: values });
              if (user.data?.login.error) {
                setGeneral(user.data.login.error.message);
              } else if (user.data?.login.user) {
                history.replace("/");
              }
            }}
            validationSchema={Yup.object({
              email: Yup.string()
                .email("Enter a valid email address!")
                .required("Required"),
              password: Yup.string()
                .min(3, "Must be 3 or more characters!")
                .max(20, "Must be 20 characters or less!")
                .required("Required"),
            })}
            validateOnChange={false}
          >
            {(props) => (
              <form
                onSubmit={(e) => {
                  e.preventDefault();
                  props.submitForm();
                }}
              >
                <VStack spacing={4}>
                  <InputField
                    error={props.errors.email}
                    name="email"
                    touched={props.touched.email}
                    label="Email"
                    placeholder="E-mail address"
                    type="email"
                  />
                  <InputField
                    error={props.errors.password}
                    name="password"
                    label="Password"
                    touched={props.touched.password}
                    placeholder="Password"
                    type="password"
                  />
                </VStack>
                <Button
                  isLoading={props.isSubmitting}
                  colorScheme="teal"
                  size="lg"
                  type="submit"
                  width="full"
                  mt={6}
                >
                  Login
                </Button>
              </form>
            )}
          </Formik>
          <Text mt={4} textAlign="center">
            Forgot password?{" "}
            <Link to="/forgot-password" style={{ color: "#319795" }}>
              Recover
            </Link>
          </Text>
        </Box>
      </Box>
    </Flex>
  );
};

export default Login;
