{"ROLES": {"NAME": "USER ROLES STORED IN DB", "CATEGORIES": ["admin", "employee"]}, "EMPLOYEE ROLES": {"NAME": "EMPLOYEE ROLES ENUMERATED", "CATEGORIES": ["admin", "employee"]}, "TYPE": {"NAME": "ACCOUNT STATUS", "CATEGORIES": ["DISABLED", "NEW   // comment: this will tell us the user hasn't logged in since creation so should change password to proceed// ", "ACTIVE", "LOCKED", "DELETED", "EXPIRED", "EXPIRING", "ONLEAVE", "SUSPENDED", "MATERNITY", "PATERNITY", "SABBATICAL", "TERMINATED"]}, "TARGET": {"DESCRIPTION": "TARGET WHEN ADDING OR EDITING ADDRESS, ADDING USER LOCATION, COMPANY LOCATION TO ADDRESS", "ENUM": ["company", "address", "currentUser<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "menu": {"Inventory": {"Internal Stock": ["Stock [done]", "Transfer requests", "Purchase / Import", "Return notice", "Direct dispatch", "Write off", "Reports"], "Merchandise": ["Stock", "Transfer requests", "Purchase / Import", "Return notice", "Direct dispatch", "Write off", "Reports"]}}}