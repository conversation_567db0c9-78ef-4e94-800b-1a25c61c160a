export const formatToMoney = (value: number): string => {
  // Convert the number to a string
  let stringValue = value.toString();

  // Split the string into integer and decimal parts
  const parts = stringValue.split(".");
  const integerPart = parts[0];
  const decimalPart = parts.length > 1 ? "." + parts[1] : "";

  // Insert commas every three digits from the right in the integer part
  let formattedIntegerPart = "";
  for (let i = integerPart.length - 1, j = 0; i >= 0; i--, j++) {
    if (j > 0 && j % 3 === 0) {
      // Insert comma after every three digits
      formattedIntegerPart = "," + formattedIntegerPart;
    }
    formattedIntegerPart = integerPart[i] + formattedIntegerPart;
  }

  // Concatenate the integer and decimal parts
  const formattedValue = formattedIntegerPart + decimalPart;

  return `${formattedValue} Tsh`;
};

// Helper function to convert PostgreSQL timestamp to formatted date string
export const toDateTimePostgresTimeStamp = (timestamp: string): string => {
  // Remove timezone information if present
  const cleanTimestamp = timestamp.replace(/([+-]\d{2}:\d{2}|Z)$/, "");

  // Parse the cleaned timestamp
  const date = new Date(cleanTimestamp);

  // Check if the date is valid
  if (isNaN(date.getTime())) {
    console.error("Invalid timestamp format");
  }

  // Format the date as dd/mm/yyyy hh:mm
  const day = String(date.getDate()).padStart(2, "0");
  const month = String(date.getMonth() + 1).padStart(2, "0"); // Month is zero-based
  const year = date.getFullYear();
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");

  return `${day}/${month}/${year} ${hours}:${minutes}`;
};

export const toDateTime = (timestamp: number): string => {
  // Create a Date object using the timestamp
  const date = new Date(timestamp);

  // Check if the date is valid
  if (isNaN(date.getTime())) {
    console.error("Invalid timestamp format");
    return "";
  }

  // Format the date as dd/mm/yyyy hh:mm
  const day = String(date.getDate()).padStart(2, "0");
  const month = String(date.getMonth() + 1).padStart(2, "0"); // Month is zero-based
  const year = date.getFullYear();
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");

  return `${day}/${month}/${year} ${hours}:${minutes}`;
};

export const toAge = (dateString: string): string => {
  const birthDate = new Date(dateString);
  const today = new Date();

  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDifference = today.getMonth() - birthDate.getMonth();
  const dayDifference = today.getDate() - birthDate.getDate();

  // Adjust the age if the current date is before the birth date's anniversary this year
  if (monthDifference < 0 || (monthDifference === 0 && dayDifference < 0)) {
    age--;
  }

  return `${age} Years`;
};
