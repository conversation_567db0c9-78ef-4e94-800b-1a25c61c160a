# Node Environment
NODE_ENV=production

# Database Configuration
POSTGRES_DB=HealDB
POSTGRES_USER=postgres
POSTGRES_PASSWORD=
DB_TYPE=postgres

# API Configuration
API_PORT=4000
API_HTTPS_PORT=443
COOKIE_NAME=hid
CUSTOM_SECRET='dfbvrttg45gterg43344rGHGYT7Ygyg&&ggyg&&ggygGYGGFF^YYHKH*888HYUHJGGH'

# Frontend Configuration
CLIENT_PORT=3000
VITE_SUPABASE_URL=https://eqzgvivfuzmyfxbupxht.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVxemd2aXZmdXpteWZ4YnVweGh0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mjc4NjA4NzQsImV4cCI6MjA0MzQzNjg3NH0.VC0pTXJJHmzVfXpxJ56-aqirQZ608PHTouFZ1-mkVbg
VITE_API_URL=https://localhost

# Redis Configuration
REDIS_PORT=6379

# Email Configuration
MY_SENDER_EMAIL=<EMAIL>
MY_SENDER_EMAIL_PASSWORD=rrvtthsitjuxzkhi
MY_SSL_EMAIL_SERVER=smtp.gmail.com

# Frontend Origins
FRONTEND_WEB_ORIGIN=http://localhost:3000
FRONTEND_DESKTOP_ORIGIN=https://localhost:5123