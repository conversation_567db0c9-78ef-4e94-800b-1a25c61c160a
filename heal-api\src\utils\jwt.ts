import jwt from "jsonwebtoken";
import { User } from "../entities/User";

const JWT_SECRET =
  process.env.JWT_SECRET ||
  "TaLiS1a_P0w3rSyNc_JWT_S3cr3t_K3y_2025_Pr0duCt10n_V3ry_L0ng_4nd_S3cur3_K3y"; // Use environment variable in production
const JWT_EXPIRES_IN = "7d";

export const generateToken = (user: User): string => {
  return jwt.sign(
    {
      userId: user.id,
      companyId: user.companyId,
      role: user.role?.name,
      email: user.email,
    },
    JWT_SECRET,
    {
      audience: "talisia-powersync",
      issuer: "talisia-api",
      expiresIn: JWT_EXPIRES_IN,
    }
  );
};

export const verifyToken = (token: string): any => {
  try {
    return jwt.verify(token, JWT_SECRET, {
      audience: "talisia-powersync",
      issuer: "talisia-api",
    });
  } catch (err) {
    return null;
  }
};
