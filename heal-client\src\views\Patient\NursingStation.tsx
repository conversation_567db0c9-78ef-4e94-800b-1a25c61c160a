import {
  Flex,
  Table,
  Tbody,
  Text,
  Th,
  Thead,
  Tr,
  Td,
  Button,
  Input,
  InputGroup,
  InputRightElement,
  useColorModeValue,
} from "@chakra-ui/react";
import Card from "../../components/Card/Card.js";
import CardBody from "../../components/Card/CardBody.js";
import CardHeader from "../../components/Card/CardHeader.js";
import { useGetClinicsQuery, useGetVisitsQuery } from "../../generated/graphql"; // Import your getVisits query hook
import { SearchIcon } from "@chakra-ui/icons";
import { Link } from "react-router-dom";
import { SetStateAction, useState } from "react";

function VisitingPatients() {
  const [{ data: visits }] = useGetVisitsQuery({
    requestPolicy: "network-only",
  });
  const [{ data: clinics }] = useGetClinicsQuery({
    requestPolicy: "network-only",
  });
  const [searchQuery, setSearchQuery] = useState(""); // State to store the search query
  const textColor = useColorModeValue("gray.700", "white");
  const borderColor = useColorModeValue("gray.200", "gray.600");
  // const buttonBg = useColorModeValue("teal.400", "teal.600");
  // const buttonHoverBg = useColorModeValue("teal.600", "teal.700");
  // const history = useHistory();

  const handleSearchChange = (e: {
    target: { value: SetStateAction<string> };
  }) => {
    setSearchQuery(e.target.value);
  };

  // Filter visits with currentLocation 'waiting vital signs tests' and no pending bills
  const filteredVisits = visits?.getVisits?.filter((visit) => {
    const fullName =
      `${visit.client.firstname} ${visit.client.lastname}`.toLowerCase();
    const hasNoPendingBills = visit.bills.every(
      (bill) => bill.cleared === true
    );
    return (
      hasNoPendingBills &&
      visit.currentLocation === "waiting vital signs tests" &&
      (fullName.includes(searchQuery.toLowerCase()) ||
        visit.client.phone.includes(searchQuery))
    );
  });

  return (
    <Flex direction="column" pt={{ base: "120px", md: "75px" }}>
      <Card overflowX={{ sm: "scroll", xl: "hidden" }} pb="0px">
        <CardHeader
          p="6px 0px 22px 0px"
          display="flex"
          justifyContent="space-between"
          alignItems="center"
        >
          <Text fontSize="xl" color={textColor} fontWeight="bold">
            Visiting Patients
          </Text>
          <InputGroup w={{ base: "100%", md: "300px" }}>
            <Input
              placeholder="Search patients..."
              value={searchQuery}
              onChange={handleSearchChange}
              bg={useColorModeValue("white", "teal.800")}
              borderColor={borderColor}
              _placeholder={{ color: "gray.400" }}
            />
            <InputRightElement pointerEvents="none">
              <SearchIcon color="gray.300" />
            </InputRightElement>
          </InputGroup>
        </CardHeader>
        <CardBody>
          <Table variant="simple" color={textColor}>
            <Thead>
              <Tr my=".8rem" pl="0px" color="gray.400">
                <Th pl="0px" borderColor={borderColor} color="gray.400">
                  Name
                </Th>
                <Th borderColor={borderColor} color="gray.400">
                  Reason For Visit
                </Th>
                <Th borderColor={borderColor} color="gray.400">
                  Assigned Clinic
                </Th>
                <Th borderColor={borderColor}></Th>
              </Tr>
            </Thead>
            <Tbody>
              {filteredVisits?.length && filteredVisits?.length > 0 ? (
                filteredVisits.map((visit) => (
                  <Tr key={visit.id}>
                    <Td pl="0px" borderColor={borderColor}>
                      {visit.client.firstname + " " + visit.client.lastname}
                    </Td>
                    <Td borderColor={borderColor}>{visit.reason}</Td>
                    <Td borderColor={borderColor}>
                      {clinics &&
                      visit.visitToClinics &&
                      visit.visitToClinics[0] &&
                      visit.visitToClinics[0].clinicId > 0
                        ? clinics.getClinics.find(
                            (clinic) =>
                              clinic.id === visit.visitToClinics[0].clinicId
                          )?.name || "Clinic not found"
                        : "No clinic"}
                    </Td>

                    <Td borderColor={borderColor} textAlign="right">
                      <Button
                        as={Link}
                        to={{
                          pathname: "add-vitals",
                          state: {
                            visitId: visit.id,
                            clientName:
                              visit.client.firstname +
                              " " +
                              visit.client.lastname,
                          },
                        }}
                        size="sm"
                        bg="blue.500"
                        color="white"
                        _hover={{ bg: "gray.600" }}
                      >
                        Add Vital Signs
                      </Button>
                    </Td>
                  </Tr>
                ))
              ) : (
                <Tr>
                  <Td colSpan={5} textAlign="center" color="gray.500">
                    No patients found.
                  </Td>
                </Tr>
              )}
            </Tbody>
          </Table>
        </CardBody>
      </Card>
    </Flex>
  );
}

export default VisitingPatients;
