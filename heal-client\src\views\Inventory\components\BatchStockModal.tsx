import {
  Modal,
  ModalOverlay,
  Modal<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  ModalCloseButton,
  ModalBody,
  ModalFooter,
  Button,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  useColorModeValue,
} from "@chakra-ui/react";
import { BatchStock, Item, StoreItemStock } from "../../../generated/graphql";

const BatchStockModal = ({
  isOpen,
  onClose,
  batch,
  item,
  stores,
}: {
  isOpen: boolean;
  onClose: () => void;
  batch: BatchStock | null;
  item: Item;
  stores: StoreItemStock[];
}) => {
  const borderColor = useColorModeValue("gray.200", "gray.600");
  const textColor = useColorModeValue("gray.700", "white");

  if (!batch) return null;

  // Calculate total batch items across all stores
  const totalBatch = batch.storeItemStocks.reduce(
    (total, storeItemStock) => {
      total.stock += storeItemStock.stock;
      total.pieceStock += storeItemStock.pieceStock;
      total.subPieceStock += storeItemStock.subPieceStock;
      return total;
    },
    { stock: 0, pieceStock: 0, subPieceStock: 0 }
  );

  const getStoreName = (storeId: number) => {
    const store = stores.find((storeItem) => storeItem.storeId === storeId);
    return store ? store.store.name : "Unknown Store";
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>{batch.batch} Stock Details</ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <Table variant="simple" color={textColor}>
            <Thead>
              <Tr>
                <Th borderColor={borderColor}>Store Name</Th>
                <Th borderColor={borderColor}>Stock in {item.unit}</Th>
                {item.pieceUnit && (
                  <Th borderColor={borderColor}>Stock in {item.pieceUnit}</Th>
                )}
                {item.subPieceUnit && (
                  <Th borderColor={borderColor}>
                    Stock in {item.subPieceUnit}
                  </Th>
                )}
              </Tr>
            </Thead>
            <Tbody>
              {batch.storeItemStocks.map((storeItemStock) => (
                <Tr key={storeItemStock.storeId}>
                  <Td borderColor={borderColor}>
                    {getStoreName(storeItemStock.storeId)}
                  </Td>
                  <Td borderColor={borderColor}>{storeItemStock.stock}</Td>
                  {item.pieceUnit && (
                    <Td borderColor={borderColor}>
                      {storeItemStock.pieceStock}
                    </Td>
                  )}
                  {item.subPieceUnit && (
                    <Td borderColor={borderColor}>
                      {storeItemStock.subPieceStock}
                    </Td>
                  )}
                </Tr>
              ))}
              {/* Total row */}
              <Tr>
                <Td fontWeight="bold" borderColor={borderColor}>
                  Total
                </Td>
                <Td fontWeight="bold" borderColor={borderColor}>
                  {totalBatch.stock}
                </Td>
                {item.pieceUnit && (
                  <Td fontWeight="bold" borderColor={borderColor}>
                    {totalBatch.pieceStock}
                  </Td>
                )}
                {item.subPieceUnit && (
                  <Td fontWeight="bold" borderColor={borderColor}>
                    {totalBatch.subPieceStock}
                  </Td>
                )}
              </Tr>
            </Tbody>
          </Table>
        </ModalBody>
        <ModalFooter>
          <Button colorScheme="blue" mr={3} onClick={onClose}>
            Close
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default BatchStockModal;
