import React, { useState, useEffect } from "react";
import AsyncSelect from "react-select/async";
import {
  Item,
  useGetBatchStockForStoreQuery,
} from "../../../generated/graphql";
import { Badge, Box, Flex, Text } from "@chakra-ui/react";

interface BatchInputProps {
  storeId: number | undefined;
  item: Item | undefined;
  onChange: (value: any) => void;
}

const BatchInput: React.FC<BatchInputProps> = ({ storeId, item, onChange }) => {
  const [
    { data: batchStocks, fetching: fetchingBatchStocks },
    reGetBatchStocks,
  ] = useGetBatchStockForStoreQuery({
    variables: {
      storeId,
      itemId: item ? item.id : undefined,
    },
    requestPolicy: "network-only",
  });

  const [batchOptions, setBatchOptions] = useState<
    {
      label: string;
      value: string;
      stock: number;
      unit: string;
      expireDate: string;
    }[]
  >([]);

  useEffect(() => {
    reGetBatchStocks({ requestPolicy: "network-only" });
  }, [storeId, item, reGetBatchStocks]);

  useEffect(() => {
    loadBatches("");
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fetchingBatchStocks]);

  const loadBatches = async (searchInput: string) => {
    if (batchStocks) {
      const tempBatches = batchStocks.getBatchStockForStore
        .filter((item) =>
          item.storeItemStocks.some((storeItemStock) => {
            if (storeId && storeId > 0)
              return storeItemStock.storeId === storeId;
            return true;
          })
        )
        .filter((batch) =>
          batch.batch
            .toLocaleLowerCase()
            .includes(searchInput.toLocaleLowerCase())
        )
        .map((r) => ({
          // label: `[${r.batch} ${r.stock} ${item ? item.unit : ""}]`,
          label: r.batch,
          value: r.batch,
          stock: r.stock,
          unit: item ? item.unit : "",
          expireDate: r.expireDate,
        }));

      setBatchOptions(tempBatches);
      return tempBatches;
    }
    return [];
  };

  const customOption = (props: any) => {
    const { innerRef, innerProps, data } = props;
    const expireDate = new Date(data.expireDate);
    const today = new Date();
    const next30Days = new Date(today);
    next30Days.setDate(today.getDate() + 30);

    return (
      <Box
        ref={innerRef}
        {...innerProps}
        display="flex"
        alignItems="center"
        justifyContent="space-between"
      >
        <Text ml={2}>{data.value}</Text>
        <Flex>
          <Badge
            colorScheme={expireDate < next30Days ? "red" : "green"}
            fontSize="0.8em"
            variant="solid"
            mr={2}
          >
            Exp: {data.expireDate}
          </Badge>
          <Badge
            colorScheme={item && data.stock > item.reorder ? "blue" : "yellow"}
            fontSize="0.8em"
            variant="solid"
            mr={2}
          >
            {data.stock} {data.unit}
          </Badge>
        </Flex>
      </Box>
    );
  };

  return (
    <AsyncSelect
      variant="flushed"
      isClearable
      isSearchable={true}
      isLoading={fetchingBatchStocks}
      defaultOptions={batchOptions}
      loadOptions={loadBatches}
      size="sm"
      colorScheme="navy"
      placeholder="Select batch"
      closeMenuOnSelect={true}
      onChange={onChange}
      escapeClearsValue={true}
      hideSelectedOptions={true}
      styles={{
        container: (base) => ({
          ...base,
          width: "100%",
          color: "navy",
        }),
      }}
      components={{ Option: customOption }}
    />
  );
};

export default BatchInput;
