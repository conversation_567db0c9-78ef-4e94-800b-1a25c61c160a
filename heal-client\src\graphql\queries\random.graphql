# permission queries
query getPermissions {
  getPermissions {
    id
    name
    roles {
      id
      name
    }
  }
}

# features queries
query getFeatures {
  getFeatures {
    id
    name
    companies {
      id
      name
      type
    }
  }
}

# types queries
query getTypes {
  getTypes {
    id
    name
    description
    category {
      id
      name
    }
  }
}

query getType($id: Float!) {
  getType(id: $id) {
    id
    name
    description
    category {
      id
      name
    }
  }
}

# roles queries
query getRoles {
  getRoles {
    id
    sys
    name
  }
}

query getRole($name: String!) {
  getRole(name: $name) {
    id
    name
    permissions {
      id
      name
    }
  }
}

# categories queries
query getAllCategories {
  getAllCategories {
    id
    name
  }
}

query getCategories($type: String!) {
  getCategories(type: $type) {
    id
    name
  }
}

# get all addresses
query getAllAddress {
  getAllAddress {
    id
    country
    city
    district
    ward
    street
    zip
  }
}
