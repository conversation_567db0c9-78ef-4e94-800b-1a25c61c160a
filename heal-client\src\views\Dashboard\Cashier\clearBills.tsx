import React, { useEffect, useRef, useState } from "react";
import {
  Flex,
  <PERSON>ton,
  Text,
  useColorModeValue,
  useToast,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from "@chakra-ui/react";
import { MdOutlineMoveUp } from "react-icons/md";
import {
  Inventory,
  Item,
  Transfer,
  useChangeInventoryApprovalStatusMutation,
  useGetSalesQuery,
} from "../../../generated/graphql";
import { formatToMoney } from "../../../utils/Helpers";
import { useReactToPrint } from "react-to-print";
import Receipt from "./receipt";

const Cashier: React.FC = () => {
  const toast = useToast({
    position: "top",
  });

  const nameColor = useColorModeValue("gray.500", "white");

  const [{ data: sales }, reGetSales] = useGetSalesQuery({
    requestPolicy: "network-only",
  });
  const [, clearBill] = useChangeInventoryApprovalStatusMutation();
  const { isOpen, onOpen, onClose } = useDisclosure();

  const [selectedSale, setSelectedSale] = useState<Inventory | null>(null);

  const receiptRef = useRef<HTMLDivElement>(null);

  const handlePrint = useReactToPrint({
    content: () => receiptRef.current,
  });

  const handleViewDetails = (sale: Inventory) => {
    setSelectedSale(sale);
    onOpen();
  };

  const handleClearBill = async () => {
    const { data } = await clearBill({ inventoryId: selectedSale!.id });
    if (data?.changeInventoryApprovalStatus.error) {
      toast({
        title: data.changeInventoryApprovalStatus.error.message,
        status: "error",
        isClosable: true,
      });
    } else {
      reGetSales();
      onClose();
      toast({
        title: "Bill cleared successfully!",
        variant: "left-accent",
        status: "success",
        isClosable: true,
      });
      handlePrint();
    }
  };

  useEffect(() => {
    const interval = setInterval(() => {
      reGetSales({
        requestPolicy: "network-only",
      });
    }, 5000);

    return () => clearInterval(interval);
  }, [reGetSales]);

  const calculateTotalSale = (sale: Inventory) => {
    return sale.items.reduce((total: number, item: Item) => {
      const itemTransfer = sale.transfers.find(
        (transfer: Transfer) => transfer.itemId === item.id
      );
      const price =
        itemTransfer!.quantity > 0
          ? item.sellingPrice * itemTransfer!.quantity
          : itemTransfer!.pieceQuantity > 0
          ? item.pieceSellingPrice * itemTransfer!.pieceQuantity
          : itemTransfer!.subPieceQuantity > 0
          ? item.subPieceSellingPrice * itemTransfer!.subPieceQuantity
          : 0;

      return total + price;
    }, 0);
  };

  return (
    <Flex direction="column" position="relative" mb="90px" mt="100px">
      <Flex
        w="100%"
        mx="auto"
        justifyContent="space-between"
        mb="60px"
        pt={{ md: "0px" }}
        direction="column"
      >
        <Flex direction="column" overflow="auto">
          <Text
            fontSize="xl"
            color={nameColor}
            fontWeight="bold"
            textAlign="center"
            mb="12px"
          >
            Sale Orders
          </Text>
          <Table variant="simple">
            <Thead>
              <Tr>
                <Th>Order No</Th>
                <Th>Bill Amount</Th>
                <Th>Remarks</Th>
                <Th>Payment</Th>
                <Th>Details</Th>
              </Tr>
            </Thead>
            <Tbody>
              {sales?.getSales.map((sale) => {
                const approved = sale.granted;
                return (
                  <Tr key={sale.id}>
                    <Td>{sale.id}</Td>
                    <Td>{formatToMoney(Number(sale.bill?.amount))}</Td>
                    <Td>{sale.details}</Td>
                    <Td>
                      {approved ? (
                        <Badge
                          colorScheme="green"
                          variant="outline"
                          borderRadius="full"
                          px={2}
                        >
                          Bill Cleared
                        </Badge>
                      ) : (
                        <Badge
                          colorScheme="red"
                          variant="outline"
                          borderRadius="full"
                          px={2}
                        >
                          Bill Not Cleared
                        </Badge>
                      )}
                    </Td>
                    <Td>
                      <Button
                        size="sm"
                        colorScheme="teal"
                        leftIcon={<MdOutlineMoveUp />}
                        onClick={() => handleViewDetails(sale as Inventory)}
                      >
                        View
                      </Button>
                    </Td>
                  </Tr>
                );
              })}
            </Tbody>
          </Table>
        </Flex>
      </Flex>

      <Modal isOpen={isOpen} onClose={onClose}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Sale Details</ModalHeader>
          <ModalBody>
            {selectedSale && (
              <>
                <Text fontSize="lg" fontWeight="bold" mb={4}>
                  Sale ID: {selectedSale.id}
                </Text>
                <Table variant="simple" mb="20px">
                  <Thead>
                    <Tr>
                      <Th>Item Name</Th>
                      <Th>Quantity</Th>
                      <Th>Sale Amount</Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    {selectedSale.items.map((item: Item, index: number) => {
                      const itemTransfer = selectedSale.transfers.find(
                        (transfer: Transfer) => transfer.itemId === item.id
                      );
                      return (
                        <Tr key={index}>
                          <Td>{item.name}</Td>
                          <Td>
                            {itemTransfer!.quantity > 0
                              ? itemTransfer!.quantity + " " + item?.unit
                              : " "}
                            {itemTransfer!.pieceQuantity > 0
                              ? itemTransfer!.pieceQuantity +
                                " " +
                                item?.pieceUnit
                              : ""}
                            {itemTransfer!.subPieceQuantity > 0
                              ? itemTransfer!.subPieceQuantity +
                                " " +
                                item?.subPieceUnit
                              : ""}
                          </Td>
                          <Td>
                            {formatToMoney(
                              itemTransfer!.quantity > 0
                                ? item.sellingPrice * itemTransfer!.quantity
                                : itemTransfer!.pieceQuantity > 0
                                ? item.pieceSellingPrice *
                                  itemTransfer!.pieceQuantity
                                : itemTransfer!.subPieceQuantity > 0
                                ? item.subPieceSellingPrice *
                                  itemTransfer!.subPieceQuantity
                                : 0
                            )}
                          </Td>
                        </Tr>
                      );
                    })}
                  </Tbody>
                  <Tfoot>
                    <Tr>
                      <Th colSpan={2}>Total Sale Amount:</Th>
                      <Th>{formatToMoney(calculateTotalSale(selectedSale))}</Th>
                    </Tr>
                  </Tfoot>
                </Table>
              </>
            )}
          </ModalBody>
          <ModalFooter>
            <Button colorScheme="blue" mr={3} onClick={onClose}>
              Close
            </Button>
            {!selectedSale?.granted && (
              <Button
                colorScheme="green"
                onClick={handleClearBill}
                isLoading={false}
              >
                Clear Bill
              </Button>
            )}
          </ModalFooter>
        </ModalContent>
      </Modal>

      {selectedSale && (
        <div style={{ display: "none" }}>
          <Receipt
            ref={receiptRef}
            sale={selectedSale}
            total={calculateTotalSale(selectedSale)}
          />
        </div>
      )}
    </Flex>
  );
};

export default Cashier;
