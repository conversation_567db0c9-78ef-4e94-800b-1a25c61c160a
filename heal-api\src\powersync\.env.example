# PowerSync Environment Variables
# Copy this file to .env and update the values for your environment

# Database Configuration (Source Database)
PS_POSTGRES_USER=postgres
PS_POSTGRES_PASSWORD=your_postgres_password
PS_POSTGRES_DB=TalisiaPOS

# Storage Database Configuration (for sync buckets)
# Can be the same as source database for Postgres 14+
PS_STORAGE_USER=powersync_storage_user
PS_STORAGE_PASSWORD=your_storage_password
PS_STORAGE_DB=postgres

# JWT Secret for client authentication
# Generate a strong secret for production: openssl rand -base64 64
PS_JWT_SECRET=TaLiS1a_P0w3rSyNc_JWT_S3cr3t_K3y_2025_Pr0duCt10n_V3ry_L0ng_4nd_S3cur3_K3y

# Optional: PowerSync Service Configuration
PS_PORT=8080
PS_LOG_LEVEL=info
