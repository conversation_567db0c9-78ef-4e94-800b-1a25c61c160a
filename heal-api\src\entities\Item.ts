import { Field, ObjectType } from "type-graphql";
import { Column, Entity, ManyToMany, OneToMany, Unique } from "typeorm";
import { AuditBaseEntity } from "./AuditEntity";
import { Import } from "./Inventory/Import";
import { Transfer } from "./Inventory/StockTransfer";
import { Inventory } from "./Inventory/Inventory";
import { BatchStock } from "./Inventory/Batch";
import { StoreItemStock } from "./Inventory/StoreItemStock";
import { Unit } from "./Inventory/Unit";

@ObjectType()
@Entity()
@Unique(["companyId", "name"])
@Unique(["companyId", "barcode"])
export class Item extends AuditBaseEntity {
  @Field(() => String)
  @Column({ type: "text" })
  name!: string;

  @Field(() => String)
  @Column({ type: "text" })
  description: string;

  @Field(() => String, { nullable: true })
  @Column({ type: "text", nullable: true })
  barcode: string;

  @Field(() => String)
  @Column({
    type: "enum",
    enum: ["consumable", "capital goods", "service"], // service is also an item, no stock count but treated as item in this app (different mutations for handling services unlike consumables and capital goods)
    default: "consumable",
  })
  type!: string; // consumables (end / decrease in volume) after one use example soap, bandages || capital goods used as assets and do not expire after one use example scissors, cars

  @Field()
  @Column({ type: "text" })
  reference: string; //same as description, characters of item that may help refer a user to a similar item

  @Field()
  @Column({ type: "int", default: 5 })
  reorder: number; // the minimum ammount at which the item should be restocked (unacceptable stock for item, amount depending on time it takes to reorder)

  @Field()
  @Column({ default: false })
  internal: boolean; // true => used by the internal organization to run its operations || false => sold out to customers

  @Field()
  @Column({
    type: "text",
    default:
      "https://eqzgvivfuzmyfxbupxht.supabase.co/storage/v1/object/public/heal/public/watermelon-removebg-preview.png",
  })
  image: string;

  @Field()
  @Column()
  unit!: string; //This is the unit for buying and selling the product example sweets can be bought in boxes so box is the unit

  @Field(() => [Unit])
  @OneToMany(() => Unit, (unit) => unit.item)
  units: Unit[];

  @Field()
  @Column({ type: "float", default: 0 })
  stock: number; //our inventory decrease by sales and increase by imports

  @Field()
  @Column({ type: "float", default: 0 })
  sellingPrice: number;

  @Field(() => [Import], { nullable: true })
  @OneToMany(() => Import, (purchase) => purchase.item)
  imports: Import[]; // history of purchasing the product

  @Field(() => [Transfer], { nullable: true })
  @OneToMany(() => Transfer, (StockTransfer) => StockTransfer.item)
  transfers: Transfer[]; //history of item inventory transfers

  @Field(() => [Inventory], { nullable: true })
  @ManyToMany(() => Inventory, (inventory) => inventory.items)
  inventoryTransfers: Inventory[];

  @Field(() => [BatchStock], { nullable: true })
  @OneToMany(() => BatchStock, (batchStock) => batchStock.item)
  batchStocks: BatchStock[];

  @Field(() => [StoreItemStock], { nullable: true })
  @OneToMany(() => StoreItemStock, (storeItemStock) => storeItemStock.item)
  storeItemStocks: StoreItemStock[];
}
