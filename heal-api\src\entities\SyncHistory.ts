import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  BaseEntity,
} from "typeorm";
import { Company } from "./Company";
import { Field, ObjectType } from "type-graphql";
import { SyncError } from "../types/SyncError";

@ObjectType()
@Entity()
export class SyncHistory extends BaseEntity {
  @Field(() => String)
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Field(() => String)
  @Column()
  entityName: string;

  @Field(() => String)
  @Column({ type: "enum", enum: ["up", "down", "bi"] })
  direction: "up" | "down" | "bi";

  @Field()
  @Column()
  status: "success" | "failed" | "in_progress";

  @Field(() => SyncError, { nullable: true })
  @Column({ type: "json", nullable: true })
  error: SyncError | null;

  @Field()
  @Column()
  recordsProcessed: number;

  @Field()
  @Column({ type: "timestamp" })
  lastSyncTimestamp: Date;

  @Field(() => String)
  @Column({ type: "uuid" })
  companyId: string;

  @Field(() => Company)
  @ManyToOne(() => Company)
  @JoinColumn({ name: "companyId" })
  company: Company;

  @Field()
  @CreateDateColumn()
  createdAt: Date;

  @Field()
  @UpdateDateColumn()
  updatedAt: Date;
}
