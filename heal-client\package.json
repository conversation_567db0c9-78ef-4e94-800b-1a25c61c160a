{"name": "heal-webclient", "version": "0.1.0", "private": true, "type": "module", "dependencies": {"@chakra-ui/icons": "^2.0.18", "@chakra-ui/react": "2.5.5", "@chakra-ui/system": "^2.5.5", "@chakra-ui/theme-tools": "^2.0.16", "@emotion/cache": "^11.10.7", "@emotion/react": "11.0.0", "@emotion/styled": "11.0.0", "@fontsource/open-sans": "^4.5.14", "@fontsource/raleway": "^4.5.12", "@fontsource/roboto": "^4.5.8", "@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@fullcalendar/resource-timeline": "^6.1.15", "@fullcalendar/timegrid": "^6.1.15", "@graphql-codegen/cli": "^3.3.0", "@graphql-codegen/typescript": "^3.0.3", "@graphql-codegen/typescript-operations": "^3.0.3", "@graphql-codegen/typescript-urql": "^3.7.3", "@supabase/supabase-js": "^2.43.1", "@urql/exchange-graphcache": "^6.0.1", "apexcharts": "^3.38.0", "chakra-react-select": "^4.6.0", "classnames": "^2.3.2", "date-fns": "^4.1.0", "eslint": "^8.0.1", "file-saver": "^2.0.5", "formik": "^2.2.9", "framer-motion": "6.2.9", "graphql": "^16.6.0", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "match-sorter": "^6.3.1", "moment": "^2.29.4", "nouislider": "^15.7.0", "react": "^18.1.0", "react-apexcharts": "^1.4.0", "react-bootstrap-sweetalert": "^5.2.0", "react-custom-scrollbars": "^4.2.1", "react-datetime": "^3.2.0", "react-dom": "^18.1.0", "react-hook-form": "^7.43.9", "react-icons": "^5.2.0", "react-jvectormap": "^0.0.16", "react-router-dom": "5", "react-swipeable-views": "^0.14.0", "react-table": "^7.8.0", "react-tagsinput": "^3.20.1", "react-to-print": "^2.15.1", "sass": "^1.62.0", "stylis": "^4.1.3", "stylis-plugin-rtl": "^2.1.1", "typescript": "^5.1.6", "urql": "^4.0.0", "uuid": "^9.0.1", "vite-plugin-checker": "^0.6.4", "vite-plugin-handlebars": "^2.0.0", "web-vitals": "^2.1.4", "xlsx": "^0.18.5", "yup": "^1.0.2"}, "resolutions": {"react-error-overlay": "^6.0.11"}, "scripts": {"start": "vite", "build": "tsc && vite build", "serve": "vite preview", "generate": "graphql-codegen --config codegen.yml"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@eslint/eslintrc": "^3.0.2", "@eslint/js": "^9.0.0", "@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.0.1", "@testing-library/user-event": "^14.1.0", "@types/eslint": "^8.56.7", "@types/file-saver": "^2.0.7", "@types/jest": "^28.1.1", "@types/node": "^12.0.0", "@types/react": "^18.0.9", "@types/react-custom-scrollbars": "^4.0.10", "@types/react-dom": "^18.0.4", "@types/react-router-dom": "^5.3.3", "@types/react-table": "^7.7.14", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^6.4.0", "@vitejs/plugin-react": "^4.2.1", "eslint-config-react-app": "^7.0.1", "eslint-config-standard-with-typescript": "^43.0.1", "eslint-plugin-import": "^2.25.2", "eslint-plugin-n": "^15.0.0 || ^16.0.0 ", "eslint-plugin-promise": "^6.0.0", "eslint-plugin-react": "^7.34.1", "globals": "^15.0.0", "typescript-eslint": "^7.5.0", "vite": "^5.2.8", "vite-plugin-svgr": "^4.2.0", "vite-tsconfig-paths": "^4.3.2"}}