import React, { useEffect, useState } from "react";
import {
  Button,
  Flex,
  FormControl,
  FormLabel,
  Grid,
  GridItem,
  Select,
  Text,
  useToast,
  useColorModeValue,
  Box,
  Input,
} from "@chakra-ui/react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { useLocation, useHistory } from "react-router-dom";
import {
  Clinic,
  Employee,
  Schedule,
  useEditSchedulesMutation,
  useGetSchedulesQuery,
} from "../../generated/graphql"; // GraphQL mutation for editing schedules

// Time options
const timeOptions = [
  { id: "null", t: "Select" },
  { id: "1", t: "01:00:00" },
  { id: "2", t: "02:00:00" },
  { id: "3", t: "03:00:00" },
  { id: "4", t: "04:00:00" },
  { id: "5", t: "05:00:00" },
  { id: "6", t: "06:00:00" },
  { id: "7", t: "07:00:00" },
  { id: "8", t: "08:00:00" },
  { id: "9", t: "09:00:00" },
  { id: "10", t: "10:00:00" },
  { id: "11", t: "11:00:00" },
  { id: "12", t: "12:00:00" },
  { id: "13", t: "13:00:00" },
  { id: "14", t: "14:00:00" },
  { id: "15", t: "15:00:00" },
  { id: "16", t: "16:00:00" },
  { id: "17", t: "17:00:00" },
  { id: "18", t: "18:00:00" },
  { id: "19", t: "19:00:00" },
  { id: "20", t: "20:00:00" },
  { id: "21", t: "21:00:00" },
  { id: "22", t: "22:00:00" },
  { id: "23", t: "23:00:00" },
  { id: "00", t: "00:00:00" },
];

// Days of the week
const daysOfWeek = [
  "MONDAY",
  "TUESDAY",
  "WEDNESDAY",
  "THURSDAY",
  "FRIDAY",
  "SATURDAY",
  "SUNDAY",
];

interface ScheduleFormInput {
  scheduleId: number;
  startTime: string;
  endTime: string;
  description: string;
}

interface LocationState {
  clinic?: Clinic;
  employee?: Employee;
}

const EditSchedules: React.FC = () => {
  const toast = useToast({ position: "top" });
  const location = useLocation<LocationState>();
  const history = useHistory();
  const { clinic, employee } = location.state || {};
  if (!clinic && !employee) history.push("departments");
  const [{ data }] = useGetSchedulesQuery({
    variables: {
      ownerId: clinic ? clinic.id : employee!.id,
      owner: clinic ? "clinic" : "employee",
    },
    requestPolicy: "network-only",
  }); // Fetch schedules using the hook
  // Get schedules from clinic or employee
  const [schedules, setSchedules] = useState<Schedule[]>([]);
  useEffect(() => {
    setSchedules(data?.getSchedules as Schedule[]);
  }, [data]);
  if (!schedules) history.push("departments");
  const [, editSchedules] = useEditSchedulesMutation();

  // Setup form with default values
  const { handleSubmit, control, reset } = useForm({
    defaultValues: {
      schedules: schedules!.map((schedule) => ({
        scheduleId: schedule.id,
        startTime: schedule.onTime,
        endTime: schedule.offTime,
        description: schedule.description || "",
      })),
    },
  });

  // Set schedules when component mounts
  useEffect(() => {
    reset({
      schedules: schedules!.map((schedule) => ({
        scheduleId: schedule.id,
        startTime: schedule.onTime,
        endTime: schedule.offTime,
        description: schedule.description || "",
      })),
    });
  }, [schedules, reset]);

  const onSubmit = async (data: { schedules: ScheduleFormInput[] }) => {
    const formattedSchedules = data.schedules.map((schedule) => ({
      scheduleId: schedule.scheduleId,
      onTime: schedule.startTime,
      offTime: schedule.endTime,
      description: schedule.description,
    }));

    try {
      const { data: responseData } = await editSchedules({
        args: { schedules: formattedSchedules },
      });

      if (responseData?.editSchedules.status) {
        toast({
          title: "Success",
          description: "Schedules updated successfully",
          status: "success",
          isClosable: true,
        });
        history.push("departments");
      } else {
        toast({
          title: "Error",
          description:
            responseData?.editSchedules.error?.message ||
            "Something went wrong",
          status: "error",
          isClosable: true,
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update schedules",
        status: "error",
        isClosable: true,
      });
    }
  };

  const textColor = useColorModeValue("gray.700", "white");
  const bgForm = useColorModeValue("white", "gray.600");

  return (
    <Flex
      w="100%"
      h="100%"
      alignItems="center"
      justifyContent="center"
      mb="60px"
      mt={{ base: "50px", md: "50px" }}
    >
      <Flex
        direction="column"
        w="80%"
        background="transparent"
        borderRadius="15px"
        p="40px"
        mx={{ base: "100px" }}
        m={{ base: "20px", md: "auto" }}
        bg={bgForm}
        boxShadow={useColorModeValue(
          "0px 5px 14px rgba(0, 0, 0, 0.05)",
          "unset"
        )}
      >
        <Text
          fontSize="xl"
          color={textColor}
          fontWeight="bold"
          textAlign="center"
          mb="12px"
        >
          Edit {clinic ? "Clinic" : "Employee"} Schedules
        </Text>

        <form onSubmit={handleSubmit(onSubmit)}>
          <Grid templateColumns="repeat(3, 1fr)" gap={6}>
            {schedules!.map((_schedule, index) => (
              <GridItem key={index} colSpan={3}>
                <Flex alignItems="center" mt={4}>
                  <Box
                    minWidth="150px"
                    display="flex"
                    justifyContent="flex-end"
                    mr={4}
                  >
                    <Text fontWeight="bold">{daysOfWeek[index]}</Text>
                  </Box>

                  <FormControl mr={4}>
                    <FormLabel htmlFor={`startTime-${index}`}>
                      Start Time
                    </FormLabel>
                    <Controller
                      name={`schedules.${index}.startTime`}
                      control={control}
                      render={({ field }) => (
                        <Select {...field}>
                          {timeOptions.map((t) => (
                            <option key={t.id} value={t.t}>
                              {t.t}
                            </option>
                          ))}
                        </Select>
                      )}
                    />
                  </FormControl>

                  <FormControl>
                    <FormLabel htmlFor={`endTime-${index}`}>End Time</FormLabel>
                    <Controller
                      name={`schedules.${index}.endTime`}
                      control={control}
                      render={({ field }) => (
                        <Select {...field}>
                          {timeOptions.map((t) => (
                            <option key={t.id} value={t.t}>
                              {t.t}
                            </option>
                          ))}
                        </Select>
                      )}
                    />
                  </FormControl>

                  <FormControl mr={4}>
                    <FormLabel htmlFor={`description-${index}`}>
                      Description
                    </FormLabel>
                    <Controller
                      name={`schedules.${index}.description`}
                      control={control}
                      render={({ field }) => (
                        <Input {...field} placeholder="Enter Description" />
                      )}
                    />
                  </FormControl>
                </Flex>
              </GridItem>
            ))}
          </Grid>

          <Button mt="6" colorScheme="teal" w="full" type="submit">
            Save Schedules
          </Button>
        </form>
      </Flex>
    </Flex>
  );
};

export default EditSchedules;
