# PowerSync Self-Hosting Configuration
# Updated for proper self-hosting setup - 2025-07-22

# Settings for source database replication
replication:
  connections:
    - type: postgresql
      # Connection URI for the source database
      uri: "********************************************/TalisiaPOS?sslmode=disable"
      # SSL settings - disable only for local/private networks
      sslmode: disable

# Connection settings for sync bucket storage
storage:
  type: postgresql
  # Hardcoded connection for sync bucket storage
  uri: "********************************************/TalisiaPOS?sslmode=disable"
  sslmode: disable

# The port which the PowerSync API server will listen on
port: 8080

# Settings for client authentication
client_auth:
  # JWT audience - should match your client configuration
  audience: ["talisia-powersync"]

  # Hardcoded JWT secret for development/self-hosting
  secret: "TaLiS1a_P0w3rSyNc_JWT_S3cr3t_K3y_2025_Pr0duCt10n_V3ry_L0ng_4nd_S3cur3_K3y"

  # Alternative: Use JWKS URI for production
  # jwks_uri: "https://your-auth-server.com/.well-known/jwks.json"

# Specify sync rules
sync_rules:
  content: |
    # Multi-tenant sync rules for Talisia POS
    # Each client only gets data for their company based on JWT companyId

    bucket_definitions:
      # Company-specific data bucket
      company_data:
        # Extract companyId from JWT and make it available as company_id
        parameters: SELECT request.jwt() ->> 'companyId' as company_id
        data:
          # Core company and user data
          - SELECT * FROM "Company" WHERE id = bucket.company_id
          - SELECT * FROM "User" WHERE "companyId" = bucket.company_id
          - SELECT * FROM "Employee" WHERE "companyId" = bucket.company_id
          - SELECT * FROM "Department" WHERE "companyId" = bucket.company_id
          - SELECT * FROM "Role" WHERE "companyId" = bucket.company_id
          - SELECT * FROM "Permission" WHERE "companyId" = bucket.company_id

          # Inventory and items
          - SELECT * FROM "Item" WHERE "companyId" = bucket.company_id
          - SELECT * FROM "Category" WHERE "companyId" = bucket.company_id
          - SELECT * FROM "Unit" WHERE "companyId" = bucket.company_id
          - SELECT * FROM "BatchStock" WHERE "companyId" = bucket.company_id
          - SELECT * FROM "Inventory" WHERE "companyId" = bucket.company_id
          - SELECT * FROM "Store" WHERE "companyId" = bucket.company_id
          - SELECT * FROM "StoreItemStock" WHERE "companyId" = bucket.company_id
          - SELECT * FROM "Transfer" WHERE "companyId" = bucket.company_id

          # Financial data
          - SELECT * FROM "Bill" WHERE "companyId" = bucket.company_id
          - SELECT * FROM "Payment" WHERE "companyId" = bucket.company_id
          - SELECT * FROM "Expense" WHERE "companyId" = bucket.company_id

          # System and configuration
          - SELECT * FROM "Feature" WHERE "companyId" = bucket.company_id
          - SELECT * FROM "Type" WHERE "companyId" = bucket.company_id
          - SELECT * FROM "Approval" WHERE "companyId" = bucket.company_id
          - SELECT * FROM "Import" WHERE "companyId" = bucket.company_id

          # Audit and logs
          - SELECT * FROM "AuditBaseEntity" WHERE "companyId" = bucket.company_id
          - SELECT * FROM "ErrorLog" WHERE "companyId" = bucket.company_id
          - SELECT * FROM "SyncHistory" WHERE "companyId" = bucket.company_id

# Settings for telemetry reporting
# See https://docs.powersync.com/self-hosting/telemetry
telemetry:
  # Opt out of reporting anonymized usage metrics to PowerSync telemetry service
  disable_telemetry_sharing: false

# Optional: Logging configuration
logging:
  level: "debug"
  format: "json"
