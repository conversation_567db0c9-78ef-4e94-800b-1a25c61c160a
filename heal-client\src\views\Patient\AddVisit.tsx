import React, { useContext, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import {
  Flex,
  Button,
  FormControl,
  FormLabel,
  Input,
  Text,
  useColorModeValue,
  FormErrorMessage,
  useToast,
  Grid,
  GridItem,
  Select,
  Box,
  Highlight,
  HStack,
  InputGroup,
  InputRightElement,
} from "@chakra-ui/react";
import {
  Patient,
  useAddVisitMutation,
  useGetCategoriesQuery,
  useGetClinicsQuery,
  useGetEmployeesQuery,
} from "../../generated/graphql";
import { useHistory, useLocation } from "react-router-dom";
import { HSeparator } from "../../components/Separator/Separator";
import { toAge } from "../../utils/Helpers";
import { AsyncSelect } from "chakra-react-select";
import CustomCheckbox from "../../theme/components/CustomCheckBox";
import FileInput from "../../components/FileInput";
import { AppContext } from "../../AppContext";
import { ActionType } from "../../interfaces/Types";

interface IAddVisitProps {}

interface LocationState {
  patient: Patient;
}

interface IVisitInput {
  status: string;
  type: string;
  reason: string;
  clientId: number;
  careTakerName: string;
  careTakerRelationship: string;
  careTakerPhone: string;
  paymentMethod: string;
  insuranceId: string;
  insuranceScheme: string;
  checkInType: string;
  referredBy: string;
  clinic: number;
  ticket: string;
  doctor: number;
}

const AddVisit: React.FC<IAddVisitProps> = () => {
  const history = useHistory();
  const location = useLocation();

  const { dispatch, state } = useContext(AppContext);

  const [visitTypeOptions, setVisitTypeOptions] = useState<any[]>([]);
  const [clinicOptions, setClinicOptions] = useState<any[]>([]);
  const [doctorOptions, setDoctorOptions] = useState<any[]>([]);
  const [paymentMethodOptions, setPaymentMethodOptions] = useState<any[]>([]);
  if (!location.state) history.push("patients");
  const { patient } = location.state as LocationState;
  if (!patient.id || patient.id <= 0) history.push("patients");
  const toast = useToast({ position: "top" });
  const [, addVisit] = useAddVisitMutation();
  const [error, setError] = useState("");
  const [paymentMethodState, setPaymentMethodState] = useState("");
  const [paymentMethodVerified, setPaymentMethodVerified] = useState(false);
  const [{ data: visitTypes, fetching: fetchingVisitTypes }, getVisitTypes] =
    useGetCategoriesQuery({
      variables: { type: "Visit type" }, // Fetch the patient visit type
    });

  const [clearFileInput, setClearFileInput] = useState(false);
  const [receiptUrl, setReceiptUrlState] = useState("");
  const [fileLoading, setFileLoading] = useState(false);
  const [fileInputError, setFileInputError] = useState("");
  const [{ data: doctors }, getDoctors] = useGetEmployeesQuery();

  const [{ data: clinics, fetching: fetchingClinics }, getClinics] =
    useGetClinicsQuery();
  const [
    { data: paymentMethods, fetching: fetchingPaymentMethods },
    getPaymentMethods,
  ] = useGetCategoriesQuery({
    variables: { type: "Insurance type" }, // Fetch the patient visit type
  });

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    formState: { errors, isSubmitting, isValid },
  } = useForm<IVisitInput>();

  async function onSubmit(values: IVisitInput) {
    setError("");
    const args = {
      status: values.status,
      type: values.type,
      reason: values.reason,
      clientId: patient.id,
      careTakerName: values.careTakerName,
      careTakerPhone: values.careTakerPhone,
      careTakerRelationship: values.careTakerRelationship,
      checkInType: values.checkInType,
      clinicId: values.clinic,
      doctorId: values.doctor,
      referralType: values.referredBy,
      ticket: values.ticket,
    };
    const { data } = await addVisit({ params: args });

    if (data?.addVisit.error) {
      toast({
        title: "Visit add failed!",
        variant: "left-accent",
        status: "error",
        isClosable: true,
      });
      return setError(data?.addVisit.error.message);
    } else if (!data?.addVisit.error) {
      reset();
      history.push("patients");
      return toast({
        title: "Visit added successfully!",
        variant: "left-accent",
        status: "success",
        isClosable: true,
      });
    }
  }

  const loadVisitTypes = async (inputValue: string) => {
    if (visitTypeOptions.length === 0) {
      // Fetch if not already done
      await getVisitTypes({ requestPolicy: "network-only" });
    }

    if (visitTypes) {
      const options = visitTypes.getCategories.map((r) => ({
        label: r.name,
        value: r.name,
      }));
      setVisitTypeOptions(options); // Store options in state
    }

    // Filter options based on inputValue
    if (inputValue.length > 0) {
      return visitTypeOptions.filter((option) =>
        option.label.toLowerCase().includes(inputValue.toLowerCase())
      );
    }

    return visitTypeOptions; // Return all options if no input
  };

  const loadClinics = async (inputValue: string) => {
    if (clinicOptions.length === 0) {
      // Fetch if not already done
      await getClinics({ requestPolicy: "network-only" });
    }

    if (clinics) {
      const options = clinics.getClinics.map((r) => ({
        label: r.name,
        value: r.id,
      }));
      setClinicOptions(options); // Store options in state
    }

    // Filter options based on inputValue
    if (inputValue.length > 0) {
      return clinicOptions.filter((option) =>
        option.label.toLowerCase().includes(inputValue.toLowerCase())
      );
    }

    return clinicOptions; // Return all options if no input
  };

  const loadDoctors = async (inputValue: string) => {
    if (doctorOptions.length === 0) {
      // Fetch if not already done
      await getDoctors({ requestPolicy: "network-only" });
    }

    if (doctors) {
      const options = doctors.getEmployees.map((r) => ({
        label: r.firstname + " " + r.lastname,
        value: r.employee?.id,
      }));
      setDoctorOptions(options); // Store options in state
    }

    // Filter options based on inputValue
    if (inputValue.length > 0) {
      return doctorOptions.filter((option) =>
        option.label.toLowerCase().includes(inputValue.toLowerCase())
      );
    }

    return doctorOptions; // Return all options if no input
  };

  const loadPaymentMethods = async (inputValue: string) => {
    if (paymentMethodOptions.length === 0) {
      // Fetch if not already done
      await getPaymentMethods({ requestPolicy: "network-only" });
    }

    if (paymentMethods) {
      const options = paymentMethods.getCategories.map((r) => ({
        label: r.name,
        value: r.name,
      }));
      setPaymentMethodOptions(options); // Store options in state
    }

    // Filter options based on inputValue
    if (inputValue.length > 0) {
      return paymentMethodOptions.filter((option) =>
        option.label.toLowerCase().includes(inputValue.toLowerCase())
      );
    }

    return paymentMethodOptions; // Return all options if no input
  };

  useEffect(() => {
    if (
      state.pendingFile?.path === location.pathname &&
      state.pendingFile.uploadingFileUrl
    )
      setReceiptUrlState(state.pendingFile.uploadingFileUrl);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Preload visit types on component mount
  useEffect(() => {
    if (paymentMethodOptions.length === 0) {
      loadPaymentMethods(""); // Preload all options on getting visit types from server
    }

    if (visitTypeOptions.length === 0) {
      loadVisitTypes("");
    }

    if (clinicOptions.length === 0) {
      loadClinics("");
    }

    if (doctorOptions.length === 0) {
      loadDoctors("");
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visitTypes, paymentMethods]);

  const handleChange = (value: any, other: any) => {
    setValue(other.name, value.value);
    if (other.name === "paymentMethod") {
      setPaymentMethodState(value.value);
      if (value.value === "Cash") setPaymentMethodVerified(true);
      if (value.value !== "Cash") setPaymentMethodVerified(false);
    }
  };

  const handleCareTakerInputs = (value: boolean) => {
    console.log("handleCareTakerInputs: ", value);
    if (value === true) {
      setValue("careTakerName", patient.nextOfKinName);
      setValue("careTakerRelationship", patient.nextOfKinRelationship);
      setValue("careTakerPhone", patient.nextOfKinPhone);
    } else {
      setValue("careTakerName", "");
      setValue("careTakerRelationship", "");
      setValue("careTakerPhone", "");
    }
  };

  const setReceiptUrl: any = (url: string) => {
    setReceiptUrlState(url);
    dispatch({
      type: ActionType.SET_UPLOADING_FILE_URL,
      pendingFile: {
        uploadingFileUrl: url,
        path: location.pathname,
      },
    });
  };

  const generateRandomTicketNumber = () => {
    // Generate a random uppercase letter from A to Z
    const randomLetter = String.fromCharCode(
      65 + Math.floor(Math.random() * 26)
    );

    // Generate a random two-digit number between 00 and 99
    const randomNumber = Math.floor(Math.random() * 100)
      .toString()
      .padStart(2, "0");

    // Combine the random letter and number
    const randomString = randomLetter + randomNumber;

    // Set the value of "ticket" input and validate
    setValue("ticket", randomString, { shouldValidate: true });
  };
  const textColor = useColorModeValue("gray.700", "white");
  const bgForm = useColorModeValue("white", "navy.800");
  const labelColor = useColorModeValue("black", "white"); // Dynamically set the color

  const fileError = (value: string): void => {
    setFileInputError(value);
  };

  return (
    <Flex
      w="100%"
      h="100%"
      alignItems="center"
      justifyContent="center"
      mb="60px"
      mt={{ base: "50px", md: "50px" }}
    >
      <Flex
        zIndex="2"
        direction="column"
        w="80%"
        background="transparent"
        borderRadius="15px"
        p="40px"
        mx={{ base: "100px" }}
        m={{ base: "20px", md: "auto" }}
        bg={bgForm}
        boxShadow={useColorModeValue(
          "0px 5px 14px rgba(0, 0, 0, 0.05)",
          "unset"
        )}
      >
        <Text
          fontSize="xl"
          color={textColor}
          fontWeight="bold"
          textAlign="center"
          mb="12px"
        >
          Add Visit
        </Text>
        {error && (
          <Text pb="15px" m={"auto"} color="red.500" textColor="red.300">
            {error}
          </Text>
        )}
        <Flex flexDirection={"column"} as={GridItem} colSpan={8}>
          <HSeparator mt="3px" mb="3px" />
          <Box>
            <HStack justify="space-around" spacing={4}>
              <Highlight
                query="Name:"
                styles={{ fontWeight: "bold", color: labelColor }}
              >
                {`Name: ${patient.firstname} ${patient.middlename} ${patient.lastname}`}
              </Highlight>
              <Highlight
                query="Age:"
                styles={{ fontWeight: "bold", color: labelColor }}
              >
                {`Age: ${toAge(patient.dateOfBirth)}`}
              </Highlight>
              <Highlight
                query="Gender:"
                styles={{ fontWeight: "bold", color: labelColor }}
              >
                {`Gender: ${patient.gender}`}
              </Highlight>
              <Highlight
                query="Group:"
                styles={{ fontWeight: "bold", color: labelColor }}
              >
                {`Group: ${
                  patient.status === "normal" &&
                  Number(toAge(patient.dateOfBirth).split(" ")[0]) > 55
                    ? "Elderly"
                    : patient.status
                }`}
              </Highlight>
            </HStack>
          </Box>
          <HSeparator mb="3px" mt="3px" />
        </Flex>
        <form onSubmit={handleSubmit(onSubmit)}>
          <Grid
            templateColumns="repeat(9, 1fr)"
            gap={6}
            mt={3}
            alignContent={"center"}
            alignItems={"center"}
          >
            {/* Visit Information */}
            <FormControl
              as={GridItem}
              colSpan={3}
              isInvalid={errors.status as any}
            >
              <FormLabel htmlFor="status" fontSize="xs">
                Status
              </FormLabel>
              <Select
                id="status"
                variant="filled"
                defaultValue="normal"
                {...register("status", {
                  required: "Visit status is required",
                })}
              >
                <option value="normal">Normal</option>
                <option value="pregnant">Pregnant</option>
                <option value="critical">Critical</option>
              </Select>
              <FormErrorMessage mb="10px" fontSize="xs">
                {errors.status && (errors.status.message as any)}
              </FormErrorMessage>
            </FormControl>
            <FormControl
              as={GridItem}
              colSpan={3}
              isInvalid={errors.type as any}
            >
              <FormLabel htmlFor="type" fontSize="xs">
                Type
              </FormLabel>
              <AsyncSelect
                variant="flushed"
                isClearable
                isSearchable={true}
                isLoading={fetchingVisitTypes}
                defaultOptions={visitTypeOptions}
                loadOptions={loadVisitTypes}
                size="sm"
                placeholder="Select item"
                closeMenuOnSelect={true}
                {...register("type", {
                  required: "Type is required",
                })}
                onChange={handleChange}
                escapeClearsValue={true}
                hideSelectedOptions={true}
                styles={{
                  container: (base) => ({
                    ...base,
                    width: "100%",
                    color: "navy",
                  }),
                }}
              />
              <FormErrorMessage mb="10px" fontSize="xs">
                {errors.type && (errors.type.message as any)}
              </FormErrorMessage>
            </FormControl>
            <FormControl
              as={GridItem}
              colSpan={3}
              isInvalid={errors.reason as any}
            >
              <FormLabel htmlFor="reason" fontSize="xs">
                Reason
              </FormLabel>
              <Input
                variant="filled"
                ms="4px"
                type="text"
                fontSize="xs"
                placeholder="Reason for the visit"
                size="lg"
                id="reason"
                {...register("reason", {
                  required: "Reason for visit is required",
                })}
              />
              <FormErrorMessage mb="10px" fontSize="xs">
                {errors.reason && (errors.reason.message as any)}
              </FormErrorMessage>
            </FormControl>
            <Flex flexDirection={"column"} as={GridItem} colSpan={9}>
              <HSeparator mt="3px" mb="3px" />

              <HStack justify="space-around" align="center" mt="0px" mb="0px">
                <Text textAlign="center">Care Taker Information:</Text>{" "}
                <CustomCheckbox
                  onValueChange={handleCareTakerInputs}
                  name="Same as next of kin information"
                />
              </HStack>

              <HSeparator mb="3px" mt="3px" />
            </Flex>
            <FormControl
              as={GridItem}
              colSpan={3}
              mb={0}
              isInvalid={errors.careTakerName as any}
            >
              <FormLabel htmlFor="careTakerName" fontSize="xs">
                Care Taker Name
              </FormLabel>
              <Input
                variant="filled"
                ms="4px"
                type="text"
                fontSize="xs"
                placeholder="Care Taker Name"
                mb="4px"
                size="lg"
                id="careTakerName"
                {...register("careTakerName", {
                  required: "Care Taker Name is required",
                })}
              />
              <FormErrorMessage mb="10px" fontSize="xs">
                {errors.careTakerName && (errors.careTakerName.message as any)}
              </FormErrorMessage>
            </FormControl>
            <FormControl
              as={GridItem}
              colSpan={3}
              mb={0}
              isInvalid={errors.careTakerPhone as any}
            >
              <FormLabel htmlFor="careTakerPhone" fontSize="xs">
                Care Taker Phone
              </FormLabel>
              <Input
                variant="filled"
                ms="4px"
                type="text"
                fontSize="xs"
                placeholder="Care Taker Phone"
                mb="4px"
                size="lg"
                id="careTakerPhone"
                {...register("careTakerPhone", {
                  required: "Care Taker Phone is required",
                })}
                maxLength={12}
                onChange={(e) => {
                  let value = e.target.value.replace(/\D/g, ""); // Remove non-digit characters
                  // Ensure the first character is "0"
                  value = value.replace(/^0*(\d*)/, "0$1");
                  // Limit to 12 characters, including spaces
                  value = value.slice(0, 12);
                  const formattedValue = value.replace(
                    /(\d{1})(\d{3})(\d{3})(\d{3})/,
                    "$1$2 $3 $4"
                  ); // Add spaces between numbers
                  e.target.value = formattedValue;
                }}
              />
              <FormErrorMessage mb="10px" fontSize="xs">
                {errors.careTakerPhone &&
                  (errors.careTakerPhone.message as any)}
              </FormErrorMessage>
            </FormControl>
            <FormControl
              as={GridItem}
              colSpan={3}
              mb={0}
              isInvalid={errors.careTakerRelationship as any}
            >
              <FormLabel htmlFor="careTakerRelationship" fontSize="xs">
                Care Taker Relationship
              </FormLabel>
              <Input
                variant="filled"
                ms="4px"
                type="text"
                fontSize="xs"
                placeholder="Care Taker Relationship"
                mb="4px"
                size="lg"
                id="careTakerRelationship"
                {...register("careTakerRelationship", {
                  required: "Care Taker Relationship is required",
                })}
              />
              <FormErrorMessage mb="10px" fontSize="xs">
                {errors.careTakerRelationship &&
                  (errors.careTakerRelationship.message as any)}
              </FormErrorMessage>
            </FormControl>
            <Flex flexDirection={"column"} as={GridItem} colSpan={9}>
              <HSeparator mt="3px" mb="3px" />

              <HStack justify="space-around" align="center" mt="0px" mb="0px">
                <Text textAlign="center">Payment method:</Text>
              </HStack>
              <HSeparator mb="3px" mt="3px" />
            </Flex>
            <FormControl
              as={GridItem}
              colSpan={
                paymentMethodState === "Cash" || paymentMethodState === ""
                  ? 9
                  : 3
              } // Span 9 columns when cash only as the payment method is present or when empty
              gridColumn={
                paymentMethodState === "Cash" || paymentMethodState === ""
                  ? "span 9 / span 9"
                  : "span 3"
              }
              isInvalid={errors.paymentMethod as any}
            >
              <FormLabel htmlFor="paymentMethod" fontSize="xs">
                Payment method
              </FormLabel>
              <AsyncSelect
                variant="flushed"
                isClearable
                isSearchable={true}
                isLoading={fetchingPaymentMethods}
                defaultOptions={paymentMethodOptions}
                loadOptions={loadPaymentMethods}
                size="sm"
                placeholder="Select payment method"
                closeMenuOnSelect={true}
                {...register("paymentMethod", {
                  required: "Payment method is required",
                })}
                onChange={handleChange}
                escapeClearsValue={true}
                hideSelectedOptions={true}
                styles={{
                  container: (base) => ({
                    ...base,
                    width: "100%",
                    color: "navy",
                  }),
                }}
              />
              <FormErrorMessage mb="10px" fontSize="xs">
                {errors.paymentMethod && (errors.paymentMethod.message as any)}
              </FormErrorMessage>
            </FormControl>
            {paymentMethodState && paymentMethodState !== "Cash" && (
              <>
                <FormControl
                  as={GridItem}
                  colSpan={3}
                  isInvalid={errors.insuranceScheme as any}
                >
                  <FormLabel htmlFor="insuranceScheme" fontSize="xs">
                    Insurance Scheme
                  </FormLabel>
                  <AsyncSelect
                    variant="flushed"
                    isClearable
                    isSearchable={true}
                    isLoading={fetchingPaymentMethods}
                    defaultOptions={paymentMethodOptions}
                    loadOptions={loadPaymentMethods}
                    size="sm"
                    placeholder="Select insurance scheme"
                    closeMenuOnSelect={true}
                    {...register("insuranceScheme", {
                      required: "Insurance scheme is required",
                    })}
                    onChange={handleChange}
                    escapeClearsValue={true}
                    hideSelectedOptions={true}
                    styles={{
                      container: (base) => ({
                        ...base,
                        width: "100%",
                        color: "navy",
                      }),
                    }}
                  />
                  <FormErrorMessage mb="10px" fontSize="xs">
                    {errors.insuranceScheme &&
                      (errors.insuranceScheme.message as any)}
                  </FormErrorMessage>
                </FormControl>

                <FormControl
                  as={GridItem}
                  colSpan={3}
                  isInvalid={errors.insuranceId as any}
                >
                  <FormLabel htmlFor="insuranceId" fontSize="xs">
                    Insurance ID
                  </FormLabel>
                  <Input
                    variant="filled"
                    ms="4px"
                    type="text"
                    fontSize="xs"
                    placeholder="Insurance ID for the patient"
                    size="lg"
                    id="insuranceId"
                    {...register("insuranceId", {
                      required: "Insurance ID is required",
                    })}
                  />
                  <FormErrorMessage mb="10px" fontSize="xs">
                    {errors.insuranceId && (errors.insuranceId.message as any)}
                  </FormErrorMessage>
                </FormControl>
              </>
            )}{" "}
            <Flex flexDirection={"column"} as={GridItem} colSpan={9}>
              <HSeparator mt="3px" mb="3px" />

              <HStack justify="space-around" align="center" mt="0px" mb="0px">
                <Text textAlign="center">Admission Details:</Text>
              </HStack>
              <HSeparator mb="3px" mt="3px" />
            </Flex>
            <FormControl
              as={GridItem}
              colSpan={3}
              isInvalid={errors.checkInType as any}
            >
              <FormLabel htmlFor="checkInType" fontSize="xs">
                Checked In
              </FormLabel>
              <Select
                id="checkInType"
                variant="filled"
                defaultValue="normal"
                {...register("checkInType", {
                  required: "Check-in type is required",
                })}
              >
                <option value="opd">OPD</option>
                <option value="ipd">IPD</option>
                <option value="other">Other</option>
              </Select>
              <FormErrorMessage mb="10px" fontSize="xs">
                {errors.checkInType && (errors.checkInType.message as any)}
              </FormErrorMessage>
            </FormControl>
            <FormControl
              as={GridItem}
              colSpan={3}
              isInvalid={errors.referredBy as any}
            >
              <FormLabel htmlFor="referredBy" fontSize="xs">
                Referred By
              </FormLabel>
              <Select
                id="referredBy"
                variant="filled"
                defaultValue="normal"
                {...register("referredBy", {
                  required: "Check-in type is required",
                })}
              >
                <option value="opd">Self</option>
                <option value="ipd">External</option>
                <option value="other">Other</option>
              </Select>
              <FormErrorMessage mb="10px" fontSize="xs">
                {errors.referredBy && (errors.referredBy.message as any)}
              </FormErrorMessage>
            </FormControl>
            <FormControl
              as={GridItem}
              colSpan={3}
              isInvalid={errors.clinic as any}
            >
              <FormLabel htmlFor="clinic" fontSize="xs">
                Clinic
              </FormLabel>
              <AsyncSelect
                variant="flushed"
                isClearable
                isSearchable={true}
                isLoading={fetchingClinics}
                defaultOptions={clinicOptions}
                loadOptions={loadClinics}
                size="sm"
                placeholder="Select clinic"
                closeMenuOnSelect={true}
                {...register("clinic", {
                  required: "Clinic is required",
                })}
                onChange={handleChange}
                escapeClearsValue={true}
                hideSelectedOptions={true}
                styles={{
                  container: (base) => ({
                    ...base,
                    width: "100%",
                    color: "navy",
                  }),
                }}
              />
              <FormErrorMessage mb="10px" fontSize="xs">
                {errors.clinic && (errors.clinic.message as any)}
              </FormErrorMessage>
            </FormControl>
            <FormControl
              as={GridItem}
              colSpan={3}
              isInvalid={errors.doctor as any}
            >
              <FormLabel htmlFor="doctor" fontSize="xs">
                Private Doctor
              </FormLabel>
              <AsyncSelect
                variant="flushed"
                isClearable
                isSearchable={true}
                isLoading={fetchingPaymentMethods}
                defaultOptions={doctorOptions}
                loadOptions={loadDoctors}
                size="sm"
                placeholder="Select doctor"
                closeMenuOnSelect={true}
                {...register("doctor", {
                  required: "Payment method is required",
                })}
                onChange={handleChange}
                escapeClearsValue={true}
                hideSelectedOptions={true}
                styles={{
                  container: (base) => ({
                    ...base,
                    width: "100%",
                    color: "navy",
                  }),
                }}
              />
              <FormErrorMessage mb="10px" fontSize="xs">
                {errors.doctor && (errors.doctor.message as any)}
              </FormErrorMessage>
            </FormControl>
            <FormControl
              as={GridItem}
              colSpan={3}
              isInvalid={errors.ticket as any}
            >
              <FormLabel htmlFor="ticket" fontSize="xs">
                Ticket Number
              </FormLabel>

              <InputGroup size="lg">
                <Input
                  variant="filled"
                  ms="4px"
                  type="text"
                  fontSize="2xl" // Large font size
                  fontWeight="bold" // Bold font
                  placeholder="Ticket Number"
                  id="ticket"
                  {...register("ticket")}
                />
                <InputRightElement width="auto">
                  <Button
                    onClick={generateRandomTicketNumber}
                    fontSize="2xl" // Large font size for the button
                    fontWeight="bold" // Bold font
                    colorScheme="teal"
                  >
                    Generate
                  </Button>
                </InputRightElement>
              </InputGroup>

              <FormErrorMessage mb="10px" fontSize="xs">
                {errors.ticket && (errors.ticket.message as any)}
              </FormErrorMessage>
            </FormControl>
            <FormControl
              as={GridItem}
              colSpan={3}
              mb={1}
              isInvalid={fileInputError === "" ? false : true}
            >
              <FormLabel htmlFor="receipt" fontSize="xs">
                Supporting documents
              </FormLabel>

              <FileInput
                setFileUrl={setReceiptUrl}
                defaultUrl={receiptUrl}
                label="Supporting documents"
                clear={clearFileInput}
                setError={fileError}
                defaultClear={(value) => setClearFileInput(value)}
                setLoading={(value) => setFileLoading(value)}
              />
              <FormErrorMessage fontSize="xs">
                {fileInputError}
              </FormErrorMessage>
            </FormControl>
          </Grid>
          {paymentMethodState !== "Cash" && paymentMethodVerified === false && (
            <Button
              fontSize="16px"
              colorScheme="teal"
              variant="solid"
              type="button"
              width="100%"
              mt="24px"
              isLoading={isSubmitting}
              isDisabled={!isValid || isSubmitting}
            >
              Verify Insurance Scheme
            </Button>
          )}
          {paymentMethodState === "" ||
            (paymentMethodVerified === true && (
              <Button
                fontSize="16px"
                colorScheme="teal"
                variant="solid"
                type="submit"
                width="100%"
                mt="24px"
                isLoading={isSubmitting}
                isDisabled={!isValid || isSubmitting || fileLoading}
              >
                Add Visit
              </Button>
            ))}
        </form>
      </Flex>
    </Flex>
  );
};

export default AddVisit;
