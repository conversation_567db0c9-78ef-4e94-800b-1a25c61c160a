import { isAuth } from "../middleware/isAuth";
import {
  Arg,
  Mutation,
  Resolver,
  UseMiddleware,
  Query,
  Ctx,
} from "type-graphql";
import { Feature } from "../entities/Feature";
import { BooleanResponse } from "./user";
import { Like } from "typeorm";
import { Company } from "../entities/Company";
import { logError } from "../utils/utils";
import { MyContext } from "../types";

@Resolver(Feature)
export class FeatureResolver {
  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async addFeature(
    @Arg("name", () => String) name: string,
    @Arg("companyId", () => String) companyId: string,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const userId = req.session.userId;

    try {
      const company = await Company.findOne(companyId);
      if (!company) {
        await logError(
          companyId,
          "Company not found",
          "FEATURE_ADD_COMPANY_NOT_FOUND",
          JSON.stringify({ companyId }),
          "medium",
          `Add feature failed - company not found: ${companyId}`,
          userId
        );
        return {
          status: false,
          error: { target: "general", message: "Company does not exist!" },
        };
      }

      if (!name || name.trim() === "") {
        await logError(
          companyId,
          "Empty feature name",
          "FEATURE_ADD_EMPTY_NAME",
          JSON.stringify({ name }),
          "medium",
          `Add feature failed - empty name provided`,
          userId
        );
        return {
          status: false,
          error: { target: "name", message: "Feature name cannot be empty" },
        };
      }

      const existingFeatures = await Feature.find({
        where: { name: Like(`${name.split(">")[0]}>%`) },
        relations: ["companies"],
      });

      if (existingFeatures) {
        let ourFeatures = existingFeatures.filter((tempFeature) => {
          let boolVar = false;
          tempFeature.companies.forEach((r) => {
            if (r.id === company.id && tempFeature.name !== name)
              boolVar = true;
          });
          return boolVar;
        });

        try {
          for (const f of ourFeatures) {
            let newCompanies: Company[] = [];
            f.companies.forEach((c) => {
              if (c.id !== company.id) newCompanies.push(c);
            });
            f.companies = newCompanies;
            await f.save();
          }
        } catch (err) {
          await logError(
            companyId,
            err.message,
            "FEATURE_ADD_UPDATE_EXISTING_ERROR",
            JSON.stringify(err),
            "high",
            `Failed to update existing features while adding new feature: ${name}`,
            userId
          );
          throw err;
        }
      }

      const feat = await Feature.findOne({
        where: { name },
        relations: ["companies"],
      });

      if (feat) {
        try {
          // use query builder to add feature
          await Feature.createQueryBuilder()
            .relation(Feature, "companies")
            .of(feat)
            .add(company);
        } catch (err) {
          await logError(
            companyId,
            err.message,
            "FEATURE_ADD_UPDATE_ERROR",
            JSON.stringify(err),
            "high",
            `Failed to update existing feature: ${name}`,
            userId
          );
          throw err;
        }
      } else {
        try {
          await Feature.create({
            name,
            companies: [company],
          }).save();
        } catch (err) {
          await logError(
            companyId,
            err.message,
            "FEATURE_ADD_CREATE_ERROR",
            JSON.stringify(err),
            "high",
            `Failed to create new feature: ${name}`,
            userId
          );
          throw err;
        }
      }
    } catch (err) {
      await logError(
        companyId,
        err.message,
        "FEATURE_ADD_ERROR",
        JSON.stringify(err),
        "high",
        `Failed to add feature: ${name}`,
        userId
      );
      return {
        status: false,
        error: { target: "general", message: err.message },
      };
    }
    return { status: true };
  }

  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async editFeature(
    @Arg("id") id: string,
    @Arg("name", () => String) name: string,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    if (!name || name.trim() === "") {
      await logError(
        companyId,
        "Empty feature name",
        "FEATURE_EDIT_EMPTY_NAME",
        JSON.stringify({ id, name }),
        "medium",
        `Edit feature failed - empty name provided for ID: ${id}`,
        userId
      );
      return {
        status: false,
        error: { target: "general", message: "name can not be empty!" },
      };
    }

    const feature = await Feature.findOne(id);
    if (!feature) {
      await logError(
        companyId,
        "Feature not found",
        "FEATURE_EDIT_NOT_FOUND",
        JSON.stringify({ id }),
        "medium",
        `Edit feature failed - feature not found: ${id}`,
        userId
      );
      return {
        status: false,
        error: { target: "general", message: "feature does not exist!" },
      };
    }

    try {
      feature.name = name;
      await feature.save();
    } catch (err) {
      await logError(
        companyId,
        err.message,
        "FEATURE_EDIT_ERROR",
        JSON.stringify(err),
        "high",
        `Failed to edit feature: ${id}`,
        userId
      );
      return {
        status: false,
        error: { target: "general", message: err.message },
      };
    }
    return { status: true };
  }

  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async deleteFeature(
    @Arg("id") id: string,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    try {
      const feature = await Feature.findOne(id);
      if (!feature) {
        await logError(
          companyId,
          "Feature not found",
          "FEATURE_DELETE_NOT_FOUND",
          JSON.stringify({ id }),
          "medium",
          `Delete feature failed - feature not found: ${id}`,
          userId
        );
        return {
          status: false,
          error: { target: "general", message: "Feature not found" },
        };
      }

      await Feature.delete(id);
    } catch (err) {
      await logError(
        companyId,
        err.message,
        "FEATURE_DELETE_ERROR",
        JSON.stringify(err),
        "high",
        `Failed to delete feature: ${id}`,
        userId
      );
      return {
        status: false,
        error: { target: "general", message: err.message },
      };
    }
    return { status: true };
  }

  @Query(() => [Feature])
  @UseMiddleware(isAuth)
  async getFeatures(@Ctx() { req }: MyContext): Promise<Feature[]> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    try {
      return await Feature.find({
        relations: ["companies"],
        order: { name: "ASC" },
      });
    } catch (err) {
      await logError(
        companyId,
        err.message,
        "FEATURE_GET_ALL_ERROR",
        JSON.stringify(err),
        "medium",
        "Failed to fetch features",
        userId
      );
      return [];
    }
  }
}
