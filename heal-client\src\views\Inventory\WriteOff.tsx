import React, { useState } from "react";
import { useForm } from "react-hook-form";
import {
  Flex,
  Button,
  FormControl,
  FormLabel,
  Input,
  Text,
  useColorModeValue,
  FormErrorMessage,
  useToast,
  Grid,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Spinner,
  <PERSON>foot,
  GridItem,
} from "@chakra-ui/react";
import {
  useGetAllItemsQuery,
  useGetWriteOffsByCompanyQuery,
  useWriteOffItemsMutation,
  useGetStoresQuery,
  Item,
} from "../../generated/graphql";
import { toDateTime } from "../../utils/Helpers";
import AllItemsInput from "./components/AllItemsInput";
import StoreInput from "./components/StoreInput";
import BatchInput from "./components/BatchInput";
import UnitInput from "./components/UnitInput";

interface IWriteOffProps {}

interface IWriteOffItem {
  itemId: number;
  quantity: number;
  reason: string;
  locationId: number;
  batch: string;
  unit: string;
}

const WriteOff: React.FC<IWriteOffProps> = () => {
  const toast = useToast({
    position: "top",
  });

  const nameColor = useColorModeValue("gray.500", "white");

  const [fetchVars, setFetchVars] = useState<{
    storeId: number | undefined;
    itemId: number | undefined;
  }>({
    storeId: 0,
    itemId: 0,
  });

  const [, writeOff] = useWriteOffItemsMutation();
  const [{ data: items }] = useGetAllItemsQuery();
  const [{ data: stores }] = useGetStoresQuery();
  const [{ data: writeOffs, fetching: fetchingWriteOffs }, reGetWriteOffs] =
    useGetWriteOffsByCompanyQuery({
      requestPolicy: "network-only",
    });

  const [selectedItem, setSelectedItem] = useState<number | null>(null);
  const [itemsAdded, setItemsAdded] = useState<IWriteOffItem[]>([]);
  const [unitType, setUnitType] = useState("");
  const [selectedItemsIds, setSelectedItemsIds] = useState<
    { itemId: number }[]
  >([]);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
    setValue,
  } = useForm({
    defaultValues: {
      itemId: 0,
      quantity: 0,
      reason: "",
      locationId: 0,
      batch: "",
      unit: "",
    },
  });

  const handleItemChange = async (value: any) => {
    if (value) {
      setSelectedItem(value.value);
      setValue("itemId", value.value);
      setFetchVars((old) => {
        return { storeId: old.storeId, itemId: Number(value.value) };
      });
    } else setSelectedItem(null);
    reset();
  };

  const handleStoreChange = (value: any) => {
    setValue("locationId", value.value);
    setFetchVars((old) => {
      return { storeId: value.value, itemId: old.itemId };
    });
  };

  const handleBatchChange = (value: any) => {
    setValue("batch", value.value);
  };

  const handleUnitChange: any = (value: any) => {
    if (value) {
      setUnitType(value.value);
      setValue("unit", value.value);
    } else {
      setUnitType("");
    }
  };

  const onSubmit = (values: IWriteOffItem) => {
    console.log("onSubmit: ", values);
    setItemsAdded((oldItems) => {
      return [...oldItems, { ...values, itemId: Number(selectedItem) }];
    });
    setSelectedItemsIds((oldSelectedItems) => {
      return [...oldSelectedItems, { itemId: values.itemId }];
    });
    setSelectedItem(null);
    return reset();
  };

  async function onDBSubmit() {
    const args = itemsAdded.map((item: IWriteOffItem) => ({
      itemId: Number(item.itemId),
      quantity: Number(item.quantity),
      reason: item.reason,
      unit: unitType,
      locationId: Number(item.locationId),
      batch: item.batch,
    }));
    const { data } = await writeOff({ args });
    if (data?.writeOffItems.error) {
      toast({
        title: data.writeOffItems.error.message,
        status: "error",
        isClosable: true,
      });
    } else {
      reset();
      setSelectedItem(null);
      setItemsAdded([]);
      toast({
        title: `Items written off successfully!`,
        variant: "left-accent",
        status: "success",
        isClosable: true,
      });
      return reGetWriteOffs();
    }
  }

  return (
    <Flex direction="column" position="relative" mb="90px" mt="100px">
      <Flex
        w="100%"
        mx="auto"
        justifyContent="space-between"
        mb="60px"
        pt={{ md: "0px" }}
        direction="column"
      >
        <Flex
          w="100%"
          as={Grid}
          templateColumns="repeat(12, 1fr)"
          gap={6}
          mb="60px"
        >
          <Flex
            direction="column"
            background="transparent"
            borderRadius="15px"
            as={GridItem}
            colSpan={5}
            width={"100%"}
            p="40px"
            bg={useColorModeValue("white", "navy.800")}
            boxShadow={useColorModeValue(
              "0px 5px 14px rgba(0, 0, 0, 0.05)",
              "unset"
            )}
          >
            <Text
              fontSize="xl"
              color={nameColor}
              fontWeight="bold"
              textAlign="center"
              mb="12px"
            >
              Select Item to Write Off
            </Text>
            <FormControl mb={4}>
              <FormLabel fontSize="xs">Item</FormLabel>
              <AllItemsInput
                onChange={handleItemChange}
                selectedItemsIds={selectedItemsIds}
              />
            </FormControl>
            {selectedItem && (
              <>
                <Text
                  fontSize="xl"
                  color={nameColor}
                  fontWeight="bold"
                  textAlign="center"
                  mb="12px"
                >
                  Write Off Details
                </Text>
                <form onSubmit={handleSubmit(onSubmit)}>
                  <Grid templateColumns="repeat(2, 1fr)" gap={4} mb={4}>
                    <Input
                      type="hidden"
                      value={selectedItem}
                      {...register("itemId", { value: selectedItem })}
                    />
                    <FormControl isInvalid={Boolean(errors.locationId)} mb={4}>
                      <FormLabel htmlFor="locationId" fontSize="xs">
                        Store
                      </FormLabel>
                      <StoreInput onChange={handleStoreChange} />
                      <FormErrorMessage fontSize="xs">
                        {errors.locationId?.message}
                      </FormErrorMessage>
                    </FormControl>
                    <FormControl isInvalid={Boolean(errors.batch)} mb={4}>
                      <FormLabel htmlFor="batch" fontSize="xs">
                        Batch No. / Lot Id
                      </FormLabel>
                      <BatchInput
                        onChange={handleBatchChange}
                        item={
                          items?.getAllItems.find(
                            (item) => item.id === selectedItem
                          ) as Item
                        }
                        storeId={fetchVars.storeId}
                      />
                      <FormErrorMessage fontSize="xs">
                        {errors.batch?.message}
                      </FormErrorMessage>
                    </FormControl>
                    <FormControl isInvalid={Boolean(errors.quantity)} mb={4}>
                      <FormLabel htmlFor="quantity" fontSize="xs">
                        Quantity
                      </FormLabel>
                      <Input
                        id="quantity"
                        type="number"
                        placeholder="Quantity"
                        {...register("quantity", {
                          required: "This is required",
                          min: {
                            value: 1,
                            message: "Quantity must be greater than 0",
                          },
                        })}
                      />
                      <FormErrorMessage fontSize="xs">
                        {errors.quantity?.message}
                      </FormErrorMessage>
                    </FormControl>{" "}
                    <FormControl isInvalid={Boolean(errors.unit)} mb={4}>
                      <FormLabel htmlFor="unit" fontSize="xs">
                        Unit
                      </FormLabel>
                      <UnitInput
                        onChange={handleUnitChange}
                        selectedItem={
                          items?.getAllItems.find(
                            (item) => item.id === selectedItem
                          ) as Item
                        }
                      />
                      <FormErrorMessage fontSize="xs">
                        {errors.unit?.message}
                      </FormErrorMessage>
                    </FormControl>
                    <FormControl
                      isInvalid={Boolean(errors.reason)}
                      mb={4}
                      as={GridItem}
                      colSpan={2}
                    >
                      <FormLabel htmlFor="reason" fontSize="xs">
                        Reason
                      </FormLabel>
                      <Input
                        id="reason"
                        placeholder="Reason"
                        {...register("reason", {
                          required: "This is required",
                        })}
                      />
                      <FormErrorMessage fontSize="xs">
                        {errors.reason?.message}
                      </FormErrorMessage>
                    </FormControl>
                  </Grid>
                  <Button
                    type="submit"
                    colorScheme="teal"
                    isLoading={isSubmitting}
                  >
                    Add Item
                  </Button>
                </form>
              </>
            )}
          </Flex>
          <Flex
            direction="column"
            background="transparent"
            borderRadius="15px"
            as={GridItem}
            colSpan={7}
            width={"100%"}
            p="40px"
            bg={useColorModeValue("white", "navy.800")}
            boxShadow={useColorModeValue(
              "0px 5px 14px rgba(0, 0, 0, 0.05)",
              "unset"
            )}
          >
            <Text
              fontSize="xl"
              color={nameColor}
              fontWeight="bold"
              textAlign="center"
              mb="12px"
            >
              Items to Write Off
            </Text>
            {itemsAdded.length > 0 ? (
              <Table variant="simple">
                <Thead>
                  <Tr>
                    <Th>Item</Th>
                    <Th>Quantity</Th>
                    <Th>Reason</Th>
                    <Th>Store</Th>
                    <Th>Batch Number</Th>
                  </Tr>
                </Thead>
                <Tbody>
                  {itemsAdded.map((item, index) => (
                    <Tr key={index}>
                      <Td>
                        {
                          items?.getAllItems.find((i) => i.id === item.itemId)
                            ?.name
                        }
                      </Td>
                      <Td>{item.quantity}</Td>
                      <Td>{item.reason}</Td>
                      <Td>
                        {
                          stores?.getStores.find(
                            (s) => s.id === item.locationId
                          )?.name
                        }
                      </Td>
                      <Td>{item.batch}</Td>
                    </Tr>
                  ))}
                </Tbody>
                <Tfoot>
                  <Tr>
                    <Th colSpan={5} textAlign="right">
                      <Button
                        colorScheme="teal"
                        onClick={onDBSubmit}
                        isLoading={isSubmitting}
                      >
                        Write Off All Items
                      </Button>
                    </Th>
                  </Tr>
                </Tfoot>
              </Table>
            ) : (
              <Flex justify="center" align="center" height="100%">
                <Text fontSize="lg" color={nameColor}>
                  No items added yet.
                </Text>
              </Flex>
            )}
          </Flex>
        </Flex>
        <Flex
          direction="column"
          background="transparent"
          borderRadius="15px"
          p="40px"
          bg={useColorModeValue("white", "navy.800")}
          boxShadow={useColorModeValue(
            "0px 5px 14px rgba(0, 0, 0, 0.05)",
            "unset"
          )}
        >
          <Text
            fontSize="xl"
            color={nameColor}
            fontWeight="bold"
            textAlign="center"
            mb="12px"
          >
            Recently Written Off Items
          </Text>
          <Table variant="simple">
            <Thead>
              <Tr>
                <Th>Item</Th>
                <Th>Quantity</Th>
                <Th>Reason</Th>
                <Th>Date</Th>
              </Tr>
            </Thead>
            <Tbody>
              {fetchingWriteOffs ? (
                <Tr>
                  <Td colSpan={4}>
                    <Spinner size="sm" />
                  </Td>
                </Tr>
              ) : (
                writeOffs?.getWriteOffsByCompany.map((writeOff) => {
                  return (
                    <Tr key={writeOff.id}>
                      <Td>{writeOff.item.name}</Td>
                      <Td>
                        {writeOff.quantity > 0
                          ? writeOff.quantity + " " + writeOff.item.unit
                          : ""}
                        {writeOff.pieceQuantity > 0
                          ? writeOff.pieceQuantity +
                            " " +
                            writeOff.item.pieceUnit
                          : ""}
                        {writeOff.subPieceQuantity > 0
                          ? writeOff.subPieceQuantity +
                            " " +
                            writeOff.item.subPieceUnit
                          : ""}
                      </Td>
                      <Td>{writeOff.details}</Td>
                      <Td>{toDateTime(Number(writeOff.createdAt))}</Td>
                    </Tr>
                  );
                })
              )}
            </Tbody>
          </Table>
        </Flex>
      </Flex>
    </Flex>
  );
};

export default WriteOff;
