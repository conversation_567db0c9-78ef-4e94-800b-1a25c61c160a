import { Field, ObjectType } from "type-graphql";
import { Column, <PERSON>tity, Join<PERSON><PERSON>umn, ManyToOne } from "typeorm";
import { AuditBaseEntity } from "./AuditEntity";
import { Employee } from "./Employee";

@ObjectType()
@Entity()
export class Approval extends AuditBaseEntity {
  @Field(() => String)
  @Column({
    type: "enum",
    enum: ["INVENTORY"], //enum in postgres is case sensitive so mind the caps
  })
  feature: string;

  @Field(() => String)
  @Column({
    type: "enum",
    enum: ["TRANSFER"], //enum in postgres is case sensitive so mind the caps
  })
  type: string;

  @Field()
  @Column({ default: false })
  status: boolean;

  @Field({ nullable: true })
  @Column({ type: "uuid", nullable: true })
  requesterId: string; // who requests

  @Field(() => Employee, { nullable: true })
  @ManyToOne(() => Employee, (employee) => employee.requestedApprovals, {
    nullable: true,
  })
  @JoinColumn([
    { name: "requesterId", referencedColumnName: "id" },
    { name: "companyId", referencedColumnName: "companyId" },
  ])
  requester: Employee;

  @Field({ nullable: true })
  @Column({ type: "uuid", nullable: true })
  approverId: string; // who approves

  @Field(() => Employee, { nullable: true })
  @ManyToOne(() => Employee, (employee) => employee.approvedApprovals, {
    nullable: true,
  })
  @JoinColumn([
    { name: "approverId", referencedColumnName: "id" },
    { name: "companyId", referencedColumnName: "companyId" },
  ])
  approver: Employee;

  @Field(() => Date, { nullable: true })
  @Column({ type: "timestamp", nullable: true })
  approvalDate: Date;

  @Field(() => String)
  @Column({ type: "uuid" })
  requestId: string;
}
