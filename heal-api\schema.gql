# -----------------------------------------------
# !!! THIS FILE WAS GENERATED BY TYPE-GRAPHQL !!!
# !!!   DO NOT MODIFY THIS FILE BY YOURSELF   !!!
# -----------------------------------------------

type Approval {
  approvalDate: DateTime
  approver: Employee
  approverId: String
  companyId: String!
  createdAt: String!
  deleted: Boolean!
  feature: String!
  id: String!
  requestId: String!
  requester: Employee
  requesterId: String
  status: Boolean!
  type: String!
  updatedAt: String!
}

input AssignPasswordArgs {
  companyId: String!
  newPassword: String!
  userId: String!
}

type BatchStock {
  batch: String!
  companyId: String!
  createdAt: String!
  deleted: Boolean!
  expireDate: String!
  id: String!
  item: Item!
  itemId: String!
  stock: Float!
  storeItemStocks: [StoreItemStock!]
  updatedAt: String!
}

type Bill {
  amount: Float!
  cleared: Boolean!
  companyId: String!
  createdAt: String!
  deleted: Boolean!
  id: String!
  inventoryId: String!
  inventoryTransfer: Inventory!
  paymentType: String!
  updatedAt: String!
}

"""Billing cycle for payment"""
enum BillingCycle {
  ANNUALLY
  MONTHLY
  QUARTERLY
}

type BooleanResponse {
  error: FieldError
  status: Boolean!
}

type BooleanResponseId {
  error: FieldError
  id: String
  name: String
  status: Boolean!
}

type BooleanResponseWithType {
  data: Type
  error: FieldError
  status: Boolean!
}

input BulkItemInput {
  items: [ItemInput!]!
}

type Category {
  companyId: String!
  createdAt: String!
  deleted: Boolean!
  id: String!
  name: String!
  parentCategoryId: String
  type: Type!
  typeId: String!
  updatedAt: String!
  user: [User!]
}

input CategoryArgs {
  name: String!
  type: Float!
}

type CategoryResponse {
  category: Category
  error: FieldError
}

input CategoryTypeArgs {
  name: String!
  typeName: String!
}

type Company {
  branches: [String!]
  companyId: String!
  createdAt: String!
  deleted: Boolean!
  departments: [Department!]!
  email: String
  employees: [Employee!]!
  features: [Feature!]
  id: String!
  isBranch: Boolean!
  isParent: Boolean!
  location: String!
  logo: String
  name: String!
  parentId: String
  payments: [Payment!]
  phone: String
  poBox: String
  registrationNumber: String!
  syncHistory: [SyncHistory!]
  syncUrl: String
  tinNumber: String!
  type: String!
  updatedAt: String!
  users: [User!]
  website: String
}

input CreatePaymentInput {
  amount: Float!
  autoRenew: Boolean
  billingCycle: String!
  endDate: DateTime!
  features: String
  maxUsers: Float
  packageName: String!
  paymentReference: String
  startDate: DateTime!
  status: String!
}

"""
The javascript `Date` as string. Type represents date and time as the ISO Date string.
"""
scalar DateTime

type Department {
  company: Company!
  companyId: String!
  createdAt: String!
  deleted: Boolean!
  description: String!
  employees: [Employee!]
  headOfDepartment: Employee
  id: String!
  name: String!
  parentId: String
  status: String
  type: String
  updatedAt: String!
}

input DepartmentInputArgs {
  description: String
  headOfDepartmentId: String
  name: String!
  parentId: String
  status: String
  type: String
}

input DispatchInput {
  batch: String
  itemId: String!
  locationId: String!
  quantity: Float!
  remarks: String
  unit: String!
}

input EditUserArgs {
  email: String!
  firstname: String!
  image: String!
  lastname: String!
  middlename: String!
  phone: String!
}

input EmailPasswordArgs {
  email: String!
  password: String!
}

type Employee {
  approvedApprovals: [Approval!]
  approved_stock: [Inventory!]
  authorizedExpenses: [Expense!]
  company: Company!
  companyId: String!
  createdAt: String!
  deleted: Boolean!
  department: Department
  departmentId: String
  designation: String!
  headingDepartment: Department
  headingDepartmentId: String
  id: String!
  image: String!
  licenceNumber: String!
  received_stock: [Inventory!]
  requestedApprovals: [Approval!]
  requestedExpenses: [Expense!]
  role: Role!
  roleId: String
  served_stock: [Inventory!]
  status: String!
  store: Store
  storeId: String
  updatedAt: String!
  user: User!
  userId: String!
}

type Expense {
  amount: Float!
  assetId: String
  assetType: String!
  authorizer: Employee
  authorizerId: String!
  companyId: String!
  createdAt: String!
  deleted: Boolean!
  details: String!
  expenseDate: String!
  id: String!
  requester: Employee
  requesterId: String!
  status: String!
  title: String!
  type: String!
  updatedAt: String!
}

input ExpenseFilterInput {
  endDate: String
  startDate: String
}

input ExpenseInput {
  amount: Float!
  assetId: String
  assetType: String
  details: String!
  expenseDate: String!
  title: String!
  type: String!
}

type Feature {
  companies: [Company!]
  companyId: String!
  createdAt: String!
  deleted: Boolean!
  id: String!
  name: String!
  updatedAt: String!
}

type FieldError {
  message: String!
  target: String!
}

type Import {
  batch: String
  companyId: String!
  createdAt: String!
  deleted: Boolean!
  expireDate: String
  id: String!
  importDate: String!
  importPrice: Float
  item: Item!
  itemId: String!
  quantity: Float!
  receipt: String
  sellingPrice: Float!
  supplier: String!
  unit: String!
  updatedAt: String!
}

input ImportInput {
  barcode: String
  batch: String
  expireDate: String
  importDate: String!
  importPrice: Float!
  itemId: String
  quantity: Float!
  receipt: String
  sellingPrice: Float
  supplier: String!
  unit: String!
}

type Inventory {
  approver: Employee
  approverId: String
  bill: Bill
  companyId: String!
  consumer: Employee
  consumerId: String
  createdAt: String!
  customerTag: String
  deleted: Boolean!
  destinationStore: Store
  destinationStoreId: String
  details: String
  dispatched: Boolean!
  granted: Boolean!
  id: String!
  items: [Item!]!
  keeper: Employee
  keeperId: String
  received: Boolean!
  returnDate: String
  sourceStore: Store
  sourceStoreId: String
  startDate: String
  transferDate: String!
  transfers: [Transfer!]!
  type: String!
  updatedAt: String!
}

type Item {
  barcode: String
  batchStocks: [BatchStock!]
  companyId: String!
  createdAt: String!
  deleted: Boolean!
  description: String!
  id: String!
  image: String!
  imports: [Import!]
  internal: Boolean!
  inventoryTransfers: [Inventory!]
  name: String!
  reference: String!
  reorder: Float!
  sellingPrice: Float!
  stock: Float!
  storeItemStocks: [StoreItemStock!]
  transfers: [Transfer!]
  type: String!
  unit: String!
  units: [Unit!]!
  updatedAt: String!
}

input ItemInput {
  barcode: String
  description: String
  image: String
  internal: Boolean
  name: String!
  reference: String
  reorder: Float!
  sellingPrice: Float
  type: String!
  unit: String!
}

type LogEntry {
  action: String
  companyId: String
  errorCode: String
  level: String!
  message: String!
  severity: String
  stackTrace: String
  timestamp: String!
  userId: String
}

type Message {
  attended: Boolean!
  createdAt: String!
  id: String!
  message: String!
  senderEmail: String!
  senderName: String!
  senderPhone: String
  subject: String!
  updatedAt: String!
}

input MessageInput {
  message: String!
  senderEmail: String!
  senderName: String!
  senderPhone: String
  subject: String!
}

type MessageResponse {
  errors: [FieldError!]
  message: Message
  status: Boolean!
}

type Mutation {
  addCategory(args: CategoryArgs!): BooleanResponse!
  addCategoryWithTypeName(args: CategoryTypeArgs!): CategoryResponse!
  addCompanyWithAddress(params: RegisterCompanyAddressedArgs!): BooleanResponse!
  addDepartment(params: DepartmentInputArgs!): BooleanResponse!
  addExpense(args: ExpenseInput!): BooleanResponse!
  addFeature(companyId: String!, name: String!): BooleanResponse!
  addItem(args: ItemInput!): BooleanResponse!
  addItemsFromExcel(args: BulkItemInput!): BooleanResponse!
  addPermission(name: String!, roleId: String, userId: String): PermissionResponse!
  addRole(name: String!): BooleanResponse!
  addService(args: ServiceInput!): BooleanResponse!
  addStore(args: StoreInput!): BooleanResponse!
  addType(args: TypeArgs!): BooleanResponseWithType!
  addUnit(args: UnitInput!): BooleanResponse!
  assignPassword(params: AssignPasswordArgs!): BooleanResponse!
  assignStoreKeeper(storeId: String!, userId: String!): BooleanResponse!
  authorizeExpense(id: String!): BooleanResponse!
  cancelPayment(id: String!, reason: String): BooleanResponse!
  changeEmployeeRole(companyRole: String!, departmentId: String!, designation: String!, employeeId: String!): BooleanResponse!
  changeEmployeeStatus(employeeId: String!, status: String!): BooleanResponse!
  changeInventoryApprovalStatus(inventoryId: String!): BooleanResponse!
  changeInventoryDispatchedStatus(inventoryId: String!): BooleanResponse!
  changeInventoryReceivedStatus(inventoryId: String!): BooleanResponse!
  changeInventorySoldStatus(inventoryId: String!): BooleanResponse!
  changePassword(currentPassword: String!, newPassword: String!): BooleanResponse!
  changePaymentStatus(companyId: String!, id: String!, status: String!): BooleanResponse!
  clearBill(saleId: String!): BooleanResponse!
  clearServedBill(inventoryId: String!): BooleanResponse!
  createPayment(input: CreatePaymentInput!): Payment!
  deleteBillItem(inventoryId: String!, transferId: String!): BooleanResponse!
  deleteCategory(id: String!): BooleanResponse!
  deleteExpense(id: String!): BooleanResponse!
  deleteFeature(id: String!): BooleanResponse!
  deleteItem(id: String!): BooleanResponse!
  deletePayment(id: String!): BooleanResponse!
  deletePermission(id: String!): BooleanResponse!
  deleteRole(id: String!): BooleanResponse!
  deleteType(id: String!): BooleanResponse!
  deleteUnit(id: String!): BooleanResponse!
  dispatchItems(args: [DispatchInput!]!): BooleanResponse!
  editBillItem(inventoryId: String!, newQuantity: Float!, transferId: String!): TransferResponse!
  editCategory(args: CategoryArgs!, id: String!): BooleanResponse!
  editCategoryByName(args: CategoryArgs!, name: String!): BooleanResponse!
  editDepartment(id: String!, params: DepartmentInputArgs!): BooleanResponse!
  editExpense(args: ExpenseInput!, id: String!): BooleanResponse!
  editFeature(id: String!, name: String!): BooleanResponse!
  editItem(args: ItemInput!, id: String!): BooleanResponse!
  editPermission(id: String!, name: String!): BooleanResponse!
  editRole(args: RoleArgs!, id: String!): BooleanResponse!
  editService(args: ServiceInput!, id: String!): BooleanResponse!
  editStore(args: StoreEditInput!): BooleanResponse!
  editType(args: TypeEditArgs!, id: String!): BooleanResponse!
  editUser(id: String!, params: EditUserArgs!): BooleanResponse!
  forgotPassword(email: String!): BooleanResponse!
  generateBarcode(itemId: String!): Item
  generateUnitBarcode(unitId: String!): Unit
  getPowerSyncToken: String!
  importItem(args: ImportInput!): BooleanResponse!
  instantTransfer(args: [TransferInput!]!, destinationStore: String!, sourceStore: String!): BooleanResponse!
  login(params: EmailPasswordArgs!): UserResponse!
  logout: Boolean!
  manageUserPermissions(id: String!, permissions: [String!]!): BooleanResponse!
  quickSale(args: [SaleInput!]!): BooleanResponse!
  receiveMessage(input: MessageInput!): MessageResponse!
  register(params: RegisterUserArgs!): BooleanResponse!
  registerCompany(params: RegisterCompanyArgs!): BooleanResponseId!
  registerEmployee(params: RegisterEmployeeArgs!): BooleanResponse!
  removePermission(name: String!, roleId: String, userId: String): BooleanResponse!
  resetPassword(newPassword: String!, token: String!): UserResponse!
  servePayLater(args: [SaleInput!]!, customerTag: String, servedTo: String): BooleanResponse!
  servePendingOrder(transferId: String!): BooleanResponse!
  setHeadOfDepartment(departmentId: String!, employeeId: String!): BooleanResponse!
  transferItems(args: [DispatchInput!]!): BooleanResponse!
  updateBill(args: [SaleInput!]!, inventoryId: String!): BooleanResponse!
  updateMessageStatus(attended: Boolean!, messageId: String!): MessageResponse!
  updatePayment(id: String!, input: UpdatePaymentInput!): Payment
  updateUnit(args: UnitInput!, id: String!): BooleanResponse!
  writeOffItems(args: [WriteOffInput!]!): BooleanResponse!
}

type Payment {
  amount: Float!
  autoRenew: Boolean!
  billingCycle: BillingCycle!
  cancellationReason: String
  company: Company!
  companyId: String!
  createdAt: String!
  deleted: Boolean!
  endDate: String!
  features: String
  id: String!
  lastPaymentDate: String
  maxUsers: Float
  packageName: String!
  paymentReference: String
  startDate: String!
  status: PaymentStatus!
  updatedAt: String!
}

"""Status of a payment"""
enum PaymentStatus {
  ACTIVE
  CANCELED
  EXPIRED
  PENDING
  TRIAL
}

type Permission {
  companyId: String!
  createdAt: String!
  deleted: Boolean!
  id: String!
  name: String!
  roles: [Role!]
  updatedAt: String!
  users: [User!]
}

type PermissionResponse {
  error: FieldError
  permission: Permission
  status: Boolean!
}

type Query {
  expenses(filter: ExpenseFilterInput): [Expense!]!
  getActivePayment(companyId: String!): Payment
  getAllCategories: [Category!]!
  getAllItems: [Item!]!
  getAllMessages(endDate: DateTime!, startDate: DateTime!): [Message!]!
  getAllServices: [Item!]!
  getBatchStockForStore(itemId: String, storeId: String): [BatchStock!]!
  getCategories(type: String!): [Category!]!
  getCompanies: [Company!]!
  getCompany(id: String!): Company
  getCompanyErrors(companyId: String!, days: Int = 7, endDate: DateTime, severity: String, startDate: DateTime): [LogEntry!]!
  getDepartments: [Department!]!
  getDispatches: [Inventory!]!
  getEmployees: [User!]!
  getExpense(id: String!): Expense
  getExpenses: [Expense!]!
  getFeatures: [Feature!]!
  getInternalItems: [Item!]!
  getInventoryTransfer(id: String!): Inventory
  getInventoryTransfers(type: String): [Inventory!]!
  getItem(id: String!): Item!
  getItemBatchImports(itemId: String!): [Import!]!
  getItemBatchStocks(itemId: String!): [BatchStock!]!
  getItemByBarcode(barcode: String!): Item
  getItemStoreStocks(itemId: String!): [StoreItemStock!]!
  getItemTransfers(itemId: String!, type: String): [Transfer!]!
  getItemUnits(itemId: String!): [Unit!]!
  getItems: [Item!]!
  getLatestPayment: Payment
  getLogs(companyId: String, endDate: DateTime, errorCode: String, level: String, startDate: DateTime, userId: String): [LogEntry!]!
  getMerchandiseItems: [Item!]!
  getMessages(attended: Boolean): [Message!]!
  getOpenTabs: [Inventory!]!
  getPayment(id: String!): Payment
  getPermissions: [Permission!]!
  getRole(name: String!): Role
  getRoles(sys: Boolean): [Role!]!
  getSales(date: DateTime): [Inventory!]!
  getSalesPOS: [Inventory!]!
  getStoreItems(storeId: String!): [Item!]!
  getStores: [Store!]!
  getTodaysErrors: [LogEntry!]!
  getTodaysImports: [Import!]!
  getTransfers: [Inventory!]!
  getType(id: String!): Type
  getTypes: [Type!]!
  getUndetailedEmployees(companyId: String!): [UndetailedUser!]!
  getUnit(id: String!): Unit
  getUnitByBarcode(barcode: String!): Unit
  getUser(id: String!): User
  getUsers(roles: [String!]): [User!]!
  getWriteOffsByCompany: [Transfer!]!
  me: User
  payments: [Payment!]!
}

input RegisterCompanyAddressedArgs {
  city: String!
  district: String!
  name: String!
  registrationNumber: String!
  street: String!
  tinNumber: String!
  type: String!
  ward: String!
}

input RegisterCompanyArgs {
  location: String
  name: String!
  registrationNumber: String!
  tinNumber: String!
  type: String!
}

input RegisterEmployeeArgs {
  companyRole: String
  department: String
  designation: String!
  email: String!
  firstname: String!
  lastname: String!
  licenseNumber: String!
  middlename: String!
  password: String!
  phone: String!
  store: String
}

input RegisterUserArgs {
  companyId: String!
  email: String!
  firstname: String!
  lastname: String!
  middlename: String!
  password: String!
  phone: String!
}

type Role {
  companyId: String!
  createdAt: String!
  deleted: Boolean!
  employees: [Employee!]
  id: String!
  name: String!
  permissions: [Permission!]
  sys: Boolean!
  updatedAt: String!
  users: [User!]
}

input RoleArgs {
  name: String!
  permissions: [String!]
}

input SaleInput {
  batch: String
  hold: Boolean = false
  itemId: String!
  quantity: Float!
  remarks: String
  unit: String!
}

input ServiceInput {
  description: String
  name: String!
  reference: String
  sellingPrice: Float!
}

type Store {
  address: String!
  companyId: String!
  createdAt: String!
  deleted: Boolean!
  id: String!
  name: String!
  primary: Boolean!
  stockIn: [Inventory!]!
  stockOut: [Inventory!]!
  storeItemStocks: [StoreItemStock!]
  storeKeepers: [Employee!]
  updatedAt: String!
}

input StoreEditInput {
  address: String
  companyId: String
  id: String!
  name: String
  primary: Boolean
}

input StoreInput {
  address: String!
  name: String!
  primary: Boolean = false
}

type StoreItemStock {
  batchId: String
  batchStock: BatchStock
  companyId: String!
  createdAt: String!
  deleted: Boolean!
  id: String!
  item: Item!
  itemId: String!
  stock: Float!
  store: Store!
  storeId: String!
  updatedAt: String!
}

type SyncError {
  code: String
  details: String
  message: String!
  stack: String
}

type SyncHistory {
  company: Company!
  companyId: String!
  createdAt: DateTime!
  direction: String!
  entityName: String!
  error: SyncError
  id: String!
  lastSyncTimestamp: DateTime!
  recordsProcessed: Float!
  status: String!
  updatedAt: DateTime!
}

type Transfer {
  batch: String
  companyId: String!
  createdAt: String!
  deleted: Boolean!
  details: String
  dispatched: Boolean!
  granted: Boolean!
  id: String!
  inventoryId: String!
  inventoryTransfer: Inventory!
  item: Item!
  itemId: String!
  price: Float!
  quantity: Float!
  received: Boolean!
  updatedAt: String!
}

input TransferInput {
  batch: String
  itemId: String!
  quantity: Float!
  unit: String!
}

type TransferResponse {
  error: FieldError
  status: Boolean!
  transfer: Transfer
}

type Type {
  category: [Category!]
  companyId: String!
  createdAt: String!
  deleted: Boolean!
  description: String!
  id: String!
  name: String!
  updatedAt: String!
}

input TypeArgs {
  description: String!
  name: String!
}

input TypeEditArgs {
  categories: [String!]!
  description: String!
  name: String!
}

type UndetailedUser {
  email: String!
  firstname: String!
  image: String
  lastname: String!
}

type Unit {
  barcode: String
  companyId: String!
  createdAt: String!
  deleted: Boolean!
  id: String!
  item: Item!
  itemId: String!
  name: String!
  price: Float!
  quantity: Float!
  updatedAt: String!
}

input UnitInput {
  barcode: String
  itemId: String!
  name: String!
  price: Float!
  quantity: Float!
}

input UpdatePaymentInput {
  amount: Float
  autoRenew: Boolean
  billingCycle: String
  cancellationReason: String
  endDate: DateTime
  features: String
  lastPaymentDate: DateTime
  maxUsers: Float
  packageName: String
  paymentReference: String
  startDate: DateTime
  status: String
}

type User {
  address: String
  company: Company!
  companyId: String!
  createdAt: String!
  dateOfBirth: String
  deleted: Boolean!
  email: String
  employee: Employee
  firstname: String!
  gender: String!
  id: String!
  image: String
  lastname: String!
  middlename: String!
  permissions: [Permission!]
  phone: String!
  role: Role!
  roleId: String!
  status: [Category!]
  updatedAt: String!
}

type UserResponse {
  error: FieldError
  token: String
  user: User
}

input WriteOffInput {
  batch: String
  itemId: String!
  locationId: String!
  quantity: Float!
  reason: String
  unit: String!
}
