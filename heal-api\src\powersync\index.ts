import express, { Router } from "express";
import { authRouter } from "./auth";
import { createDataRouter } from "./data";
import type { Connection } from "typeorm";

// Factory to create the PowerSync API router with required dependencies
export function createPowerSyncApiRouter(conn: Connection): Router {
  const router = express.Router();

  // Auth/JWKS endpoints under /powersync/auth
  router.use("/powersync/auth", authRouter);

  // Data/upload endpoints
  router.use("/", createDataRouter(conn));

  return router;
}
