import {
  Box,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Button,
  useColorModeValue,
} from "@chakra-ui/react";
import { BatchStock, Import, Item, Transfer } from "../../../generated/graphql";
import { formatToMoney, toDateTime } from "../../../utils/Helpers";

const RecentImportsTable = ({
  item,
  imports,
  onView,
  batches,
  purchases,
}: {
  item: Item;
  imports: Transfer[];
  onView: (batch: BatchStock) => void;
  batches: BatchStock[];
  purchases?: Import[];
}) => {
  const borderColor = useColorModeValue("gray.200", "gray.600");
  const textColor = useColorModeValue("gray.700", "white");

  const handleViewBatch = (batchNumber?: string) => {
    const foundBatch = batches.find((batch) => batch.batch === batchNumber);
    if (foundBatch) {
      onView(foundBatch);
    }
  };

  return (
    <Box>
      <Table variant="simple" color={textColor}>
        <Thead>
          <Tr>
            <Th borderColor={borderColor}>Date</Th>
            <Th borderColor={borderColor}>Quantity</Th>
            <Th borderColor={borderColor}>Batch Number</Th>
            <Th borderColor={borderColor}>Importing Price Per 1 Item</Th>
          </Tr>
        </Thead>
        <Tbody>
          {imports.map((imp) => {
            let pp: number;
            if (purchases)
              pp = purchases.find(
                (purch) =>
                  purch.batch === imp.batch && purch.itemId === imp.itemId
              )?.importPrice!;
            else pp = 0;
            return (
              <Tr key={imp.id}>
                <Td borderColor={borderColor}>
                  {toDateTime(Number(imp.inventoryTransfer.transferDate))}
                </Td>
                <Td borderColor={borderColor}>
                  {imp.quantity > 0 ? imp.quantity + " " + item?.unit : " "}
                  {imp.pieceQuantity > 0
                    ? imp.pieceQuantity + " " + item?.pieceUnit
                    : ""}
                  {imp.subPieceQuantity > 0
                    ? imp.subPieceQuantity + " " + item?.subPieceUnit
                    : ""}
                </Td>
                <Td borderColor={borderColor}>
                  <Button onClick={() => handleViewBatch(imp.batch)}>
                    {imp.batch}
                  </Button>
                </Td>
                <Td borderColor={borderColor}>{formatToMoney(pp)}</Td>
              </Tr>
            );
          })}
        </Tbody>
      </Table>
    </Box>
  );
};

export default RecentImportsTable;
