import { Field, ObjectType } from "type-graphql";
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  Unique,
} from "typeorm";

@ObjectType()
@Entity()
@Unique(["id", "companyId"]) // Make the combination unique instead of just id
export class AuditBaseEntity extends BaseEntity {
  @Field(() => String)
  @PrimaryGeneratedColumn("uuid") // ← generates UUID automatically
  id!: string;

  @Field(() => String)
  @Column({ type: "uuid" })
  companyId!: string;

  @Field(() => String)
  @CreateDateColumn({ type: "timestamp" })
  createdAt = new Date();

  @Field(() => String)
  @UpdateDateColumn({ type: "timestamp" })
  updatedAt = new Date();

  @Field(() => Boolean)
  @Column({ type: "boolean", default: false })
  deleted: boolean;
}
