// @ts-nocheck
import fs from "fs";
import path from "path";
import crypto from "crypto";
import os from "os";

const TOKEN_ENCRYPTION_KEY = "TalisiaSuperSecretKey2024!";
// Electron's app.getPath('userData') equivalent for Node.js:
const USER_DATA_PATH = path.join(os.homedir(), ".config", "TalisiaDesktop");
const TOKEN_FILE_PATH = path.join(USER_DATA_PATH, "auth_token.enc");

export class PowerSyncServiceClient {
  static decryptToken(data: string): string {
    const [ivStr, encrypted] = data.split(":");
    const iv = Buffer.from(ivStr, "base64");
    const decipher = crypto.createDecipheriv(
      "aes-256-cbc",
      Buffer.from(TOKEN_ENCRYPTION_KEY, "utf8"),
      iv
    );
    let decrypted = decipher.update(encrypted, "base64", "utf8");
    decrypted += decipher.final("utf8");
    return decrypted;
  }

  /**
   * Reads and decrypts the auth token from the user's device.
   * @returns {string|null} The decrypted token, or null if not found/error.
   */
  static getDecryptedToken(): string | null {
    try {
      if (!fs.existsSync(TOKEN_FILE_PATH)) {
        console.error("PowerSync token file not found:", TOKEN_FILE_PATH);
        return null;
      }
      const encrypted = fs.readFileSync(TOKEN_FILE_PATH, { encoding: "utf8" });
      return this.decryptToken(encrypted);
    } catch (err) {
      console.error("Failed to read/decrypt PowerSync token:", err);
      return null;
    }
  }

  /**
   * Example: Use the decrypted token to authenticate with PowerSync API.
   * You can extend this to initialize the PowerSync client, set headers, etc.
   */
  static async getAuthHeaders(): Promise<{ Authorization: string } | null> {
    const token = this.getDecryptedToken();
    if (!token) return null;
    return { Authorization: `Bearer ${token}` };
  }
}

// Usage example (in your sync logic):
// const headers = await PowerSyncServiceClient.getAuthHeaders();
// if (headers) { /* use headers.Authorization for PowerSync API requests */ }
