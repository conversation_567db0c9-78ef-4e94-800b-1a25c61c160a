import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import {
  Flex,
  Button,
  FormControl,
  FormLabel,
  Input,
  Text,
  useColorModeValue,
  FormErrorMessage,
  useToast,
  Grid,
  GridItem,
  Select,
  Box,
  Icon,
} from "@chakra-ui/react";
import { useHistory, useLocation } from "react-router-dom";
import { FaBuilding } from "react-icons/fa";
import {
  Department,
  useEditDepartmentMutation,
} from "../../../generated/graphql";
import { HSeparator } from "../../../components/Separator/Separator";

interface IDepartmentInput {
  name: string;
  description: string;
  status: string;
  type: string;
}

interface LocationState {
  department: Department;
}

const EditDepartment: React.FC = () => {
  const toast = useToast({
    position: "top",
  });
  const history = useHistory();

  const location = useLocation<LocationState>();
  const { department } = location.state; // Get the department data passed via state

  // Mutation for editing a department
  const [, editDepartment] = useEditDepartmentMutation();

  const [error, setError] = useState("");
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    setValue, // Used to pre-fill form fields
  } = useForm<IDepartmentInput>();

  useEffect(() => {
    if (department) {
      setValue("name", department.name);
      setValue("description", department.description || "");
      setValue("status", department.status || "active");
      setValue("type", department.type || "");
    }
  }, [department, setValue]);

  const onSubmit = async (values: IDepartmentInput) => {
    setError("");
    const params = {
      name: values.name,
      description: values.description,
      status: values.status,
      type: values.type,
    };

    const { data } = await editDepartment({
      id: department.id, // Use department ID for the edit
      params,
    });

    if (data?.editDepartment.status) {
      toast({
        title: "Department updated successfully!",
        variant: "left-accent",
        status: "success",
        isClosable: true,
      });
      reset();
      history.push("/departments"); // Redirect to department list page
    } else {
      setError(data?.editDepartment.error?.message || "An error occurred.");
      toast({
        title: "Error updating department",
        description: data?.editDepartment.error?.message,
        variant: "left-accent",
        status: "error",
        isClosable: true,
      });
    }
  };

  const textColor = useColorModeValue("gray.700", "white");
  const bgForm = useColorModeValue("white", "navy.800");
  const borderColor = useColorModeValue("gray.200", "gray.600");
  const bgColor = useColorModeValue("white", "gray.700");

  return (
    <Flex
      w="100%"
      h="100%"
      alignItems="center"
      justifyContent="center"
      mb="60px"
      mt={{ base: "50px", md: "50px" }}
    >
      <Flex
        zIndex="2"
        direction="column"
        w="80%"
        background="transparent"
        borderRadius="15px"
        p="40px"
        mx={{ base: "100px" }}
        m={{ base: "20px", md: "auto" }}
        bg={bgForm}
        boxShadow={useColorModeValue(
          "0px 5px 14px rgba(0, 0, 0, 0.05)",
          "unset"
        )}
      >
        <Box
          p={5}
          bg={bgColor}
          borderRadius="lg"
          boxShadow="md"
          border="1px solid"
          borderColor={borderColor}
          mb={5}
        >
          <Grid templateColumns="repeat(3, 1fr)" gap={6}>
            <GridItem>
              <Text fontSize="2xl" fontWeight="bold" color={textColor}>
                <Icon as={FaBuilding} mr={2} />
                Department Details
              </Text>
            </GridItem>
            <GridItem>
              <Text fontSize="lg" fontWeight="bold" color={textColor}>
                <Box as="span" minWidth="120px" display="inline-block">
                  Name:
                </Box>
                {department.name}
              </Text>
              <Text fontSize="md" color={textColor}>
                <Box as="span" minWidth="120px" display="inline-block">
                  Description:
                </Box>
                {department.description}
              </Text>
            </GridItem>
          </Grid>
        </Box>
        <Text
          fontSize="xl"
          color={textColor}
          fontWeight="bold"
          textAlign="center"
          mb="12px"
        >
          Edit Department
        </Text>
        <HSeparator mt="3px" mb="3px" />
        {error && (
          <Text pb="15px" m={"auto"} color="red.500" textColor="red.300">
            {error}
          </Text>
        )}
        <form onSubmit={handleSubmit(onSubmit)}>
          <Grid templateColumns="repeat(6, 1fr)" gap={6}>
            {/* Department Name */}
            <FormControl
              as={GridItem}
              colSpan={6}
              isInvalid={errors.name as any}
            >
              <FormLabel htmlFor="name" fontSize="xs">
                Department Name
              </FormLabel>
              <Input
                variant="filled"
                type="text"
                fontSize="xs"
                placeholder="Department Name"
                size="lg"
                id="name"
                {...register("name", {
                  required: "Department name is required",
                })}
              />
              <FormErrorMessage fontSize="xs">
                {errors.name && (errors.name.message as any)}
              </FormErrorMessage>
            </FormControl>

            {/* Description */}
            <FormControl
              as={GridItem}
              colSpan={6}
              isInvalid={errors.description as any}
            >
              <FormLabel htmlFor="description" fontSize="xs">
                Department Description
              </FormLabel>
              <Input
                variant="filled"
                type="text"
                fontSize="xs"
                placeholder="Department Description"
                size="lg"
                id="description"
                {...register("description", {
                  required: "Description is required",
                })}
              />
              <FormErrorMessage fontSize="xs">
                {errors.description && (errors.description.message as any)}
              </FormErrorMessage>
            </FormControl>

            {/* Status */}
            <FormControl
              as={GridItem}
              colSpan={6}
              isInvalid={errors.status as any}
            >
              <FormLabel htmlFor="status" fontSize="xs">
                Department Status
              </FormLabel>
              <Select
                id="status"
                variant="filled"
                {...register("status", {
                  required: "Status is required",
                })}
              >
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </Select>
              <FormErrorMessage fontSize="xs">
                {errors.status && (errors.status.message as any)}
              </FormErrorMessage>
            </FormControl>

            {/* Department Type */}
            <FormControl
              as={GridItem}
              colSpan={6}
              isInvalid={errors.type as any}
            >
              <FormLabel htmlFor="type" fontSize="xs">
                Department Type
              </FormLabel>
              <Select
                id="type"
                variant="filled"
                {...register("type", {
                  required: "Type is required",
                })}
              >
                <option value="administration">Administration</option>
                <option value="clinical">Clinical</option>
                <option value="support">Support</option>
              </Select>
              <FormErrorMessage fontSize="xs">
                {errors.type && (errors.type.message as any)}
              </FormErrorMessage>
            </FormControl>
          </Grid>

          <Button
            mt="6"
            colorScheme="teal"
            isLoading={isSubmitting}
            type="submit"
            w="full"
          >
            Update Department
          </Button>
        </form>
      </Flex>
    </Flex>
  );
};

export default EditDepartment;
