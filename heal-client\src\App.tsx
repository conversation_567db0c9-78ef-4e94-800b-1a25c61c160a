import { Chakra<PERSON>rovider, Grid } from "@chakra-ui/react";
import { Route, BrowserRouter as Router, Switch } from "react-router-dom";
import { ColorModeSwitcher } from "./ColorModeSwitcher";
import { Provider } from "urql";
import client from "./utils/CreateUrqlClient";
import AuthRoute from "./utils/AuthRoute";
import UnAuthRoute from "./utils/UnAuthRoute";
import theme from "./theme/theme";
import Login from "./pages/login";
import Forgot_password from "./pages/forgot-password";
import Reset_password from "./pages/reset-password";
import DefaultLayout from "./layouts/Default";
import NotFoundPage from "./pages/404";

export const App = () => (
  <Provider value={client}>
    <ChakraProvider theme={theme}>
      <Grid minH="100vh" m={0} p={0}>
        <ColorModeSwitcher justifySelf="flex-end" />
        <Router>
          <Switch>
            {/* protected routes  */}
            <AuthRoute
              exact
              path="/admin/dashboard"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/admin/import-stock"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/employee/import-stock"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/admin/write-off"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/employee/write-off"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/admin/bulk-import"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/employee/bulk-import"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/employee/dashboard"
              component={DefaultLayout}
            />
            <AuthRoute exact path="/employee/users" component={DefaultLayout} />
            <AuthRoute exact path="/admin/users" component={DefaultLayout} />
            <AuthRoute
              exact
              path="/employee/internal/stock"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/admin/internal/stock"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/employee/services"
              component={DefaultLayout}
            />
            <AuthRoute exact path="/admin/services" component={DefaultLayout} />
            <AuthRoute
              exact
              path="/employee/manage-service"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/admin/manage-service"
              component={DefaultLayout}
            />
            <AuthRoute exact path="/admin/stores" component={DefaultLayout} />
            <AuthRoute
              exact
              path="/employee/stores"
              component={DefaultLayout}
            />
            <AuthRoute exact path="/admin/pharmacy" component={DefaultLayout} />
            <AuthRoute
              exact
              path="/employee/pharmacy"
              component={DefaultLayout}
            />
            <AuthRoute exact path="/admin/cashier" component={DefaultLayout} />
            <AuthRoute
              exact
              path="/employee/cashier"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/admin/reception"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/employee/reception"
              component={DefaultLayout}
            />
            <AuthRoute exact path="/admin/visit" component={DefaultLayout} />
            <AuthRoute exact path="/employee/visit" component={DefaultLayout} />
            <AuthRoute exact path="/admin/patients" component={DefaultLayout} />
            <AuthRoute
              exact
              path="/employee/patients"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/admin/patient-update"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/employee/patient-update"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/admin/view-store"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/employee/view-store"
              component={DefaultLayout}
            />
            <AuthRoute exact path="/admin/pharmacy" component={DefaultLayout} />
            <AuthRoute
              exact
              path="/employee/pharmacy"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/admin/view-item"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/employee/view-item"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/admin/add-store"
              component={DefaultLayout}
            />
            <AuthRoute exact path="/admin/dispatch" component={DefaultLayout} />
            <AuthRoute
              exact
              path="/employee/dispatch"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/admin/transfer-items"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/employee/transfer-items"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/employee/add-store"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/admin/edit-store"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/employee/edit-store"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/employee/internal/add-item"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/admin/internal/edit-item"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/employee/internal/edit-item"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/admin/internal/add-item"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/admin/merch/edit-item"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/employee/merch/edit-item"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/employee/merch/add-item"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/admin/merch/add-item"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/employee/merch/stock"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/admin/merch/stock"
              component={DefaultLayout}
            />
            <AuthRoute exact path="/admin/tables" component={DefaultLayout} />
            <AuthRoute exact path="/admin/billing" component={DefaultLayout} />
            <AuthRoute exact path="/admin/profile" component={DefaultLayout} />
            <AuthRoute exact path="/admin/roles" component={DefaultLayout} />
            <AuthRoute
              exact
              path="/admin/edit-country"
              component={DefaultLayout}
            />
            <AuthRoute exact path="/admin/employee" component={DefaultLayout} />
            <AuthRoute exact path="/admin/types" component={DefaultLayout} />
            <AuthRoute exact path="/admin/add-type" component={DefaultLayout} />
            <AuthRoute
              exact
              path="/admin/add-address"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/admin/edit-type"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/admin/categories"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/admin/permissions"
              component={DefaultLayout}
            />
            <AuthRoute exact path="/admin/features" component={DefaultLayout} />
            <AuthRoute
              exact
              path="/admin/companies"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/admin/departments"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/admin/add-company"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/admin/add-department"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/admin/add-subdepartment"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/admin/add-clinic"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/admin/edit-clinic"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/admin/add-schedule"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/admin/edit-schedule"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/admin/nursing-station"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/admin/view-clinic"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/admin/view-department"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/admin/add-vitals"
              component={DefaultLayout}
            />
            <AuthRoute exact path="/admin/roles" component={DefaultLayout} />
            <AuthRoute
              exact
              path="/employee/add-user"
              component={DefaultLayout}
            />
            <AuthRoute exact path="/admin/add-user" component={DefaultLayout} />
            <AuthRoute
              exact
              path="/admin/add-category"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/admin/edit-category"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/admin/add-category"
              component={DefaultLayout}
            />
            <AuthRoute
              exact
              path="/admin/edit-category"
              component={DefaultLayout}
            />
            {/* Unprotected routes */}
            <UnAuthRoute exact path="/login" component={Login} user="user" />
            <UnAuthRoute
              exact
              path="/forgot-password"
              component={Forgot_password}
            />
            <UnAuthRoute
              exact
              path="/reset-password"
              component={Reset_password}
            />
            <UnAuthRoute exact path="/" component={Login} user="user" />
            {/* 404 page */}
            <Route component={NotFoundPage} />
          </Switch>
        </Router>
      </Grid>
    </ChakraProvider>
  </Provider>
);
