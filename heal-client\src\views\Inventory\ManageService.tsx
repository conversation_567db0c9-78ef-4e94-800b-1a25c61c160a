import React, { useContext, useState } from "react";
import { useForm } from "react-hook-form";

import {
  <PERSON>lex,
  Button,
  FormControl,
  FormLabel,
  Input,
  Text,
  useColorModeValue,
  FormErrorMessage,
  useToast,
  Grid,
  GridItem,
} from "@chakra-ui/react";
import {
  Item,
  useAddServiceMutation,
  useEditServiceMutation,
} from "../../generated/graphql";
import { useHistory, useLocation } from "react-router-dom";
import { HSeparator } from "../../components/Separator/Separator";
import { MeContext } from "../../components/Wrapper";

interface IAddEditServiceProps {}

interface State {
  service: Item;
}

const AddEditService: React.FC<IAddEditServiceProps> = () => {
  const history = useHistory();
  const location = useLocation();
  const toast = useToast({
    position: "top",
  });
  const [, addService] = useAddServiceMutation();
  const [, editService] = useEditServiceMutation();
  const [error, setError] = useState("");

  const state = location.state as State;
  const me = useContext(MeContext);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm();

  async function onSubmit(values: any) {
    setError("");
    const args = {
      name: values.name,
      description: values.description,
      reference: values.reference,
      sellingPrice: Number(values.sellingPrice),
    };

    if (state && state.service && state.service.id > 0) {
      const { data } = await editService({ id: state.service.id, args });
      if (data?.editService.error) {
        return setError(data?.editService.error.message);
      } else {
        reset();
        toast({
          title: "Service edited successfully!",
          variant: "left-accent",
          status: "success",
          isClosable: true,
        });
        return history.push({
          pathname: `/${me?.role!.name}/services`,
          state: {
            serviceName: values.name,
          },
        });
      }
    } else {
      const { data } = await addService({ args });
      if (data?.addService.error) {
        return setError(data?.addService.error.message);
      } else {
        reset();
        toast({
          title: "Service added successfully!",
          variant: "left-accent",
          status: "success",
          isClosable: true,
        });
        return history.push({
          pathname: `/${me?.role!.name}/services`,
          state: {
            serviceName: values.name,
          },
        });
      }
    }
  }

  // Chakra color mode
  const textColor = useColorModeValue("gray.700", "white");
  const bgForm = useColorModeValue("white", "navy.800");
  return (
    <Flex position="relative" mb="90px">
      <Flex
        h={{ sm: "initial", md: "75vh", lg: "85vh" }}
        w="100%"
        maxW="1044px"
        mx="auto"
        justifyContent="space-between"
        mb="60px"
        pt={{ md: "0px" }}
      >
        <Flex
          w="100%"
          h="100%"
          alignItems="center"
          justifyContent="center"
          mb="60px"
          mt={{ base: "50px", md: "50px" }}
        >
          <Flex
            zIndex="2"
            direction="column"
            w="720px"
            background="transparent"
            borderRadius="15px"
            p="40px"
            mx={{ base: "100px" }}
            m={{ base: "20px", md: "auto" }}
            bg={bgForm}
            boxShadow={useColorModeValue(
              "0px 5px 14px rgba(0, 0, 0, 0.05)",
              "unset"
            )}
          >
            <Text
              fontSize="xl"
              color={textColor}
              fontWeight="bold"
              textAlign="center"
              mb="12px"
            >
              {state && state.service && state.service.id > 0 ? "Edit" : "Add"}{" "}
              Service
            </Text>

            <HSeparator mt="6px" mb="6px" />
            <Text textAlign="center" mt="0px" mb="0px">
              Service Details:
            </Text>
            <HSeparator mb="22px" mt="6px" />
            {error && (
              <Text mb="10px" color="red.500" textColor="red.300">
                {error}
              </Text>
            )}
            <form onSubmit={handleSubmit(onSubmit)}>
              <Grid templateColumns="repeat(6, 1fr)" gap={6}>
                <FormControl
                  as={GridItem}
                  colSpan={6}
                  mb={3}
                  isInvalid={errors.name as any}
                >
                  <FormLabel htmlFor="name" fontSize="xs">
                    Service Name
                  </FormLabel>
                  <Input
                    variant="filled"
                    ms="4px"
                    type="text"
                    fontSize="xs"
                    placeholder="Enter service name"
                    mb="4px"
                    size="lg"
                    id="name"
                    defaultValue={state?.service?.name}
                    {...register("name", {
                      required: "This is required",
                    })}
                  />
                  <FormErrorMessage mb="10px" fontSize="xs">
                    {errors.name && (errors.name.message as any)}
                  </FormErrorMessage>
                </FormControl>

                <FormControl
                  as={GridItem}
                  colSpan={6}
                  mb={3}
                  isInvalid={errors.description as any}
                >
                  <FormLabel htmlFor="description" fontSize="xs">
                    Service Details
                  </FormLabel>
                  <Input
                    variant="filled"
                    fontSize="xs"
                    ms="4px"
                    type="text"
                    placeholder="Enter service description"
                    mb="4px"
                    size="lg"
                    id="description"
                    defaultValue={state?.service?.description}
                    {...register("description", {
                      required: "This is required",
                    })}
                  />
                  <FormErrorMessage fontSize="xs">
                    {errors.description && (errors.description.message as any)}
                  </FormErrorMessage>
                </FormControl>

                <FormControl
                  as={GridItem}
                  colSpan={6}
                  mb={3}
                  isInvalid={errors.reference as any}
                >
                  <FormLabel htmlFor="reference" fontSize="xs">
                    Service Reference
                  </FormLabel>
                  <Input
                    variant="filled"
                    fontSize="xs"
                    ms="4px"
                    type="text"
                    placeholder="Eg: Department providing the service"
                    mb="4px"
                    size="lg"
                    id="reference"
                    defaultValue={state?.service?.reference}
                    {...register("reference", {
                      required: "This is required",
                    })}
                  />
                  <FormErrorMessage fontSize="xs">
                    {errors.reference && (errors.reference.message as any)}
                  </FormErrorMessage>
                </FormControl>

                <FormControl
                  as={GridItem}
                  colSpan={6}
                  mb={3}
                  isInvalid={errors.sellingPrice as any}
                >
                  <FormLabel htmlFor="sellingPrice" fontSize="xs">
                    Service charge
                  </FormLabel>
                  <Input
                    variant="filled"
                    fontSize="xs"
                    ms="4px"
                    type="number"
                    placeholder="Enter selling price"
                    mb="4px"
                    size="lg"
                    id="sellingPrice"
                    defaultValue={state?.service?.sellingPrice}
                    {...register("sellingPrice", {
                      required: "This is required",
                    })}
                  />
                  <FormErrorMessage fontSize="xs">
                    {errors.sellingPrice &&
                      (errors.sellingPrice.message as any)}
                  </FormErrorMessage>
                </FormControl>
              </Grid>

              <Button
                fontSize="14px"
                variant="dark"
                fontWeight="bold"
                w="100%"
                h="45"
                mb="24px"
                mt={4}
                colorScheme="teal"
                isLoading={isSubmitting}
                type="submit"
              >
                {state && state.service && state.service.id > 0
                  ? "Edit"
                  : "Add"}{" "}
                Service
              </Button>
            </form>
          </Flex>
        </Flex>
      </Flex>
    </Flex>
  );
};

export default AddEditService;
