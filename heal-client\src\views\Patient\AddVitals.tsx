import React from "react";
import {
  Button,
  Flex,
  FormControl,
  FormLabel,
  Grid,
  GridItem,
  Input,
  Text,
  useToast,
  useColorModeValue,
} from "@chakra-ui/react";
import { useForm, Controller } from "react-hook-form";
import { useLocation, useHistory } from "react-router-dom";
import { useAddVitalsMutation } from "../../generated/graphql"; // GraphQL mutation

// Form input type for Vitals
interface VitalsFormInput {
  height: number;
  weight: number;
  pulseRate: number;
  bodyTemperature: number;
  respirationRate: number;
  oxygenSaturation: number;
  systolicPressure: number;
  diastolicPressure: number;
  bloodGlucose: number;
}

const AddVitals: React.FC = () => {
  const toast = useToast({ position: "top" });
  const location = useLocation<{ visitId: number; clientName: string }>();
  const history = useHistory();

  // If clinicId or visitId is missing, redirect to a relevant page (e.g., the clinics page)
  if (!location.state?.visitId || !location.state?.clientName) {
    history.push("nursing-station");
  }

  const { visitId, clientName } = location.state;

  // GraphQL mutation to add vitals
  const [, addVitals] = useAddVitalsMutation();

  // React Hook Form setup
  const {
    handleSubmit,
    control,
    formState: { errors },
  } = useForm<VitalsFormInput>();

  // Submit handler
  const onSubmit = async (data: VitalsFormInput) => {
    try {
      const response = await addVitals({
        params: {
          visitId,
          height: Number(data.height),
          weight: Number(data.weight),
          pulseRate: Number(data.pulseRate),
          bodyTemperature: Number(data.bodyTemperature),
          respirationRate: Number(data.respirationRate),
          oxygenSaturation: Number(data.oxygenSaturation),
          systolicPressure: Number(data.systolicPressure),
          diastolicPressure: Number(data.diastolicPressure),
          bloodGlucose: Number(data.bloodGlucose),
        },
      });

      if (!response.data?.addVitals.error) {
        toast({
          title: "Vitals added successfully",
          status: "success",
          isClosable: true,
        });
        history.push(`nursing-station`); // Redirect to the clinic's visits page
      } else {
        toast({
          title: "Error",
          description:
            response.data?.addVitals.error?.message || "Something went wrong",
          status: "error",
          isClosable: true,
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add vitals.",
        status: "error",
        isClosable: true,
      });
    }
  };

  const textColor = useColorModeValue("gray.700", "white");
  const bgForm = useColorModeValue("white", "gray.600");

  return (
    <Flex
      w="100%"
      h="100%"
      alignItems="center"
      justifyContent="center"
      mt="50px"
    >
      <Flex
        direction="column"
        w="80%"
        bg={bgForm}
        p="40px"
        borderRadius="15px"
        boxShadow="md"
      >
        <Text
          fontSize="xl"
          fontWeight="bold"
          textAlign="center"
          mb="12px"
          color={textColor}
        >
          Add Vitals for {clientName} Visit
        </Text>

        <form onSubmit={handleSubmit(onSubmit)}>
          <Grid templateColumns="repeat(3, 1fr)" gap={6}>
            {/* Height */}
            <GridItem colSpan={1}>
              <FormControl isInvalid={!!errors.height}>
                <FormLabel>Height (cm)</FormLabel>
                <Controller
                  name="height"
                  control={control}
                  defaultValue={0}
                  rules={{ required: "Height is required" }}
                  render={({ field }) => <Input {...field} type="number" />}
                />
              </FormControl>
            </GridItem>

            {/* Weight */}
            <GridItem colSpan={1}>
              <FormControl isInvalid={!!errors.weight}>
                <FormLabel>Weight (kg)</FormLabel>
                <Controller
                  name="weight"
                  control={control}
                  defaultValue={0}
                  rules={{ required: "Weight is required" }}
                  render={({ field }) => <Input {...field} type="number" />}
                />
              </FormControl>
            </GridItem>

            {/* Pulse Rate */}
            <GridItem colSpan={1}>
              <FormControl isInvalid={!!errors.pulseRate}>
                <FormLabel>Pulse Rate (bpm)</FormLabel>
                <Controller
                  name="pulseRate"
                  control={control}
                  defaultValue={0}
                  rules={{ required: "Pulse Rate is required" }}
                  render={({ field }) => <Input {...field} type="number" />}
                />
              </FormControl>
            </GridItem>

            {/* Body Temperature */}
            <GridItem colSpan={1}>
              <FormControl isInvalid={!!errors.bodyTemperature}>
                <FormLabel>Body Temperature (°C)</FormLabel>
                <Controller
                  name="bodyTemperature"
                  control={control}
                  defaultValue={0}
                  rules={{ required: "Body Temperature is required" }}
                  render={({ field }) => <Input {...field} type="number" />}
                />
              </FormControl>
            </GridItem>

            {/* Respiration Rate */}
            <GridItem colSpan={1}>
              <FormControl isInvalid={!!errors.respirationRate}>
                <FormLabel>Respiration Rate (breaths/min)</FormLabel>
                <Controller
                  name="respirationRate"
                  control={control}
                  defaultValue={0}
                  rules={{ required: "Respiration Rate is required" }}
                  render={({ field }) => <Input {...field} type="number" />}
                />
              </FormControl>
            </GridItem>

            {/* Oxygen Saturation */}
            <GridItem colSpan={1}>
              <FormControl isInvalid={!!errors.oxygenSaturation}>
                <FormLabel>Oxygen Saturation (%)</FormLabel>
                <Controller
                  name="oxygenSaturation"
                  control={control}
                  defaultValue={0}
                  rules={{ required: "Oxygen Saturation is required" }}
                  render={({ field }) => <Input {...field} type="number" />}
                />
              </FormControl>
            </GridItem>

            {/* Systolic Pressure */}
            <GridItem colSpan={1}>
              <FormControl isInvalid={!!errors.systolicPressure}>
                <FormLabel>Systolic Pressure (mmHg)</FormLabel>
                <Controller
                  name="systolicPressure"
                  control={control}
                  defaultValue={0}
                  rules={{ required: "Systolic Pressure is required" }}
                  render={({ field }) => <Input {...field} type="number" />}
                />
              </FormControl>
            </GridItem>

            {/* Diastolic Pressure */}
            <GridItem colSpan={1}>
              <FormControl isInvalid={!!errors.diastolicPressure}>
                <FormLabel>Diastolic Pressure (mmHg)</FormLabel>
                <Controller
                  name="diastolicPressure"
                  control={control}
                  defaultValue={0}
                  rules={{ required: "Diastolic Pressure is required" }}
                  render={({ field }) => <Input {...field} type="number" />}
                />
              </FormControl>
            </GridItem>

            {/* Blood Glucose */}
            <GridItem colSpan={1}>
              <FormControl isInvalid={!!errors.bloodGlucose}>
                <FormLabel>Blood Glucose (mg/dL)</FormLabel>
                <Controller
                  name="bloodGlucose"
                  control={control}
                  defaultValue={0}
                  rules={{ required: "Blood Glucose is required" }}
                  render={({ field }) => <Input {...field} type="number" />}
                />
              </FormControl>
            </GridItem>
          </Grid>

          <Button mt={8} colorScheme="teal" w="full" type="submit">
            Submit Vitals
          </Button>
        </form>
      </Flex>
    </Flex>
  );
};

export default AddVitals;
