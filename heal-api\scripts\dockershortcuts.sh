# Login to Docker Hub
docker login -u samxtu

# Build API image
cd heal-api
docker build -t samxtu/heal-api:latest -f Dockerfile.node .

# Build Client image
cd ../heal-client
docker build -t samxtu/heal-client:latest -f Dockerfile.node .

# Push images to Docker Hub
docker push samxtu/heal-api:latest
docker push samxtu/heal-client:latest

# Optional: Tag with version number (e.g., v1.0.0)
docker tag samxtu/heal-api:latest samxtu/heal-api:v1.0.0
docker tag samxtu/heal-client:latest samxtu/heal-client:v1.0.0
docker push samxtu/heal-api:v1.0.0
docker push samxtu/heal-client:v1.0.0