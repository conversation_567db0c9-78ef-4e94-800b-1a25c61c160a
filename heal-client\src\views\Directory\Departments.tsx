// Chakra imports
import {
  Flex,
  Table,
  Tbody,
  Text,
  Th,
  Thead,
  Tr,
  Td,
  Button,
  Input,
  InputGroup,
  InputRightElement,
  useColorModeValue,
} from "@chakra-ui/react";
// Custom components
import Card from "../../components/Card/Card.js";
import CardBody from "../../components/Card/CardBody.js";
import CardHeader from "../../components/Card/CardHeader.js";
import { useGetDepartmentsQuery } from "../../generated/graphql";
import { SearchIcon } from "@chakra-ui/icons";
import { Link, useHistory } from "react-router-dom";
import { useState } from "react";

function ViewDepartments() {
  const [{ data: departments }] = useGetDepartmentsQuery({
    requestPolicy: "network-only",
  }); // Fetch data using the hook
  const [searchQuery, setSearchQuery] = useState(""); // State to store the search query
  const textColor = useColorModeValue("gray.700", "white");
  const borderColor = useColorModeValue("gray.200", "gray.600");
  const buttonBg = useColorModeValue("teal.400", "teal.600");
  const buttonHoverBg = useColorModeValue("teal.600", "teal.700");
  const history = useHistory();

  const handleSearchChange = (e: { target: { value: string } }) => {
    setSearchQuery(e.target.value);
  };

  const filteredDepartments = departments?.getDepartments?.filter(
    (department) => {
      return (
        department.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        department.status?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }
  );

  return (
    <Flex direction="column" pt={{ base: "120px", md: "75px" }}>
      <Card overflowX={{ sm: "scroll", xl: "hidden" }} pb="0px">
        <CardHeader
          p="6px 0px 22px 0px"
          display="flex"
          justifyContent="space-between"
          alignItems="center"
        >
          <Text fontSize="xl" color={textColor} fontWeight="bold">
            Departments
          </Text>
          <Link to={"add-department"}>
            <Button variant="outline">Add Department</Button>
          </Link>
          <InputGroup w={{ base: "100%", md: "300px" }}>
            <Input
              placeholder="Search departments..."
              value={searchQuery}
              onChange={handleSearchChange}
              bg={useColorModeValue("white", "teal.800")}
              borderColor={borderColor}
              _placeholder={{ color: "gray.400" }}
            />
            <InputRightElement pointerEvents="none">
              <SearchIcon color="gray.300" />
            </InputRightElement>
          </InputGroup>
        </CardHeader>
        <CardBody>
          <Table variant="simple" color={textColor}>
            <Thead>
              <Tr my=".8rem" pl="0px" color="gray.400">
                <Th pl="0px" borderColor={borderColor} color={textColor}>
                  Department Name
                </Th>
                <Th borderColor={borderColor} color={textColor}>
                  Type
                </Th>
                <Th borderColor={borderColor} color={textColor}>
                  Status
                </Th>
                <Th borderColor={borderColor} color={textColor}>
                  Parent Department
                </Th>
                <Th borderColor={borderColor} color={textColor}>
                  No of Employees
                </Th>
                <Th borderColor={borderColor}></Th>
              </Tr>
            </Thead>
            <Tbody>
              {filteredDepartments?.length && filteredDepartments.length > 0 ? (
                filteredDepartments.map((department) => (
                  <Tr key={department.id}>
                    <Td pl="0px" borderColor={borderColor}>
                      {department.name}
                    </Td>
                    <Td pl="0px" borderColor={borderColor}>
                      {department.type}
                    </Td>
                    <Td borderColor={borderColor}>
                      <Text
                        fontWeight="bold"
                        color={
                          department.status === "active"
                            ? "green.400"
                            : department.status === "inactive"
                            ? "yellow.400"
                            : "red.400"
                        }
                      >
                        {department.status || "N/A"}
                      </Text>
                    </Td>
                    <Td pl="0px" borderColor={borderColor}>
                      {departments?.getDepartments.find(
                        (dep) => dep.id === department.parentId
                      )?.name || "N/A"}
                    </Td>
                    <Td borderColor={borderColor}>
                      {department.employees?.length || "N/A"}
                    </Td>
                    <Td borderColor={borderColor} textAlign="right">
                      <Button
                        size="sm"
                        bg={buttonBg}
                        color="white"
                        _hover={{ bg: buttonHoverBg }}
                        as={Link}
                        to={{
                          pathname: "edit-department",
                          state: {
                            departmentId: department.id,
                          },
                        }}
                        mr="2"
                      >
                        Edit Department
                      </Button>
                      <Button
                        size="sm"
                        bg="blue.500"
                        color="white"
                        _hover={{ bg: "blue.600" }}
                        onClick={() => {
                          history.push({
                            pathname: "view-department",
                            state: {
                              department: department,
                            },
                          });
                        }}
                      >
                        View Department
                      </Button>
                    </Td>
                  </Tr>
                ))
              ) : (
                <Tr>
                  <Td colSpan={5} textAlign="center" color="gray.500">
                    No departments found.
                  </Td>
                </Tr>
              )}
            </Tbody>
          </Table>
        </CardBody>
      </Card>
    </Flex>
  );
}

export default ViewDepartments;
