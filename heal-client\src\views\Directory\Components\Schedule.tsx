import React, { useMemo } from "react";
import { Box, useColorModeValue } from "@chakra-ui/react";
import FullCalendar from "@fullcalendar/react";
import timeGridPlugin from "@fullcalendar/timegrid";
import interactionPlugin from "@fullcalendar/interaction";
import { addDays, format, startOfDay, getDay } from "date-fns";

interface ScheduleItem {
  id: number;
  onTime: string;
  offTime: string;
  day: string;
  description?: string | null | undefined;
}

interface ClinicScheduleProps {
  schedules: ScheduleItem[];
}

const ClinicSchedule: React.FC<ClinicScheduleProps> = ({ schedules }) => {
  const backgroundColor = useColorModeValue("white", "gray.600");
  const textColor = useColorModeValue("gray.800", "white");

  // Get the current day index (0 = Sunday, 1 = Monday, etc.)
  const today = new Date();
  const todayDayIndex = getDay(today);

  // Map days to numerical values, for comparison
  const dayMapping: { [key: string]: number } = {
    sunday: 0,
    monday: 1,
    tuesday: 2,
    wednesday: 3,
    thursday: 4,
    friday: 5,
    saturday: 6,
  };

  // Transform schedules into FullCalendar event format
  const events = useMemo(() => {
    const eventsList: any[] = [];

    for (let i = 0; i < 7; i++) {
      const currentDate = addDays(startOfDay(today), i);

      schedules.forEach((schedule) => {
        // Check if the schedule matches the current day
        if (dayMapping[schedule.day.toLowerCase()] === getDay(currentDate)) {
          eventsList.push({
            id: schedule.id.toString(),
            title: schedule.description || "Shift",
            start: `${format(currentDate, "yyyy-MM-dd")}T${schedule.onTime}`,
            end: `${format(currentDate, "yyyy-MM-dd")}T${schedule.offTime}`,
          });
        }
      });
    }
    return eventsList;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [schedules, today]);

  return (
    <Box
      p={4}
      bgColor={backgroundColor}
      borderRadius="lg"
      height="700px"
      boxShadow="md"
    >
      <FullCalendar
        plugins={[timeGridPlugin, interactionPlugin]}
        initialView="timeGridWeek" // Weekly view with timeGrid
        events={events}
        headerToolbar={false} // Remove header toolbar
        allDaySlot={false} // Disable all-day slot
        slotMinTime="00:00:00" // Start at midnight
        slotMaxTime="24:00:00" // End at midnight
        slotDuration="01:00:00" // Hourly slots
        height="100%"
        contentHeight="auto"
        themeSystem="standard"
        eventColor={useColorModeValue("teal", "blue.400")}
        eventTextColor="white"
        nowIndicator={true}
        editable={false}
        initialDate={today} // Set the initial date to today
        validRange={{
          start: today, // Ensure the calendar starts from today
        }}
        dayHeaderContent={({ date }) => (
          <span style={{ color: textColor }}>
            {format(date, "EEEE, MMM d")}
          </span>
        )}
        firstDay={todayDayIndex} // Start the week with today as the first column
      />
    </Box>
  );
};

export default ClinicSchedule;
