import React from "react";
import AsyncSelect from "react-select/async";
import { useGetAllItemsQuery } from "../../../generated/graphql";

interface AllItemsInputProps {
  onChange: (value: any) => void;
  selectedItemsIds: Array<{ itemId: number }>;
}

const AllItemsInput: React.FC<AllItemsInputProps> = ({
  onChange,
  selectedItemsIds,
}) => {
  const [{ data: items, fetching: fetchingItems }, getItemsAgain] =
    useGetAllItemsQuery({
      requestPolicy: "cache-and-network",
    });

  // Load items to the input
  const loadItems = async (searchInput: string) => {
    if (searchInput.length > 0) {
      if (!fetchingItems && items) {
        return items.getAllItems
          .filter(
            (item) =>
              item.name
                .toLocaleLowerCase()
                .includes(searchInput.toLocaleLowerCase()) &&
              !selectedItemsIds.some((ii) => ii.itemId === item.id)
          )
          .map((r) => ({
            label: r.name,
            value: r.id,
          }));
      }
      await getItemsAgain({ requestPolicy: "network-only" });
      if (items) {
        return items.getAllItems
          .filter((item) =>
            item.name
              .toLocaleLowerCase()
              .includes(searchInput.toLocaleLowerCase())
          )
          .map((r) => ({
            label: r.name,
            value: r.id,
          }));
      }
    } else {
      if (!fetchingItems && items)
        return items.getAllItems
          .filter(
            (item) => !selectedItemsIds.some((ii) => ii.itemId === item.id)
          )
          .map((r) => ({
            label: r.name,
            value: r.id,
          }));
    }
    return [];
  };

  return (
    <AsyncSelect
      variant="flushed"
      isClearable
      isSearchable={true}
      isLoading={fetchingItems}
      defaultOptions
      loadOptions={loadItems}
      size="sm"
      placeholder="Select item"
      closeMenuOnSelect={true}
      onChange={onChange}
      escapeClearsValue={true}
      hideSelectedOptions={true}
      styles={{
        container: (base) => ({
          ...base,
          width: "100%",
          color: "navy",
        }),
      }}
    />
  );
};

export default AllItemsInput;
