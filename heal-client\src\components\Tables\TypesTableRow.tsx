import {
  Badge,
  Button,
  Flex,
  Icon,
  Td,
  Text,
  Tr,
  useColorModeValue,
} from "@chakra-ui/react";
import { FaPencilAlt, FaTrashAlt } from "react-icons/fa";

function TypesTableRow(props: any) {
  const { name, count, description, isLast } = props;
  const textColor = useColorModeValue("gray.500", "white");
  const titleColor = useColorModeValue("gray.700", "white");
  const bgStatus = useColorModeValue("gray.400", "navy.900");
  const borderColor = useColorModeValue("gray.200", "gray.600");

  return (
    <Tr>
      <Td
        minWidth={{ sm: "250px" }}
        pl="0px"
        borderColor={borderColor}
        borderBottom={isLast ? ("none" as any) : null}
      >
        <Text
          fontSize="md"
          color={titleColor}
          fontWeight="bold"
          minWidth="100%"
        >
          {name}
        </Text>
      </Td>

      <Td
        borderColor={borderColor}
        borderBottom={isLast ? ("none" as any) : null}
      >
        <Text fontSize="md" color={textColor} fontWeight="bold">
          {description}
        </Text>
      </Td>
      <Td
        borderColor={borderColor}
        borderBottom={isLast ? ("none" as any) : null}
      >
        <Badge
          bg={count > 1 ? "green.400" : bgStatus}
          color={count < 2 ? "white" : "white"}
          fontSize="16px"
          p="3px 10px"
          ml={10}
          borderRadius="8px"
        >
          {count}
        </Badge>
      </Td>
      <Td
        borderColor={borderColor}
        borderBottom={isLast ? ("none" as any) : null}
      >
        <Flex
          direction={{ sm: "column", md: "row" }}
          align="flex-start"
          p={{ md: "0px" }}
        >
          <Button
            onClick={() => {
              return props.navigate(
                {
                  pathname: "/admin/edit-type",
                },
                {
                  state: {
                    id: props.typeId,
                    name: name,
                    description: description,
                    categories: props.categories,
                  },
                }
              );
            }}
            p="0px"
            bg="transparent"
            variant="no-effects"
          >
            <Flex color={textColor} cursor="pointer" align="center" p="12px">
              <Icon as={FaPencilAlt} me="4px" />
              <Text fontSize="sm" fontWeight="semibold">
                EDIT
              </Text>
            </Flex>
          </Button>
          <Button
            p="0px"
            bg="transparent"
            variant="no-effects"
            mb={{ sm: "10px", md: "0px" }}
            me={{ md: "12px" }}
            onClick={props.callOnClick}
          >
            <Flex color="red.200" cursor="pointer" align="center" p="12px">
              <Icon as={FaTrashAlt} me="4px" />
              <Text fontSize="sm" fontWeight="semibold">
                DELETE
              </Text>
            </Flex>
          </Button>
        </Flex>
      </Td>
    </Tr>
  );
}

export default TypesTableRow;
