import React, { ChangeEvent, useEffect, useRef, useState } from "react";
import {
  useColorModeValue,
  Box,
  Text,
  Flex,
  Icon,
  Progress,
  useToast,
  Button,
} from "@chakra-ui/react";
import { AiOutlineUpload } from "react-icons/ai";
import supabase from "../supabase";
import { v4 } from "uuid";
import { IoMdRemoveCircle } from "react-icons/io";

interface FileInputProps {
  label: string;
  clear: boolean;
  setFileUrl: (value: string) => void;
  defaultClear: (value: boolean) => void;
  setLoading: (value: boolean) => void;
  setError: (value: string) => void;
  defaultUrl: string | undefined;
}

const FileInput: React.FC<FileInputProps> = ({
  label,
  setFileUrl,
  setLoading,
  clear,
  defaultClear,
  setError,
  defaultUrl,
}) => {
  const borderColor = useColorModeValue("gray.200", "gray.600");
  const hoverBorderColor = useColorModeValue("gray.300", "gray.500");
  const [progressPercent, setProgressPercent] = useState(0);
  const [fileName, setFileName] = useState<string>("");
  const [fileId, setId] = useState<string>("");
  const [internalLoading, setInternalLoading] = useState<boolean>(false);

  useEffect(() => {
    if (clear) {
      setFileName("");
      setError("");
      setProgressPercent(0);
      defaultClear(false);
      setInternalLoading(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [clear]);

  useEffect(() => {
    if (defaultUrl) {
      setId(defaultUrl);
      setFileName("Previously uploaded file");
      setProgressPercent(100);
    }
  }, [defaultUrl]);

  const toast = useToast({
    position: "top",
  });

  const inputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = async (event: ChangeEvent<HTMLInputElement>) => {
    setProgressPercent(0);
    setError("");
    setLoading(true);
    setInternalLoading(true);
    const file = event.target.files?.[0];
    if (file) {
      setFileName(file.name);
      const { data: uploadedFile, error: uploadError } = await supabase.storage
        .from("heal")
        .upload(`public/${v4()}`, file);
      setInternalLoading(false);
      if (uploadError) {
        setError(uploadError.message);
        return toast({
          title: `File can not be uploaded!`,
          variant: "left-accent",
          status: "error",
          isClosable: true,
        });
      } else if (uploadedFile) {
        console.log("uploaded file response: ", uploadedFile);
        setProgressPercent(100);
        setFileUrl(
          `https://clcxzryztgsyurhguhoo.supabase.co/storage/v1/object/public/heal/${uploadedFile.path}`
        );
        setId(uploadedFile.path);
        setLoading(false);
      }
    } else {
      setFileName("");
    }
  };

  const handleInputClick = () => {
    if (inputRef.current) {
      inputRef.current.click();
    }
  };

  return (
    <Box position="relative">
      {internalLoading ? (
        <Progress size="xs" w={"100%"} isIndeterminate />
      ) : (
        <Progress
          size="xs"
          value={progressPercent}
          position="absolute"
          top="0"
          left="0"
          right="0"
          borderRadius="md"
          color={"green"}
        />
      )}
      <Flex
        align="center"
        justify="space-between"
        p={2}
        border="1px solid"
        borderColor={borderColor}
        borderRadius="md"
        cursor="pointer"
        _hover={{ borderColor: hoverBorderColor }}
        onClick={handleInputClick}
      >
        <Text>{label}</Text>
        <label htmlFor="file-input">
          <Flex align="center">
            <Icon as={AiOutlineUpload} mr={2} />
            <Text>{fileName === "" ? "Choose a receipt file" : fileName}</Text>
            {fileName !== "" && (
              <Button
                m={0}
                p={0}
                ml={2}
                height="inherit"
                variant="ghost"
                onClick={async () => {
                  defaultClear(true);
                  const { error: deleteError } = await supabase.storage
                    .from("heal")
                    .remove([fileId]);
                  if (deleteError) {
                    console.error(
                      "Error deleteng file metadata:",
                      deleteError.message
                    );
                  }
                }}
              >
                <Icon as={IoMdRemoveCircle} mx={2} />
              </Button>
            )}
          </Flex>
        </label>
        <input
          ref={inputRef}
          id="file-input"
          type="file"
          accept=".jpg,.jpeg,.png,.pdf"
          onChange={handleFileChange}
          style={{ display: "none" }}
        />
      </Flex>
    </Box>
  );
};

export default FileInput;
