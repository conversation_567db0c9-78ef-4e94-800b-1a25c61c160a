import * as bcrypt from "bcryptjs";

// Basic hash and verify functions
export const hash = async (password: string): Promise<string> => {
  const saltRounds = 10;
  return bcrypt.hash(password, saltRounds);
};

export const verify = async (
  hash: string,
  password: string
): Promise<boolean> => {
  return bcrypt.compare(password, hash);
};

// Dragon object with hash and verify methods (equivalent to the argon2 version)
export const dragon = {
  // Enhanced hash function with stronger parameters
  hash: async (password: string): Promise<string> => {
    const saltRounds = 12; // Higher salt rounds for more security
    return bcrypt.hash(password, saltRounds);
  },

  // Standard verify function
  verify: async (hash: string, password: string): Promise<boolean> => {
    return bcrypt.compare(password, hash);
  },
};
