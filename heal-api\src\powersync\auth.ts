import express from "express";
import { getPublicJwks } from "./jwks";

const router = express.Router();

// JWKS endpoint for PowerSync server
router.get("/keys", async (req, res) => {
  console.log("🔑 [JWKS] PowerSync server requesting JW<PERSON> keys");
  console.log("🔑 [JWKS] Request headers:", req.headers);
  console.log("🔑 [JWKS] Request URL:", req.url);
  console.log("🔑 [JWKS] Request method:", req.method);
  console.log("🔑 [JWKS] User-Agent:", req.get("User-Agent"));

  try {
    const jwks = await getPublicJwks();
    console.log(
      "🔑 [JWKS] Returning JWKS keys:",
      JSON.stringify(jwks, null, 2)
    );

    // Set proper headers for PowerSync
    res.setHeader("Content-Type", "application/json");
    res.setHeader("Cache-Control", "public, max-age=300"); // Cache for 5 minutes
    res.setHeader("Access-Control-Allow-Origin", "*");
    res.setHeader("Access-Control-Allow-Methods", "GET, OPTIONS");
    res.setHeader("Access-Control-Allow-Headers", "Content-Type");

    res.send(jwks);
  } catch (error) {
    console.error("❌ [JWKS] Error generating JWKS:", error);
    res.status(500).json({
      error: "Failed to generate JWKS",
      details: error.message,
      timestamp: new Date().toISOString(),
    });
  }
});

// Health check endpoint for JWKS
router.get("/health", async (_req, res) => {
  try {
    const jwks = await getPublicJwks();
    res.json({
      status: "ok",
      keys: jwks.keys.length,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    res.status(500).json({
      status: "error",
      error: error.message,
      timestamp: new Date().toISOString(),
    });
  }
});

export { router as authRouter };
