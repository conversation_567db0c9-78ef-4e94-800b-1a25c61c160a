import React, { useContext, useState } from "react";
import { useForm } from "react-hook-form";
import {
  <PERSON>lex,
  Button,
  FormControl,
  FormLabel,
  Input,
  Text,
  useColorModeValue,
  FormErrorMessage,
  useToast,
  Grid,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Spinner,
  GridItem,
  Tag,
  TagLabel,
  Badge,
} from "@chakra-ui/react";
import {
  useGetAllItemsQuery,
  useGetDispatchesQuery,
  useDispatchItemsMutation,
  useGetStoresQuery,
  useChangeInventoryApprovalStatusMutation,
  useChangeInventoryReceivedStatusMutation,
  useChangeInventoryDispatchedStatusMutation,
  Item,
} from "../../generated/graphql";
import { toDateTime } from "../../utils/Helpers";
import { MeContext } from "../../components/Wrapper";

//icons
import { MdOutlineMoveUp } from "react-icons/md";
import { IoMdCheckmarkCircleOutline } from "react-icons/io";
import AllItemsInput from "./components/AllItemsInput";
import UnitInput from "./components/UnitInput";
import StoreInput from "./components/StoreInput";
import BatchInput from "./components/BatchInput";

interface IDispatchProps {}

interface IDispatchItem {
  itemId: number;
  quantity: number;
  remarks: string;
  locationId: number;
  batch: string;
}

const Dispatch: React.FC<IDispatchProps> = () => {
  const toast = useToast({
    position: "top",
  });

  const nameColor = useColorModeValue("gray.500", "white");

  const me = useContext(MeContext);

  const [, dispatchItems] = useDispatchItemsMutation();
  const [, approveDispatch] = useChangeInventoryApprovalStatusMutation();
  const [, initiateDispatch] = useChangeInventoryDispatchedStatusMutation();
  const [, receiveDispatch] = useChangeInventoryReceivedStatusMutation();
  const [{ data: items }] = useGetAllItemsQuery();
  const [{ data: stores }] = useGetStoresQuery();
  const [{ data: dispatches, fetching: fetchingDispatches }, reGetDispatches] =
    useGetDispatchesQuery({
      requestPolicy: "network-only",
    });
  const [selectedItem, setSelectedItem] = useState<number | null>(null);
  const [itemsAdded, setItemsAdded] = useState<IDispatchItem[]>([]);
  const [unitType, setUnitType] = useState("");
  const [selectedStore, setSelectedStore] = useState<number | undefined>(
    undefined
  );

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
    setValue,
  } = useForm({
    defaultValues: {
      itemId: 0,
      quantity: 0,
      remarks: "",
      locationId: 0,
      batch: "",
      unit: "",
    },
  });

  const handleItemChange = (value: any) => {
    setSelectedItem(value.value);
    setValue("itemId", value.value);
    reset();
  };

  const handleUnitChange: any = (value: any) => {
    if (value) {
      setUnitType(value.value);
      setValue("unit", value.value);
    } else {
      setUnitType("");
    }
  };

  const handleStoreChange = (value: any) => {
    setValue("locationId", value.value);
    setSelectedStore(value.value);
  };

  const handleBatchChange = (value: any) => {
    setValue("batch", value.value);
  };

  const onSubmit = (values: IDispatchItem) => {
    setItemsAdded((oldItems) => {
      return [...oldItems, { ...values, itemId: Number(selectedItem) }];
    });
    setSelectedItem(null);
    return reset();
  };

  async function onDBSubmit() {
    const args = itemsAdded.map((item: IDispatchItem) => ({
      itemId: Number(item.itemId),
      quantity: Number(item.quantity),
      remarks: item.remarks,
      unit: unitType,
      locationId: Number(item.locationId),
      batch: item.batch,
    }));
    const { data } = await dispatchItems({ args });
    if (data?.dispatchItems.error) {
      toast({
        title: data.dispatchItems.error.message,
        status: "error",
        isClosable: true,
      });
    } else {
      reset();
      setSelectedItem(null);
      setItemsAdded([]);
      reGetDispatches();
      return toast({
        title: `Items dispatch request sent successfully!`,
        variant: "left-accent",
        status: "success",
        isClosable: true,
      });
    }
  }

  const bgColor = useColorModeValue("white", "navy.800");
  const boxShadowColor = useColorModeValue(
    "0px 5px 14px rgba(0, 0, 0, 0.05)",
    "unset"
  );

  return (
    <Flex direction="column" position="relative" mb="90px" mt="100px">
      <Flex
        w="100%"
        mx="auto"
        justifyContent="space-between"
        mb="60px"
        pt={{ md: "0px" }}
        direction="column"
      >
        {(me?.role!.name === "admin" ||
          stores?.getStores.find((store) =>
            store.storeKeepers.find(
              (storeKeeper) => storeKeeper.id === me?.employee?.id
            )
          )?.name) && (
          <Flex
            w="100%"
            as={Grid}
            templateColumns="repeat(12, 1fr)"
            gap={6}
            mb="60px"
          >
            <Flex
              direction="column"
              background="transparent"
              borderRadius="15px"
              as={GridItem}
              colSpan={5}
              width={"100%"}
              p="40px"
              bg={bgColor}
              boxShadow={boxShadowColor}
            >
              <Text
                fontSize="xl"
                color={nameColor}
                fontWeight="bold"
                textAlign="center"
                mb="12px"
              >
                Request Item Dispatch{" "}
                {me?.role!.name === "admin"
                  ? "From " +
                    stores?.getStores.find((store) => store.primary === true)
                      ?.name
                  : stores?.getStores.find((store) =>
                      store.storeKeepers.find(
                        (storeKeeper) => storeKeeper.id === me?.employee?.id
                      )
                    )?.name}
              </Text>
              <FormControl mb={4}>
                <FormLabel fontSize="xs">Item</FormLabel>
                <AllItemsInput
                  onChange={handleItemChange}
                  selectedItemsIds={[]}
                />
              </FormControl>
              {selectedItem && (
                <>
                  <Text
                    fontSize="xl"
                    color={nameColor}
                    fontWeight="bold"
                    textAlign="center"
                    mb="12px"
                  >
                    Dispatch Details
                  </Text>
                  <form onSubmit={handleSubmit(onSubmit)}>
                    <Grid templateColumns="repeat(2, 1fr)" gap={4} mb={4}>
                      <Input
                        type="hidden"
                        value={selectedItem}
                        {...register("itemId", { value: selectedItem })}
                      />
                      <FormControl
                        isInvalid={Boolean(errors.locationId)}
                        mb={4}
                      >
                        <FormLabel htmlFor="locationId" fontSize="xs">
                          Destination Store
                        </FormLabel>
                        <StoreInput onChange={handleStoreChange} />
                        <FormErrorMessage fontSize="xs">
                          {errors.locationId?.message}
                        </FormErrorMessage>
                      </FormControl>
                      <FormControl isInvalid={Boolean(errors.batch)} mb={4}>
                        <FormLabel htmlFor="batch" fontSize="xs">
                          Batch Number
                        </FormLabel>
                        <BatchInput
                          onChange={handleBatchChange}
                          storeId={selectedStore}
                          item={
                            items?.getAllItems.find(
                              (item) => item.id === selectedItem
                            ) as Item
                          }
                        />
                        <FormErrorMessage fontSize="xs">
                          {errors.batch?.message}
                        </FormErrorMessage>
                      </FormControl>{" "}
                      <FormControl isInvalid={Boolean(errors.quantity)} mb={4}>
                        <FormLabel htmlFor="quantity" fontSize="xs">
                          Quantity
                        </FormLabel>
                        <Input
                          id="quantity"
                          type="number"
                          placeholder="Quantity"
                          {...register("quantity", {
                            required: "This is required",
                          })}
                        />
                        <FormErrorMessage fontSize="xs">
                          {errors.quantity?.message}
                        </FormErrorMessage>
                      </FormControl>
                      <FormControl isInvalid={Boolean(errors.unit)} mb={4}>
                        <FormLabel htmlFor="unit" fontSize="xs">
                          Unit
                        </FormLabel>
                        <UnitInput
                          onChange={handleUnitChange}
                          selectedItem={
                            items?.getAllItems.find(
                              (item) => item.id === selectedItem
                            ) as Item
                          }
                        />
                        <FormErrorMessage fontSize="xs">
                          {errors.unit?.message}
                        </FormErrorMessage>
                      </FormControl>
                      <FormControl
                        isInvalid={Boolean(errors.remarks)}
                        mb={4}
                        as={GridItem}
                        colSpan={2}
                      >
                        <FormLabel htmlFor="remarks" fontSize="xs">
                          Remarks
                        </FormLabel>
                        <Input
                          id="remarks"
                          placeholder="Remarks"
                          {...register("remarks", {
                            required: "This is required",
                          })}
                        />
                        <FormErrorMessage fontSize="xs">
                          {errors.remarks?.message}
                        </FormErrorMessage>
                      </FormControl>
                    </Grid>
                    <Button
                      colorScheme="blue"
                      type="submit"
                      isLoading={isSubmitting}
                      w={"100%"}
                    >
                      Add Item
                    </Button>
                  </form>
                </>
              )}
            </Flex>
            <Flex
              direction="column"
              background="transparent"
              borderRadius="15px"
              as={GridItem}
              colSpan={7}
              width={"100%"}
              p="40px"
              bg={bgColor}
              boxShadow={boxShadowColor}
            >
              <Text
                fontSize="xl"
                color={nameColor}
                fontWeight="bold"
                textAlign="center"
                mb="12px"
              >
                Items to Dispatch
              </Text>
              <Table variant="simple" mb="20px">
                <Thead>
                  <Tr>
                    <Th>Name</Th>
                    <Th>Quantity</Th>
                    <Th>Remarks</Th>
                    <Th>Destination Store</Th>
                    <Th>Batch Number</Th>
                  </Tr>
                </Thead>
                <Tbody>
                  {itemsAdded.map((item, index) => {
                    const itemName = items?.getAllItems.find(
                      (i) => i.id === item.itemId
                    )?.name;
                    const storeName = stores?.getStores.find(
                      (s) => s.id === item.locationId
                    )?.name;
                    return (
                      <Tr key={index}>
                        <Td>{itemName}</Td>
                        <Td>{item.quantity}</Td>
                        <Td>{item.remarks}</Td>
                        <Td>{storeName}</Td>
                        <Td>{item.batch}</Td>
                      </Tr>
                    );
                  })}
                </Tbody>
              </Table>
              <Flex justifyContent="center">
                {itemsAdded.length > 0 && (
                  <Button
                    colorScheme="blue"
                    onClick={onDBSubmit}
                    isLoading={isSubmitting}
                  >
                    Dispatch Items
                  </Button>
                )}
              </Flex>
            </Flex>
          </Flex>
        )}
        <Flex
          direction="column"
          background="transparent"
          borderRadius="15px"
          width={"100%"}
          p="40px"
          bg={useColorModeValue("white", "navy.800")}
          boxShadow={useColorModeValue(
            "0px 5px 14px rgba(0, 0, 0, 0.05)",
            "unset"
          )}
        >
          <Text
            fontSize="xl"
            color={nameColor}
            fontWeight="bold"
            textAlign="center"
            mb="12px"
          >
            Dispatch History
          </Text>
          {fetchingDispatches ? (
            <Flex justify="center" align="center" height="200px">
              <Spinner size="xl" />
            </Flex>
          ) : (
            <Table variant="simple">
              <Thead>
                <Tr>
                  <Th>Items</Th>
                  <Th>Origin</Th>
                  <Th>Destination</Th>
                  <Th>On (Date)</Th>
                </Tr>
              </Thead>
              <Tbody>
                {dispatches?.getDispatches.map((dispatch) => {
                  return (
                    <Tr key={dispatch.id}>
                      <Td>
                        {dispatch.transfers.map((transfer) => {
                          const item = dispatch.items.find(
                            (item) => item.id === transfer.itemId
                          );
                          return (
                            <Tag
                              size="lg"
                              key={transfer.id}
                              borderRadius="full"
                              variant="solid"
                              colorScheme="cyan"
                              p="auto"
                              m={1}
                            >
                              <TagLabel p="auto">
                                {item?.name}
                                <Badge
                                  variant="solid"
                                  colorScheme="red"
                                  ml={3}
                                  p="auto"
                                  fontSize="1em"
                                >
                                  {transfer.quantity > 0
                                    ? transfer.quantity + " " + item?.unit
                                    : " "}
                                  {transfer.pieceQuantity > 0
                                    ? transfer.pieceQuantity +
                                      " " +
                                      item?.pieceUnit
                                    : ""}
                                  {transfer.subPieceQuantity > 0
                                    ? transfer.subPieceQuantity +
                                      " " +
                                      item?.subPieceUnit
                                    : ""}
                                </Badge>
                              </TagLabel>
                            </Tag>
                          );
                        })}
                      </Td>
                      <Td>{dispatch.sourceStore?.name}</Td>
                      <Td>{dispatch.destinationStore?.name}</Td>
                      <Td>{toDateTime(Number(dispatch.updatedAt))}</Td>
                      <Td>
                        {dispatch.granted ? (
                          <Badge variant="subtle" colorScheme="green">
                            Approved
                          </Badge>
                        ) : me?.role!.name === "admin" ? (
                          <Button
                            rightIcon={<IoMdCheckmarkCircleOutline />}
                            colorScheme="teal"
                            variant="outline"
                            my={1}
                            size="xs"
                            onClick={async () => {
                              const approval = await approveDispatch({
                                inventoryId: dispatch.id,
                              });
                              if (
                                approval.data?.changeInventoryApprovalStatus
                                  .error
                              ) {
                                return toast({
                                  title:
                                    approval.data?.changeInventoryApprovalStatus
                                      .error.message,
                                  status: "error",
                                  isClosable: true,
                                });
                              }
                              await reGetDispatches();
                              return toast({
                                title: `Dispatch approved successfully!`,
                                variant: "left-accent",
                                status: "success",
                                isClosable: true,
                              });
                            }}
                          >
                            Approve
                          </Button>
                        ) : (
                          // put here a check if someone has permission to approve this
                          <Badge variant="solid" colorScheme="yellow">
                            Pending approval
                          </Badge>
                        )}
                        <br />
                        {dispatch.dispatched ? (
                          <Badge variant="subtle" colorScheme="green">
                            Dispatched
                          </Badge>
                        ) : (me?.role!.name === "admin" ||
                            stores?.getStores.find((store) =>
                              store.storeKeepers.find(
                                (storeKeeper) =>
                                  storeKeeper.id === dispatch.keeperId
                              )
                            )?.name) &&
                          dispatch.granted ? (
                          <Button
                            rightIcon={<MdOutlineMoveUp />}
                            colorScheme="teal"
                            variant="outline"
                            my={1}
                            size="xs"
                            onClick={async () => {
                              const dispatchInitiated = await initiateDispatch({
                                inventoryId: dispatch.id,
                              });
                              if (
                                dispatchInitiated.data
                                  ?.changeInventoryDispatchedStatus.error
                              ) {
                                return toast({
                                  title:
                                    dispatchInitiated.data
                                      ?.changeInventoryDispatchedStatus.error
                                      .message,
                                  status: "error",
                                  isClosable: true,
                                });
                              }
                              await reGetDispatches();
                              return toast({
                                title: `Dispatch sent successfully!`,
                                variant: "left-accent",
                                status: "success",
                                isClosable: true,
                              });
                            }}
                          >
                            Dispatch
                          </Button>
                        ) : (
                          <Badge variant="solid" colorScheme="yellow">
                            Pending dispatch
                          </Badge>
                        )}
                        <br />
                        {dispatch.received ? (
                          <Badge variant="subtle" colorScheme="green">
                            Received
                          </Badge>
                        ) : (me?.role!.name === "admin" ||
                            stores?.getStores.find((store) =>
                              store.storeKeepers.find(
                                (storeKeeper) =>
                                  storeKeeper.id === dispatch.keeperId
                              )
                            )?.name) &&
                          dispatch.dispatched ? (
                          <Button
                            rightIcon={<MdOutlineMoveUp />}
                            colorScheme="teal"
                            variant="outline"
                            my={1}
                            size="xs"
                            onClick={async () => {
                              const received = await receiveDispatch({
                                inventoryId: dispatch.id,
                              });
                              if (
                                received.data?.changeInventoryReceivedStatus
                                  .error
                              ) {
                                return toast({
                                  title:
                                    received.data?.changeInventoryReceivedStatus
                                      .error.message,
                                  status: "error",
                                  isClosable: true,
                                });
                              }
                              await reGetDispatches();
                              return toast({
                                title: `Dispatch received successfully!`,
                                variant: "left-accent",
                                status: "success",
                                isClosable: true,
                              });
                            }}
                          >
                            Receive
                          </Button>
                        ) : (
                          <Badge variant="solid" colorScheme="yellow">
                            Pending reception
                          </Badge>
                        )}
                      </Td>
                    </Tr>
                  );
                })}
              </Tbody>
            </Table>
          )}
        </Flex>
      </Flex>
    </Flex>
  );
};

export default Dispatch;
