FROM node:14.15.4-alpine3.12

ARG PACKAGE_PATH=./
ARG WORKING_DIR=/usr/api

WORKDIR ${WORKING_DIR}

# Copy package files first
COPY ${PACKAGE_PATH}/package.json ${PACKAGE_PATH}/package-lock.json* ./

# Install production dependencies only
RUN npm ci --quiet --only=production || npm install --only=production

# Copy dist directory containing compiled JS
COPY ${PACKAGE_PATH}/dist ./dist

# Create certificates directory
RUN mkdir -p certificates

EXPOSE 4000 443

CMD ["npm", "start"]







