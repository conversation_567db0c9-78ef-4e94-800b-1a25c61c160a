// Chakra imports
import { useEffect, useState } from "react";
// Chakra imports
import {
  Flex,
  Text,
  useColorModeValue,
  Icon,
  TableContainer,
  Table,
  Thead,
  Tr,
  Th,
  Tbody,
  Td,
  GridItem,
  Grid,
  TableCaption,
  useToast,
} from "@chakra-ui/react";
import { useTable, useExpanded } from "react-table";
import {
  ArrowDownRightCircleFillIcon,
  ArrowRightCircleFillIcon,
} from "../../components/Icons/Icons";
import { ActionMeta, AsyncSelect, Select } from "chakra-react-select";
import {
  useGetCategoriesQuery,
  useGetRoleQuery,
  useGetUserQuery,
  useGetUsersQuery,
  useAddPermissionMutation,
} from "../../generated/graphql";
import { TableRadioGroup } from "../../components/Card/RadioCardGroup";

interface OptionsPermission {
  value: string;
  label: string;
}

const AddPermission = () => {
  const toast = useToast({
    position: "top",
  });
  const [option, setOption] = useState("");
  const [role, setRole] = useState("");
  const [user, setUser] = useState(0);
  const [permissionState, setPermissionState] = useState<any[]>([]);
  // const [permissions, setPermissions] = useState<string[]>([]);
  const [{ data: people, fetching }, getUsersAsync] = useGetUsersQuery();
  const [{ data: fetchedRole }, refetchingRole] = useGetRoleQuery({
    variables: { name: role },
  });
  const [{ data: person }, refetchingUser] = useGetUserQuery({
    variables: { id: user },
  });
  const [, AddPermission] = useAddPermissionMutation();

  // const [{ data: perms, fetching: loading }] = useGetPermissionsQuery();

  const [{ data: cats, fetching: loading }] = useGetCategoriesQuery({
    variables: { type: "user permissions" },
  });

  // Chakra color mode
  // const titleColor = useColorModeValue("gray.700", "blue.500");
  // const colorIcons = useColorModeValue("gray.700", "white");
  // const bgIcons = useColorModeValue("trasnparent", "navy.700");
  // const bgIconsHover = useColorModeValue("gray.50", "whiteAlpha.100");

  const columns = [
    {
      // Build our expander column
      id: "expander", // Make sure it has an ID
      Header: ({ getToggleAllRowsExpandedProps, isAllRowsExpanded }: any) => (
        <span {...getToggleAllRowsExpandedProps()}>
          {isAllRowsExpanded ? (
            <Icon as={ArrowDownRightCircleFillIcon} fontSize={25} />
          ) : (
            <Icon as={ArrowRightCircleFillIcon} fontSize={25} />
          )}
        </span>
      ),
      Cell: ({ row }: any) =>
        // Use the row.canExpand and row.getToggleRowExpandedProps prop getter
        // to build the toggle for expanding a row
        row.canExpand ? (
          <span
            {...row.getToggleRowExpandedProps({
              style: {
                // We can even use the row.depth property
                // and paddingLeft to indicate the depth
                // of the row
                paddingLeft: `${row.depth * 2}rem`,
              },
            })}
          >
            {row.isExpanded ? (
              <Icon as={ArrowDownRightCircleFillIcon} fontSize={25} />
            ) : (
              <Icon as={ArrowRightCircleFillIcon} fontSize={25} />
            )}
          </span>
        ) : null,
    },
    {
      Header: "MODULE/SUB",
      accessor: "module",
    },
    {
      Header: "ACTIONS",
      accessor: "permission",
    },
  ];

  const changeState: any = async (newPermission: string, module: string) => {
    if ((role && role !== "") || (user && user !== 0)) {
      if (role && role !== "") {
        const { data } = await AddPermission({
          name: module + ">" + newPermission,
          roleId: fetchedRole?.getRole?.id,
        });
        if (data?.addPermission.error) {
          return toast({
            title: "Permission change failed!",
            variant: "left-accent",
            status: "error",
            isClosable: true,
          });
        }
      } else if (user && user !== 0) {
        const { data } = await AddPermission({
          name: module + ">" + newPermission,
          userId: person?.getUser?.id,
        });
        if (data?.addPermission.error) {
          return toast({
            title: "Permission change failed!",
            variant: "left-accent",
            status: "error",
            isClosable: true,
          });
        }
      }
      // setPermissions((oldPerms) => {
      //   let perms: string[] = [];
      //   let index = oldPerms.findIndex((p) => p.match(module + ">"));
      //   if (index === 0 || index > 0) {
      //     oldPerms.forEach((op, i) => {
      //       if (i === index) {
      //         return perms.push(module + ">" + newPermission);
      //       } else return perms.push(op);
      //     });
      //   } else {
      //     perms = oldPerms;
      //     perms.push(module + ">" + newPermission);
      //   }
      //   if (cats)
      //     setPermissionState(
      //       convertPermissionsIntoUIArray(cats.getCategories, perms)
      //     );
      //   return perms;
      // });
      return toast({
        title: "Permission set successful!",
        variant: "left-accent",
        status: "success",
        isClosable: true,
      });
    } else
      return toast({
        title: "Permission change needs user or role set!",
        variant: "left-accent",
        status: "warning",
        isClosable: true,
      });
  };

  useEffect(() => {
    if (cats) {
      if (fetchedRole && role) {
        let perms: any[] = [];
        if (
          fetchedRole.getRole?.permissions &&
          fetchedRole.getRole?.permissions.length > 0
        ) {
          perms = fetchedRole.getRole?.permissions.map((perm) => {
            return perm.name;
          });
          // setPermissions(perms);
        }
        setPermissionState(
          convertPermissionsIntoUIArray(cats.getCategories, perms)
        );
      } else if (person && user > 0) {
        let perms: any[] = [];
        if (
          person.getUser?.permissions &&
          person.getUser?.permissions.length > 0
        ) {
          perms = person.getUser?.permissions.map((perm) => {
            return perm.name;
          });
          // setPermissions(perms);
        }
        setPermissionState(
          convertPermissionsIntoUIArray(cats.getCategories, perms)
        );
      } else {
        setPermissionState(
          convertPermissionsIntoUIArray(cats.getCategories, [])
        );
      }
    } else setPermissionState([]);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [cats, fetchedRole, person]);

  const textColor = useColorModeValue("gray.700", "white");

  const fetchUsers: any = async (inputValue: string) => {
    console.log("input value: ", inputValue);
    if (!fetching && people?.getUsers) {
      console.log("users: ", people);
      if (inputValue)
        return people.getUsers
          .filter(
            (t) =>
              (t.firstname + " " + t.lastname + " " + t.middlename)
                .toLowerCase()
                .match(inputValue.toLowerCase()) !== null
          )
          .map((t) => {
            console.log("we got: ", t);
            return {
              label: t.firstname + " " + t.middlename + " " + t.lastname,
              value: t.id,
            };
          });
      else if (
        inputValue === "" ||
        inputValue === undefined ||
        inputValue === null ||
        inputValue === " "
      )
        return people.getUsers.map((t) => {
          return {
            label: t.firstname + " " + t.middlename + " " + t.lastname,
            value: t.id,
          };
        });
    } else await getUsersAsync({ requestPolicy: "network-only" });

    if (people)
      return people?.getUsers.map((t) => {
        return {
          label: t.firstname + " " + t.middlename + " " + t.lastname,
          value: t.id,
        };
      });
  };

  const handleChange: any = async (
    newValue: OptionsPermission,
    actionMeta: ActionMeta<{ label: string; value: number }>
  ) => {
    if (actionMeta.action !== "clear") {
      if (actionMeta.name === "option") {
        if (newValue) setOption(newValue.value);
        else {
          setOption("");
          setRole("");
          setUser(0);
        }
      } else if (actionMeta.name === "roles") {
        setUser(0);
        setRole(newValue.value);
        await refetchingRole();
      } else if (actionMeta.name === "users") {
        setRole("");
        setUser(Number(newValue.value));
        await refetchingUser();
      }
    } else {
      setRole("");
      setUser(0);
    }
  };

  return (
    <Flex direction="column" pt={{ base: "120px", md: "75px" }}>
      <Grid templateColumns="repeat(5, 1fr)" templateRows="1fr" columnGap={5}>
        <GridItem rowSpan={2} colSpan={1}>
          <Text fontSize="lg" color={textColor} fontWeight="bold">
            Permissions for
          </Text>
        </GridItem>

        <GridItem rowSpan={2} colSpan={2}>
          <Select
            variant="flushed"
            isClearable
            // ml={3}
            //   width={100}
            options={[
              {
                label: "User",
                value: "user",
              },
              {
                label: "Role",
                value: "role",
              },
            ]}
            name="option"
            placeholder="Assign permissions to"
            closeMenuOnSelect={true}
            onChange={handleChange}
            hideSelectedOptions={true}
            // isMulti={true}
          />
        </GridItem>

        <GridItem rowSpan={2} colSpan={2}>
          {option === "" ? null : option === "role" ? (
            <Select
              variant="flushed"
              isClearable
              // ml={3}
              //   width={100}
              options={[
                {
                  label: "Admin",
                  value: "admin",
                },
                {
                  label: "Employee (disabled)",
                  value: "employee",
                },
                {
                  label: "Owner (disabled)",
                  value: "owner",
                },
                {
                  label: "Client (disabled)",
                  value: "client",
                },
              ]}
              name="roles"
              placeholder="Role"
              closeMenuOnSelect={true}
              onChange={handleChange}
              hideSelectedOptions={true}
              // isMulti={true}
            />
          ) : option === "user" ? (
            <AsyncSelect
              variant="flushed"
              isClearable
              // ml={3}
              // width={100}
              name="users"
              defaultOptions={true}
              cacheOptions={false}
              isLoading={fetching}
              loadOptions={fetchUsers}
              placeholder="Select user"
              closeMenuOnSelect={true}
              onChange={handleChange}
              escapeClearsValue={true}
              hideSelectedOptions={true}
              // isMulti={true}
            />
          ) : null}
        </GridItem>
        <TableContainer as={GridItem} colSpan={5} mt={10}>
          {!loading && (role || user > 0) && (
            <PermissionTable
              columns={columns}
              data={permissionState}
              changePermissionState={changeState}
            />
          )}
        </TableContainer>
      </Grid>
    </Flex>
  );
};

export default AddPermission;

function PermissionTable({
  columns: permissionColumns,
  data,
  changePermissionState,
}: any) {
  const { getTableProps, getTableBodyProps, headerGroups, rows, prepareRow } =
    useTable(
      {
        autoResetHiddenColumns: false,
        columns: permissionColumns,
        data,
      },
      useExpanded // Use the useExpanded plugin hook
    );

  return (
    <Table colorScheme="cyan" {...getTableProps()}>
      <TableCaption>User or Role Permissions setup.</TableCaption>
      <Thead>
        {headerGroups.map((headerGroup) => (
          <Tr {...headerGroup.getHeaderGroupProps()}>
            {headerGroup.headers.map((column) => (
              <Th {...column.getHeaderProps()}>{column.render("Header")}</Th>
            ))}
          </Tr>
        ))}
      </Thead>
      <Tbody {...getTableBodyProps()}>
        {" "}
        {rows.map((row) => {
          prepareRow(row);
          return (
            <Tr
              {...row.getRowProps()}
              bgColor={
                row.id.split(".").length === 2
                  ? "blue.500"
                  : row.id.split(".").length === 3
                  ? "cyan.600"
                  : row.id.split(".").length === 4
                  ? "teal"
                  : "none"
              }
            >
              {row.cells.map((cell) => {
                return (
                  <Td {...cell.getCellProps()}>
                    {[
                      "none",
                      "view",
                      "read",
                      "create",
                      "edit",
                      "delete",
                    ].includes(cell.value) ? (
                      <TableRadioGroup
                        value={cell.value}
                        options={[
                          "none",
                          "view",
                          "read",
                          "create",
                          "edit",
                          "delete",
                        ]}
                        onValueChange={(value) =>
                          changePermissionState(value, row.cells[1].value)
                        }
                      />
                    ) : (
                      cell.render("Cell")
                    )}
                  </Td>
                );
              })}
            </Tr>
          );
        })}
      </Tbody>
    </Table>
  );
}

function convertPermissionsIntoUIArray(arr: any[], perms: any[]) {
  let result: any[] = [];

  arr.forEach((item: { name: string }) => {
    const modules = item.name.split(">")[0].split("::");

    let current = result;
    let currentMainModule = "";
    let currentMainModuleIndex = -1;
    let currentSL1 = "";
    let currentSL1Index = -1;
    let currentSL2 = "";
    let currentSL2Index = -1;

    modules.forEach((module: string, index: number) => {
      if (index === 0) {
        const existingModule = current.find((m) => m.module === module);
        if (!existingModule) {
          const newModule = {
            module: module,
            permission: "none",
            subRows: [],
          };
          current.push(newModule);
        }
        currentMainModule = module;
      } else if (index === 1) {
        currentMainModuleIndex = current.findIndex(
          (c) => c.module === currentMainModule
        );
        const existingL1SubModule = current[
          currentMainModuleIndex
        ].subRows.find((m: { module: any }) => m.module === module);
        if (!existingL1SubModule) {
          const newModule = {
            module: module,
            permission: "none",
            subRows: [],
          };
          current[currentMainModuleIndex].subRows.push(newModule);
        }
        currentSL1 = module;
      } else if (index === 2) {
        currentSL1Index = current[currentMainModuleIndex].subRows.findIndex(
          (c: { module: string }) => c.module === currentSL1
        );
        const existingL2SubModule = current[currentMainModuleIndex].subRows[
          currentSL1Index
        ].subRows.find((m: { module: any }) => m.module === module);
        if (!existingL2SubModule) {
          const newModule = {
            module: module,
            permission: "none",
            subRows: [],
          };
          current[currentMainModuleIndex].subRows[currentSL1Index].subRows.push(
            newModule
          );
        }
        currentSL2 = module;
      } else if (index === 3) {
        currentSL2Index = current[currentMainModuleIndex].subRows[
          currentSL1Index
        ].subRows.findIndex((c: { module: string }) => c.module === currentSL2);
        const existingL3SubModule = current[currentMainModuleIndex].subRows[
          currentSL1Index
        ].subRows[currentSL2Index].subRows.find(
          (m: { module: any }) => m.module === module
        );
        if (!existingL3SubModule) {
          const newModule = {
            module: module,
            permission: "none",
            subRows: [],
          };
          current[currentMainModuleIndex].subRows[currentSL1Index].subRows[
            currentSL2Index
          ].subRows.push(newModule);
        }
      }
    });
    result = current;
  });

  perms.forEach((p) => {
    let pid = result.findIndex((r) => {
      return r.module === p.split(">")[0];
    });
    if (pid >= 0) result[pid].permission = p.split(">")[1];
    else {
      result.forEach((res1, i1) => {
        if (res1.subRows.length > 0) {
          let pid1 = res1.subRows.findIndex(
            (s1: { module: string; permission: string; subRows: any[] }) => {
              return s1.module === p.split(">")[0];
            }
          );
          if (pid1 >= 0) result[i1].subRows[pid1].permission = p.split(">")[1];
          else {
            res1.subRows.forEach((res2: { subRows: any[] }, i2: any) => {
              if (res2.subRows.length > 0) {
                let pid2 = res2.subRows.findIndex(
                  (s2: {
                    module: string;
                    permission: string;
                    subRows: any[];
                  }) => {
                    return s2.module === p.split(">")[0];
                  }
                );
                if (pid2 >= 0)
                  result[i1].subRows[i2].subRows[pid2].permission =
                    p.split(">")[1];
                else {
                  res2.subRows.forEach((res3: { subRows: any[] }, i3: any) => {
                    if (res3.subRows.length > 0) {
                      let pid3 = res3.subRows.findIndex(
                        (s3: {
                          module: string;
                          permission: string;
                          subRows: any[];
                        }) => {
                          return s3.module === p.split(">")[0];
                        }
                      );
                      if (pid3 >= 0)
                        result[i1].subRows[i2].subRows[i3].subRows[
                          pid3
                        ].permission = p.split(">")[1];
                    }
                  });
                }
              }
            });
          }
        }
      });
    }
  });

  return result;
}
