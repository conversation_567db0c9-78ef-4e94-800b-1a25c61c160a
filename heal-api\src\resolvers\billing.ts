import { isAuth } from "../middleware/isAuth";
import { Arg, Mutation, Resolver, UseMiddleware, Ctx } from "type-graphql";
import { BooleanResponse } from "./user";
import { getConnection } from "typeorm";
import { Bill } from "../entities/Bill";
import { MyContext } from "../types";
import { Inventory } from "../entities/Inventory/Inventory";
import { Employee } from "../entities/Employee";
import { User } from "../entities/User";
import { logError } from "../utils/utils";

@Resolver(Bill)
export class BillResolver {
  // clear bill
  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async clearBill(
    @Arg("saleId") saleId: string,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const userId = req.session.userId;
    const companyId = req.session.companyId;

    try {
      await getConnection().transaction(async (transactionalEntityManager) => {
        const employee = await transactionalEntityManager.findOne(Employee, {
          where: { userId, companyId: req.session.companyId },
        });
        const admin = await transactionalEntityManager.findOne(User, {
          where: { id: userId, companyId: req.session.companyId },
          relations: ["role"],
        });

        if (!employee && admin?.role.name !== "admin") {
          throw new Error("Employee not found or not authorized.");
        }
        const inventory = await transactionalEntityManager.findOne(Inventory, {
          where: { id: saleId, companyId: req.session.companyId, type: "sale" },
          relations: ["keeper", "bill"],
        });

        if (!inventory) {
          throw new Error("Sale not found.");
        }

        // Check if the user is authorized to change the dispatched status
        const isAdmin = req.session.role === "admin";
        const isApprover =
          inventory.keeper &&
          inventory.keeper.storeId === inventory.sourceStore.id; // changed to proper approver uppon implementing permissions
        if (!isAdmin && !isApprover) {
          throw new Error("Not authorized to change the dispatched status.");
        }

        // Change the granted (approved) status to true
        inventory.granted = true;
        inventory.approverId = employee ? employee.id : admin!.id;
        await transactionalEntityManager.save(inventory);
        const bill = await transactionalEntityManager.findOne(Bill, {
          where: { id: inventory.bill.id, companyId: req.session.companyId },
        });
        if (!bill) {
          throw new Error("No bill created for this transaction.");
        }
        bill.cleared = true;
        await transactionalEntityManager.save(bill);
      });

      return {
        status: true,
      };
    } catch (err) {
      console.error(err.message);

      // Fire and forget error logging
      logError(
        companyId,
        err.message,
        "BILL_CLEAR_ERROR",
        JSON.stringify(err),
        "high",
        `Clear bill attempt for saleId: ${saleId}`,
        userId
      );

      return {
        status: false,
        error: {
          target: "general",
          message: "Something went wrong, try again!",
        },
      };
    }
  }
}
