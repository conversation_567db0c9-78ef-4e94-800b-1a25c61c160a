import { Field, ObjectType } from "type-graphql";
import { Column, Entity } from "typeorm";
import { AuditBaseEntity } from "./AuditEntity";

@ObjectType()
@Entity()
export class ErrorLog extends AuditBaseEntity {
  @Field(() => String)
  @Column({ type: "text" })
  errorMessage!: string;

  @Field(() => String)
  @Column({ type: "text" })
  errorCode!: string;

  @Field(() => String)
  @Column({ type: "text" })
  errorStackTrace: string;

  @Field(() => String)
  @Column({ type: "text" })
  severity: "low" | "medium" | "high" | "critical";

  @Field(() => String, { nullable: true })
  @Column({ type: "uuid", nullable: true })
  userId?: string;

  @Field(() => String)
  @Column({ type: "text" })
  action: string;

  @Field(() => Boolean)
  @Column({ type: "boolean", default: false })
  resolved: boolean;
}
