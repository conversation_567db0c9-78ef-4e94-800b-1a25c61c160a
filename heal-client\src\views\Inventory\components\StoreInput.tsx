import React from "react";
import AsyncSelect from "react-select/async";
import { useGetStoresQuery } from "../../../generated/graphql";

interface StoreInputProps {
  onChange: (value: any) => void;
}

const StoreInput: React.FC<StoreInputProps> = ({ onChange }) => {
  const [{ data: stores, fetching: fetchingStores }] = useGetStoresQuery();

  // Load stores to the input
  const loadStores = async (searchInput: string) => {
    if (searchInput.length > 0) {
      if (!fetchingStores && stores) {
        return stores.getStores
          .filter((store) =>
            store.name
              .toLocaleLowerCase()
              .includes(searchInput.toLocaleLowerCase())
          )
          .map((s) => ({
            label: s.name,
            value: s.id,
          }));
      }
    } else {
      if (!fetchingStores && stores) {
        return stores.getStores.map((s) => ({
          label: s.name,
          value: s.id,
        }));
      }
    }
    return [];
  };

  return (
    <AsyncSelect
      variant="flushed"
      isClearable
      isSearchable={true}
      isLoading={fetchingStores}
      defaultOptions
      loadOptions={loadStores}
      size="sm"
      colorScheme="navy"
      placeholder="Select store"
      closeMenuOnSelect={true}
      onChange={onChange}
      escapeClearsValue={true}
      hideSelectedOptions={true}
      styles={{
        container: (base) => ({
          ...base,
          width: "100%",
          color: "navy",
        }),
      }}
    />
  );
};

export default StoreInput;
