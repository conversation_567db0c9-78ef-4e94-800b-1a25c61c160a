import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import {
  Flex,
  Button,
  FormControl,
  FormLabel,
  Input,
  Text,
  useColorModeValue,
  FormErrorMessage,
  useToast,
  Grid,
  GridItem,
  Select,
  Box,
  Icon,
} from "@chakra-ui/react";
import { useHistory, useLocation } from "react-router-dom";
import { FaClinicMedical } from "react-icons/fa";
import {
  Clinic,
  useGetEmployeesQuery,
  useEditClinicMutation,
} from "../../generated/graphql";
import { HSeparator } from "../../components/Separator/Separator";

interface IClinicInput {
  name: string;
  description: string;
  clinicType: string;
  status: string;
  size: number;
  leaderId: string | null;
}

interface LocationState {
  clinic: Clinic;
}

const EditClinic: React.FC = () => {
  const toast = useToast({
    position: "top",
  });
  // absorb the state from router and leave if the page comes from non assigned route
  const location = useLocation<{ clinic: Clinic }>();
  const history = useHistory();
  if (!location.state) history.push("departments");
  const locationState = location.state as LocationState;
  const [clinic, setClinic] = useState<Clinic | null>(
    locationState && locationState.clinic
  );

  if (!clinic || !clinic.id || clinic.id <= 0) history.push("departments");

  useEffect(() => {
    if (!locationState.clinic) history.push("departments");
    setClinic(locationState.clinic);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Fetch the list of employees for leader selection
  const [{ data: employeesData }] = useGetEmployeesQuery();

  // Mutation to update the clinic
  const [, updateClinic] = useEditClinicMutation();

  const [error, setError] = useState("");
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<IClinicInput>({
    defaultValues: {
      name: locationState.clinic.name,
      description: locationState.clinic.description,
      clinicType: locationState.clinic.clinicType,
      status: locationState.clinic.status,
      size: locationState.clinic.size,
      leaderId: locationState.clinic.leaderId?.toString() || null,
    },
  });

  const onSubmit = async (values: IClinicInput) => {
    setError("");
    const params = {
      name: values.name,
      description: values.description,
      clinicType: values.clinicType,
      status: values.status,
      size: values.size,
      leaderId: values.leaderId ? parseInt(values.leaderId) : null,
    };

    const { data } = await updateClinic({ id: clinic!.id, params });

    if (data?.editClinic.status) {
      toast({
        title: "Clinic updated successfully!",
        variant: "left-accent",
        status: "success",
        isClosable: true,
      });
      reset();
      history.push({
        pathname: "view-clinic",
        state: {
          clinic: data.editClinic.clinic,
        },
      });
    } else {
      setError(data?.editClinic.error?.message || "An error occurred.");
      toast({
        title: "Error updating clinic",
        description: data?.editClinic.error?.message,
        variant: "left-accent",
        status: "error",
        isClosable: true,
      });
    }
  };

  const textColor = useColorModeValue("gray.700", "white");
  const bgForm = useColorModeValue("white", "navy.800");
  const borderColor = useColorModeValue("gray.200", "gray.600");
  const bgColor = useColorModeValue("white", "gray.700");

  return (
    <Flex
      w="100%"
      h="100%"
      alignItems="center"
      justifyContent="center"
      mb="60px"
      mt={{ base: "50px", md: "50px" }}
    >
      <Flex
        zIndex="2"
        direction="column"
        w="80%"
        background="transparent"
        borderRadius="15px"
        p="40px"
        mx={{ base: "100px" }}
        m={{ base: "20px", md: "auto" }}
        bg={bgForm}
        boxShadow={useColorModeValue(
          "0px 5px 14px rgba(0, 0, 0, 0.05)",
          "unset"
        )}
      >
        {clinic && (
          <Box
            p={5}
            bg={bgColor}
            borderRadius="lg"
            boxShadow="md"
            border="1px solid"
            borderColor={borderColor}
            mb={5}
          >
            <Grid templateColumns="repeat(3, 1fr)" gap={6}>
              <GridItem>
                <Text fontSize="2xl" fontWeight="bold" color={textColor}>
                  <Icon as={FaClinicMedical} mr={2} />
                  Clinic Details
                </Text>
              </GridItem>
              <GridItem>
                <Text fontSize="lg" fontWeight="bold" color={textColor}>
                  <Box as="span" minWidth="120px" display="inline-block">
                    Clinic Name:
                  </Box>
                  {clinic.name}
                </Text>
                <Text fontSize="md" color={textColor}>
                  <Box as="span" minWidth="120px" display="inline-block">
                    Clinic Description:
                  </Box>
                  {clinic.description}
                </Text>
              </GridItem>
            </Grid>
          </Box>
        )}
        <Text
          fontSize="xl"
          color={textColor}
          fontWeight="bold"
          textAlign="center"
          mb="12px"
        >
          Edit Clinic
        </Text>
        <HSeparator mt="3px" mb="3px" />
        <Text textAlign="center" mt="0px" mb="0px">
          Clinic Details:
        </Text>
        <HSeparator mb="3px" mt="3px" />
        {error && (
          <Text pb="15px" m={"auto"} color="red.500" textColor="red.300">
            {error}
          </Text>
        )}
        <form onSubmit={handleSubmit(onSubmit)}>
          <Grid templateColumns="repeat(6, 1fr)" gap={6}>
            {/* Clinic Name */}
            <FormControl
              as={GridItem}
              colSpan={6}
              isInvalid={errors.name as any}
            >
              <FormLabel htmlFor="name" fontSize="xs">
                Clinic Name
              </FormLabel>
              <Input
                variant="filled"
                type="text"
                fontSize="xs"
                placeholder="Clinic Name"
                size="lg"
                id="name"
                {...register("name", {
                  required: "Clinic name is required",
                })}
              />
              <FormErrorMessage fontSize="xs">
                {errors.name && (errors.name.message as any)}
              </FormErrorMessage>
            </FormControl>

            {/* Description */}
            <FormControl
              as={GridItem}
              colSpan={6}
              isInvalid={errors.description as any}
            >
              <FormLabel htmlFor="description" fontSize="xs">
                Clinic Description
              </FormLabel>
              <Input
                variant="filled"
                type="text"
                fontSize="xs"
                placeholder="Clinic Description"
                size="lg"
                id="description"
                {...register("description", {
                  required: "Description is required",
                })}
              />
              <FormErrorMessage fontSize="xs">
                {errors.description && (errors.description.message as any)}
              </FormErrorMessage>
            </FormControl>

            {/* Clinic Type */}
            <FormControl
              as={GridItem}
              colSpan={6}
              isInvalid={errors.clinicType as any}
            >
              <FormLabel htmlFor="clinicType" fontSize="xs">
                Clinic Type
              </FormLabel>
              <Select
                id="clinicType"
                variant="filled"
                {...register("clinicType", {
                  required: "Clinic Type is required",
                })}
              >
                <option value="general">General</option>
                <option value="specialized">Specialized</option>
              </Select>
              <FormErrorMessage fontSize="xs">
                {errors.clinicType && (errors.clinicType.message as any)}
              </FormErrorMessage>
            </FormControl>

            {/* Status */}
            <FormControl
              as={GridItem}
              colSpan={6}
              isInvalid={errors.status as any}
            >
              <FormLabel htmlFor="status" fontSize="xs">
                Clinic Status
              </FormLabel>
              <Select
                id="status"
                variant="filled"
                {...register("status", {
                  required: "Status is required",
                })}
              >
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </Select>
              <FormErrorMessage fontSize="xs">
                {errors.status && (errors.status.message as any)}
              </FormErrorMessage>
            </FormControl>

            {/* Clinic Size */}
            <FormControl
              as={GridItem}
              colSpan={6}
              isInvalid={errors.size as any}
            >
              <FormLabel htmlFor="size" fontSize="xs">
                Clinic Size
              </FormLabel>
              <Input
                variant="filled"
                type="number"
                fontSize="xs"
                placeholder="Clinic Size"
                size="lg"
                id="size"
                {...register("size", {
                  required: "Clinic size is required",
                  valueAsNumber: true,
                })}
              />
              <FormErrorMessage fontSize="xs">
                {errors.size && (errors.size.message as any)}
              </FormErrorMessage>
            </FormControl>

            {/* Leader (Optional) */}
            <FormControl
              as={GridItem}
              colSpan={6}
              isInvalid={errors.leaderId as any}
            >
              <FormLabel htmlFor="leaderId" fontSize="xs">
                Clinic Leader (Optional)
              </FormLabel>
              <Select
                id="leaderId"
                variant="filled"
                placeholder="Select Clinic Leader"
                {...register("leaderId")}
              >
                {employeesData?.getEmployees
                  .filter((emp) => emp.role.name === "employee")
                  .map((employee) => (
                    <option key={employee.id} value={employee.employee?.id}>
                      {employee.firstname} {employee.lastname}
                    </option>
                  ))}
              </Select>
              <FormErrorMessage fontSize="xs">
                {errors.leaderId && (errors.leaderId.message as any)}
              </FormErrorMessage>
            </FormControl>
          </Grid>

          <Button
            mt="6"
            colorScheme="teal"
            isLoading={isSubmitting}
            type="submit"
            w="full"
          >
            Update Clinic
          </Button>
        </form>
      </Flex>
    </Flex>
  );
};

export default EditClinic;
