import { Field, ObjectType, registerEnumType } from "type-graphql";
import { Column, <PERSON>tity, Join<PERSON><PERSON>um<PERSON>, ManyToOne } from "typeorm";
import { AuditBaseEntity } from "./AuditEntity";
import { Company } from "./Company";

enum PaymentStatus {
  ACTIVE = "active",
  CANCELED = "canceled",
  EXPIRED = "expired",
  PENDING = "pending",
  TRIAL = "trial",
}

registerEnumType(PaymentStatus, {
  name: "PaymentStatus",
  description: "Status of a payment",
});

enum BillingCycle {
  MONTHLY = "monthly",
  QUARTERLY = "quarterly",
  ANNUALLY = "annually",
}

registerEnumType(BillingCycle, {
  name: "BillingCycle",
  description: "Billing cycle for payment",
});

@ObjectType()
@Entity()
export class Payment extends AuditBaseEntity {
  @Field(() => String)
  @Column({ type: "text", default: "basic" })
  packageName: string;

  @Field(() => PaymentStatus)
  @Column({
    type: "enum",
    enum: PaymentStatus,
    default: PaymentStatus.PENDING,
  })
  status: PaymentStatus;

  @Field(() => BillingCycle)
  @Column({
    type: "enum",
    enum: BillingCycle,
    default: BillingCycle.MONTHLY,
  })
  billingCycle: BillingCycle;

  @Field(() => String)
  @Column({ type: "timestamp" })
  startDate: Date;

  @Field(() => String)
  @Column({ type: "timestamp" })
  endDate: Date;

  @Field()
  @Column({ type: "float" })
  amount: number;

  @Field(() => String, { nullable: true })
  @Column({ type: "text", nullable: true })
  paymentReference: string;

  @Field(() => Boolean)
  @Column({ type: "boolean", default: false })
  autoRenew: boolean;

  @Field(() => Number, { nullable: true })
  @Column({ type: "int", nullable: true })
  maxUsers: number;

  @Field(() => String, { nullable: true })
  @Column({ type: "text", nullable: true })
  features: string;

  @Field(() => String, { nullable: true })
  @Column({ type: "timestamp", nullable: true })
  lastPaymentDate: Date;

  @Field(() => String, { nullable: true })
  @Column({ type: "text", nullable: true })
  cancellationReason: string;

  @Field(() => Company)
  @ManyToOne(() => Company, (company) => company.payments)
  @JoinColumn({ name: "companyId" })
  company: Company;
}
