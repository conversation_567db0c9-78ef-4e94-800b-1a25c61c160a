import { MigrationInterface, QueryRunner } from "typeorm";

export class FixRoleUserRelationship1234567890124
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // First drop the dependent foreign key constraints
    await queryRunner.query(`
      ALTER TABLE "permission_roles_role" 
      DROP CONSTRAINT IF EXISTS "FK_9f44b6228b173c7b9dfb8c66003";
    `);

    await queryRunner.query(`
      ALTER TABLE "permission_users_user" 
      DROP CONSTRAINT IF EXISTS "FK_a68535c371c96a600abe56090b7";
    `);

    // Now we can safely modify the permission table
    await queryRunner.query(`
      ALTER TABLE "permission"
      ADD COLUMN IF NOT EXISTS "companyId" integer;
    `);

    // Add unique constraint on permission (id, companyId)
    await queryRunner.query(`
      ALTER TABLE "permission"
      ADD CONSTRAINT "permission_id_company_id_unique" UNIQUE ("id", "companyId");
    `);

    // Recreate the foreign key constraints with the new composite key
    await queryRunner.query(`
      ALTER TABLE "permission_roles_role"
      ADD CONSTRAINT "FK_permission_roles_permission" 
      FOREIGN KEY ("permissionId", "companyId") 
      REFERENCES "permission"("id", "companyId");
    `);

    await queryRunner.query(`
      ALTER TABLE "permission_users_user"
      ADD CONSTRAINT "FK_permission_users_permission" 
      FOREIGN KEY ("permissionId", "companyId") 
      REFERENCES "permission"("id", "companyId");
    `);

    // Add unique constraint on role (id, companyId)
    await queryRunner.query(`
      ALTER TABLE "role"
      ADD CONSTRAINT "role_id_company_id_unique" UNIQUE ("id", "companyId");
    `);

    // Update role-user relationship
    await queryRunner.query(`
      ALTER TABLE "user" DROP CONSTRAINT IF EXISTS "user_role_id_fkey";
    `);

    await queryRunner.query(`
      ALTER TABLE "user"
      ADD CONSTRAINT "user_role_company_fkey"
      FOREIGN KEY ("roleId", "companyId")
      REFERENCES "role" ("id", "companyId");
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop all new constraints in reverse order
    await queryRunner.query(`
      ALTER TABLE "user" DROP CONSTRAINT IF EXISTS "user_role_company_fkey";
    `);

    await queryRunner.query(`
      ALTER TABLE "permission_users_user" 
      DROP CONSTRAINT IF EXISTS "FK_permission_users_permission";
    `);

    await queryRunner.query(`
      ALTER TABLE "permission_roles_role" 
      DROP CONSTRAINT IF EXISTS "FK_permission_roles_permission";
    `);

    await queryRunner.query(`
      ALTER TABLE "permission" 
      DROP CONSTRAINT IF EXISTS "permission_id_company_id_unique";
    `);

    await queryRunner.query(`
      ALTER TABLE "role" 
      DROP CONSTRAINT IF EXISTS "role_id_company_id_unique";
    `);

    // Restore original foreign keys
    await queryRunner.query(`
      ALTER TABLE "user"
      ADD CONSTRAINT "user_role_id_fkey"
      FOREIGN KEY ("roleId")
      REFERENCES "role" ("id");
    `);

    await queryRunner.query(`
      ALTER TABLE "permission_roles_role"
      ADD CONSTRAINT "FK_9f44b6228b173c7b9dfb8c66003"
      FOREIGN KEY ("permissionId")
      REFERENCES "permission" ("id");
    `);

    await queryRunner.query(`
      ALTER TABLE "permission_users_user"
      ADD CONSTRAINT "FK_a68535c371c96a600abe56090b7"
      FOREIGN KEY ("permissionId")
      REFERENCES "permission" ("id");
    `);
  }
}
