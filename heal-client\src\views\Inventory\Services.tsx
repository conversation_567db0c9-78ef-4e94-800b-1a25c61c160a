// Chakra imports
import {
  Button,
  Flex,
  Icon,
  Table,
  Tbody,
  Text,
  Th,
  Thead,
  Tr,
  // useColorMode,
  useColorModeValue,
  useToast,
} from "@chakra-ui/react";
// Custom components
import Card from "../../components/Card/Card";
import CardBody from "../../components/Card/CardBody";
import CardHeader from "../../components/Card/CardHeader";
import { FaPlus } from "react-icons/fa";
import { SearchBar } from "../../components/Navbars/SearchBar/SearchBar";
import {
  Item,
  useDeleteItemMutation,
  useGetAllServicesQuery,
} from "../../generated/graphql";
import { Link, useHistory } from "react-router-dom";
import DeleteConfirm from "../../components/DeleteConfirmation";
import { useContext, useState } from "react";
import { MeContext } from "../../components/Wrapper";
import ServicesTableRow from "./components/ServicesTable";

function Services() {
  const [{ data, fetching }, getServicesAsync] = useGetAllServicesQuery({
    requestPolicy: "network-only",
  });
  const me = useContext(MeContext);
  const [openDelete, setOpenDelete] = useState({ open: false, id: -1000000 });
  const history = useHistory();
  const [{ fetching: loadingDelete }, deleteItem] = useDeleteItemMutation();
  // const { colorMode } = useColorMode();
  const borderColor = useColorModeValue("gray.200", "gray.600");
  const textColor = useColorModeValue("gray.700", "white");
  const bgProfile = useColorModeValue("hsla(0,0%,100%,.8)", "navy.800");
  const borderProfileColor = useColorModeValue("white", "transparent");

  const toast = useToast({
    position: "top",
  });

  const callDeleteItem = async (id: number) => {
    const { error } = await deleteItem({ id });
    setOpenDelete({ open: false, id: -1000000 });
    if (error) {
      toast({
        title: "Service delete failed!",
        variant: "left-accent",
        status: "error",
        isClosable: true,
      });
    } else {
      await getServicesAsync({ requestPolicy: "network-only" });
      toast({
        title: "Service deleted successfully!",
        variant: "left-accent",
        status: "success",
        isClosable: true,
      });
    }
  };

  return (
    <Flex direction="column" pt={{ base: "120px", md: "75px" }}>
      <DeleteConfirm
        loading={loadingDelete}
        item="Service"
        open={openDelete.open}
        id={openDelete.id!}
        feedback={callDeleteItem}
        nofeedback={() => setOpenDelete({ open: false, id: -1000000 })}
      />

      <Card overflowX={{ sm: "scroll", xl: "hidden" }} pb="0px">
        <CardHeader p="0px 0px 8px 0px">
          <Flex
            direction={{ sm: "column", md: "row" }}
            mb="12px"
            maxH="330px"
            justifyContent={{ sm: "center", md: "space-between" }}
            align="center"
            backdropFilter="blur(21px)"
            boxShadow="0px 2px 5.5px rgba(0, 0, 0, 0.02)"
            border="1.5px solid"
            borderColor={borderProfileColor}
            bg={bgProfile}
            p="24px"
            borderRadius="20px"
          >
            <Flex
              align="center"
              mb={{ sm: "10px", md: "0px" }}
              direction={{ sm: "column", md: "row" }}
              w={{ sm: "100%" }}
              textAlign={{ sm: "center", md: "start" }}
            >
              <Text>Services</Text>
            </Flex>
            <Flex
              direction={{ sm: "column", lg: "row" }}
              w={{ sm: "100%", md: "50%", lg: "auto" }}
            >
              <SearchBar placeholder="Search services ..." />
              <Link to={`/${me?.role!.name}/manage-service`}>
                <Button px={1} bg="transparent" variant="no-effects">
                  <Flex
                    align="center"
                    w={{ lg: "135px" }}
                    borderRadius="15px"
                    justifyContent="center"
                    py="10px"
                    cursor="pointer"
                  >
                    <Icon
                      as={FaPlus}
                      color={textColor}
                      fontSize="xs"
                      mr="4px"
                    />
                    <Text fontSize="xs" color={textColor} fontWeight="bold">
                      ADD NEW SERVICE
                    </Text>
                  </Flex>
                </Button>
              </Link>
            </Flex>
          </Flex>
        </CardHeader>
        <CardBody>
          <Table variant="simple" color={textColor}>
            <Thead>
              <Tr my=".8rem" pl="0px" color="gray.400">
                <Th pl="0px" borderColor={borderColor} color="gray.400">
                  Name
                </Th>
                <Th pl="0px" borderColor={borderColor} color="gray.400">
                  Details
                </Th>
                <Th pl="0px" borderColor={borderColor} color="gray.400">
                  Reference
                </Th>
                <Th borderColor={borderColor} color="gray.400">
                  Service charge
                </Th>
                <Th borderColor={borderColor}></Th>
              </Tr>
            </Thead>
            <Tbody>
              {!fetching &&
                data?.getAllServices &&
                data?.getAllServices.map((service, index, arr) => (
                  <ServicesTableRow
                    callOnClick={() =>
                      setOpenDelete({ open: true, id: service.id })
                    }
                    role={me?.role!.name ? me?.role.name : "employee"}
                    history={history}
                    isLast={index === arr.length - 1 ? true : false}
                    key={service.id}
                    service={service as Item}
                  />
                ))}
            </Tbody>
          </Table>
          {!fetching && !data?.getAllServices && (
            <Link to={`/${me?.role!.name}/addedititem`}>
              <Button
                left={40}
                m="80px"
                p="80px"
                bg="transparent"
                border="1px solid lightgray"
                borderRadius="15px"
                minHeight={{ sm: "200px", md: "100%" }}
              >
                <Flex direction="column" justifyContent="center" align="center">
                  <Icon
                    as={FaPlus}
                    mb={10}
                    color={textColor}
                    fontSize="lg"
                    mr="12px"
                  />
                  <Text fontSize="lg" color={textColor} fontWeight="bold">
                    No Services In DB, Add One Now
                  </Text>
                </Flex>
              </Button>
            </Link>
          )}
        </CardBody>
      </Card>
    </Flex>
  );
}

export default Services;
