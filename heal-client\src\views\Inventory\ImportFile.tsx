import React, { useContext, useState } from "react";
import {
  <PERSON>ton,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Flex,
  Box,
  useColorModeValue,
} from "@chakra-ui/react";
import { read, utils } from "xlsx";
import { useAddItemsFromExcelMutation } from "../../generated/graphql";
import { formatToMoney } from "../../utils/Helpers";
import { useHistory } from "react-router-dom";
import { MeContext } from "../../components/Wrapper";

const ImportFile: React.FC = () => {
  const [data, setData] = useState<any[]>([]);
  const textColor = useColorModeValue("gray.700", "white");
  const borderColor = useColorModeValue("gray.200", "gray.600");

  const me = useContext(MeContext);

  const history = useHistory();

  const [, addItems] = useAddItemsFromExcelMutation();

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files?.length) {
      const file = files[0];
      const reader = new FileReader();
      reader.onload = (event) => {
        const arrayBuffer = event.target!.result;
        const wb = read(arrayBuffer, { type: "array" });
        const sheets = wb.SheetNames;
        if (sheets.length) {
          const rows = utils.sheet_to_json(wb.Sheets[sheets[0]]);
          console.log("rows from uploaded file: ", rows);
          setData(rows as any[]);
        }
      };
      reader.readAsArrayBuffer(file);
    }
  };

  const handleUpload = async () => {
    const dataForUpload = data.map((dataItem) => {
      return {
        name: dataItem.name as string,
        description: dataItem.description as string,
        type: dataItem.type as string,
        reference: dataItem.reference as string,
        reorder: Number(dataItem.reorder) || 0,
        internal: dataItem.internal as boolean,
        unit: dataItem.unit as string,
        pieceUnit: dataItem.pieceUnit as string,
        subPieceUnit: dataItem.subPieceUnit as string,
        pieces: Number(dataItem.pieces) ? Number(dataItem.pieces) : 0,
        subPieces: Number(dataItem.subPieces) ? Number(dataItem.subPieces) : 0,
        sellingPrice: Number(dataItem.sellingPrice)
          ? Number(dataItem.sellingPrice)
          : 0,
        pieceSellingPrice: Number(dataItem.pieceSellingPrice)
          ? Number(dataItem.pieceSellingPrice)
          : 0,
        subPieceSellingPrice: Number(dataItem.subPieceSellingPrice)
          ? Number(dataItem.subPieceSellingPrice)
          : 0,
      };
    });
    try {
      const response = await addItems({
        args: {
          items: dataForUpload,
        },
      });

      if (response.data) {
        console.log("Upload successful:", response.data);
        return history.push({
          pathname: dataForUpload[0].internal
            ? `/${me?.role!.name}/internal/stock`
            : `/${me?.role!.name}/merch/stock`,
        });
        // Handle successful upload, e.g., notify the user
      } else {
        console.error("Upload failed:", response.error);
        // Handle errors, e.g., notify the user
      }
    } catch (error) {
      console.error("An error occurred during the upload:", error);
      // Handle errors, e.g., notify the user
    }
  };

  return (
    <Box m={"auto"} p={4}>
      <Flex justify="center" mb={4}>
        <input
          type="file"
          accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
          onChange={handleFileUpload}
          style={{ display: "none" }}
          id="file-upload"
        />
        <label htmlFor="file-upload">
          <Button as="span" variant="outline">
            Import File
          </Button>
        </label>
      </Flex>

      {data.length > 0 && (
        <>
          <Table variant="simple" color={textColor}>
            <Thead>
              <Tr>
                <Th borderColor={borderColor}>Number</Th>
                <Th borderColor={borderColor}>Name</Th>
                <Th borderColor={borderColor}>Type</Th>
                <Th borderColor={borderColor}>Description</Th>
                <Th borderColor={borderColor}>Reorder</Th>
                <Th borderColor={borderColor}>Selling price</Th>
                <Th borderColor={borderColor}>For sale</Th>
                <Th borderColor={borderColor}>Unit</Th>
                <Th borderColor={borderColor}>Stock</Th>
              </Tr>
            </Thead>
            <Tbody>
              {data.map((item, index) => (
                <Tr key={index}>
                  <Td borderColor={borderColor} color={textColor}>
                    {index + 1}
                  </Td>
                  <Td borderColor={borderColor} color={textColor}>
                    {item.name}
                  </Td>
                  <Td borderColor={borderColor} color={textColor}>
                    {item.type}
                  </Td>
                  <Td borderColor={borderColor} color={textColor}>
                    {item.description}
                  </Td>
                  <Td borderColor={borderColor} color={textColor}>
                    {item.reorder}
                  </Td>
                  <Td borderColor={borderColor} color={textColor}>
                    {formatToMoney(item.sellingPrice)}
                  </Td>
                  <Td borderColor={borderColor} color={textColor}>
                    {item.internal ? "No" : "Yes"}
                  </Td>
                  <Td borderColor={borderColor} color={textColor}>
                    {item.unit}
                  </Td>
                  <Td borderColor={borderColor} color={textColor}>
                    {item.stock}
                  </Td>
                </Tr>
              ))}
            </Tbody>
          </Table>

          <Flex justify="center" mt={4}>
            <Button onClick={handleUpload} colorScheme="teal">
              Save to Database
            </Button>
          </Flex>
        </>
      )}
    </Box>
  );
};

export default ImportFile;
