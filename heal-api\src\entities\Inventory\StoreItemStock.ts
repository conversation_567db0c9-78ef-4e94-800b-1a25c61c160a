import { Field, ObjectType } from "type-graphql";
import { Column, Entity, JoinC<PERSON>umn, ManyToOne } from "typeorm";
import { Item } from "../Item";
import { Store } from "./Store";
import { AuditBaseEntity } from "../AuditEntity";
import { BatchStock } from "./Batch";

@ObjectType()
@Entity()
export class StoreItemStock extends AuditBaseEntity {
  @Field()
  @Column({ type: "uuid" })
  itemId!: string;

  @Field(() => Item)
  @ManyToOne(() => Item, (item) => item.storeItemStocks)
  @JoinColumn([
    { name: "itemId", referencedColumnName: "id" },
    { name: "companyId", referencedColumnName: "companyId" },
  ])
  item: Item;

  @Field()
  @Column({ type: "uuid" })
  storeId!: string;

  @Field(() => Store)
  @ManyToOne(() => Store, (store) => store.storeItemStocks)
  @JoinColumn([
    { name: "storeId", referencedColumnName: "id" },
    { name: "companyId", referencedColumnName: "companyId" },
  ])
  store: Store;

  @Field()
  @Column({ type: "float", default: 0 })
  stock: number;

  @Field(() => String, { nullable: true })
  @Column({ type: "uuid", nullable: true })
  batchId: string;

  @Field(() => BatchStock, { nullable: true })
  @ManyToOne(() => BatchStock, (batchStock) => batchStock.storeItemStocks)
  @JoinColumn([
    { name: "batchId", referencedColumnName: "id" },
    { name: "companyId", referencedColumnName: "companyId" },
  ])
  batchStock: BatchStock;
}
