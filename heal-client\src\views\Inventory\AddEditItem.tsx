import React, { useContext, useState } from "react";
import { useForm } from "react-hook-form";

import {
  <PERSON>lex,
  Button,
  FormControl,
  FormLabel,
  Input,
  Text,
  useColorModeValue,
  FormErrorMessage,
  useToast,
  Grid,
  GridItem,
} from "@chakra-ui/react";
import {
  Item,
  useAddItemMutation,
  useEditItemMutation,
} from "../../generated/graphql";
import { useHistory, useLocation } from "react-router-dom";
import { Select } from "chakra-react-select";
import { HSeparator } from "../../components/Separator/Separator";
import { MeContext } from "../../components/Wrapper";

interface IAddItemProps {}

interface State {
  item: Item;
}

const AddEditItem: React.FC<IAddItemProps> = () => {
  const toast = useToast({
    position: "top",
  });
  const [, addItem] = useAddItemMutation();
  const [, editItem] = useEditItemMutation();
  const [error, seterror] = useState("");
  const history = useHistory();
  const location = useLocation();

  const state = location.state as State;
  const [itemType, setItemType] = useState(
    state?.item?.type ? state.item.type : ""
  );
  const me = useContext(MeContext);

  const handleChange: any = (value: any) => {
    if (value) {
      setItemType(value.value);
    } else {
      setItemType("");
    }
  };

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm();

  async function onSubmit(values: any) {
    console.log("WELL WE HAVE: ", values);
    seterror("");
    const args = {
      name: values.name,
      description: values.description,
      type: itemType,
      reference: values.reference,
      reorder: Number(values.reorder),
      internal: history.location.pathname.includes("internal"),
      unit: values.unit,
      pieceUnit: values.pieceUnit,
      subPieceUnit: values.subPieceUnit,
      pieces: Number(values.pieces),
      subPieces: Number(values.subPieces),
    };
    if (history.location.pathname.includes("edit-item")) {
      const { data } = await editItem({ id: state.item.id, args });
      if (data?.editItem.error) {
        console.log("The error cam eback: ", data.editItem.error.message);
        return seterror(data?.editItem.error.message);
      } else {
        reset();
        toast({
          title: "Item edited successful!",
          variant: "left-accent",
          status: "success",
          isClosable: true,
        });
        return history.push({
          pathname: history.location.pathname.includes("internal")
            ? `/${me?.role!.name}/internal/stock`
            : `/${me?.role!.name}/merch/stock`,
          state: {
            typeName: values.name,
          },
        });
      }
    } else {
      const { data } = await addItem({ args });
      if (data?.addItem.error) {
        console.log("The error cam eback: ", data.addItem.error.message);
        return seterror(data?.addItem.error.message);
      } else {
        reset();
        toast({
          title: "Item added successful!",
          variant: "left-accent",
          status: "success",
          isClosable: true,
        });
        return history.push({
          pathname: history.location.pathname.includes("internal")
            ? `/${me?.role!.name}/internal/stock`
            : `/${me?.role!.name}/merch/stock`,
          state: {
            typeName: values.name,
          },
        });
      }
    }
  }

  // Chakra color mode
  const textColor = useColorModeValue("gray.700", "white");
  const bgForm = useColorModeValue("white", "navy.800");
  return (
    <Flex position="relative" mb="90px">
      <Flex
        // minH={{ md: "1000px" }}
        h={{ sm: "initial", md: "75vh", lg: "85vh" }}
        w="100%"
        maxW="1044px"
        mx="auto"
        justifyContent="space-between"
        mb="60px"
        pt={{ md: "0px" }}
      >
        <Flex
          w="100%"
          h="100%"
          alignItems="center"
          justifyContent="center"
          mb="60px"
          mt={{ base: "50px", md: "50px" }}
        >
          <Flex
            zIndex="2"
            direction="column"
            w="720px"
            background="transparent"
            borderRadius="15px"
            p="40px"
            mx={{ base: "100px" }}
            m={{ base: "20px", md: "auto" }}
            bg={bgForm}
            boxShadow={useColorModeValue(
              "0px 5px 14px rgba(0, 0, 0, 0.05)",
              "unset"
            )}
          >
            <Text
              fontSize="xl"
              color={textColor}
              fontWeight="bold"
              textAlign="center"
              mb="12px"
            >
              {history.location.pathname.includes("edit-item") ? "Edit" : "Add"}{" "}
              Item
            </Text>

            <HSeparator mt="6px" mb="6px" />
            <Text textAlign="center" mt="0px" mb="0px">
              Item Details:
            </Text>
            <HSeparator mb="22px" mt="6px" />
            {error && (
              <Text mb="10px" color="red.500" textColor="red.300">
                {error}
              </Text>
            )}
            <form onSubmit={handleSubmit(onSubmit)}>
              <Grid templateColumns="repeat(6, 1fr)" gap={6}>
                <FormControl
                  as={GridItem}
                  colSpan={2}
                  mb={3}
                  isInvalid={errors.name as any}
                >
                  <FormLabel htmlFor="name" fontSize="xs">
                    Item trade name
                  </FormLabel>
                  <Input
                    variant="filled"
                    ms="4px"
                    type="text"
                    fontSize="xs"
                    placeholder="Eg: Kilimanjaro 1L"
                    mb="4px"
                    size="lg"
                    id="name"
                    defaultValue={state?.item?.name}
                    {...register("name", {
                      required: "This is required",
                    })}
                  />
                  <FormErrorMessage mb="10px" fontSize="xs">
                    {errors.name && (errors.name.message as any)}
                  </FormErrorMessage>
                </FormControl>
                <FormControl
                  as={GridItem}
                  colSpan={2}
                  mb={3}
                  isInvalid={errors.description as any}
                >
                  <FormLabel htmlFor="description" fontSize="xs">
                    Item Generic Name
                  </FormLabel>
                  <Input
                    variant="filled"
                    fontSize="xs"
                    ms="4px"
                    type="text"
                    placeholder="Eg: Water"
                    mb="4px"
                    size="lg"
                    id="description"
                    defaultValue={state?.item?.description}
                    {...register("description", {
                      required: "This is required",
                    })}
                  />
                  <FormErrorMessage fontSize="xs">
                    {errors.description && (errors.description.message as any)}
                  </FormErrorMessage>
                </FormControl>
                <FormControl
                  as={GridItem}
                  colSpan={2}
                  mb={3}
                  isInvalid={errors.reference as any}
                >
                  <FormLabel htmlFor="reference" fontSize="xs">
                    Item Reference
                  </FormLabel>
                  <Input
                    variant="filled"
                    fontSize="xs"
                    ms="4px"
                    type="text"
                    placeholder="Eg: Manufacturer Name"
                    mb="4px"
                    size="lg"
                    id="reference"
                    defaultValue={state?.item?.reference}
                    {...register("reference", {
                      required: "This is required",
                    })}
                  />
                  <FormErrorMessage fontSize="xs">
                    {errors.reference && (errors.reference.message as any)}
                  </FormErrorMessage>
                </FormControl>
                <FormControl
                  as={GridItem}
                  colSpan={2}
                  mb={3}
                  isInvalid={errors.reorder as any}
                >
                  <FormLabel htmlFor="reorder" fontSize="xs">
                    Reorder point
                  </FormLabel>
                  <Input
                    variant="filled"
                    fontSize="xs"
                    ms="4px"
                    type="number"
                    placeholder="Low in stock signal"
                    mb="4px"
                    size="lg"
                    id="reorder"
                    defaultValue={state?.item?.reorder}
                    {...register("reorder", {
                      required: "This is required",
                    })}
                  />
                  <FormErrorMessage fontSize="xs">
                    {errors.reorder && (errors.reorder.message as any)}
                  </FormErrorMessage>
                </FormControl>

                <FormControl
                  as={GridItem}
                  colSpan={2}
                  mb={3}
                  isInvalid={errors.unit as any}
                >
                  <FormLabel htmlFor="unit" fontSize="xs">
                    Item Count Unit
                  </FormLabel>
                  <Input
                    variant="filled"
                    fontSize="xs"
                    ms="4px"
                    type="text"
                    placeholder="Count Unit e.g Box, Kg, Crate, Gallon"
                    mb="4px"
                    size="lg"
                    id="unit"
                    defaultValue={state?.item?.unit}
                    {...register("unit", {
                      required: "This is required",
                    })}
                  />
                  <FormErrorMessage fontSize="xs">
                    {errors.unit && (errors.unit.message as any)}
                  </FormErrorMessage>
                </FormControl>
                <FormControl
                  as={GridItem}
                  colSpan={2}
                  mb={3}
                  isInvalid={errors.pieces as any}
                >
                  <FormLabel htmlFor="pieces" fontSize="xs">
                    Number of pieces in an item
                  </FormLabel>
                  <Input
                    variant="filled"
                    fontSize="xs"
                    ms="4px"
                    type="number"
                    placeholder="Eg: 24 (bottles in one crate)"
                    mb="4px"
                    size="lg"
                    id="pieces"
                    defaultValue={
                      state?.item?.pieces ? state.item.pieces : undefined
                    }
                    {...register("pieces")}
                  />
                  <FormErrorMessage fontSize="xs">
                    {errors.pieces && (errors.pieces.message as any)}
                  </FormErrorMessage>
                </FormControl>
                <FormControl
                  as={GridItem}
                  colSpan={2}
                  mb={3}
                  isInvalid={errors.pieceUnit as any}
                >
                  <FormLabel htmlFor="pieceUnit" fontSize="xs">
                    Piece unit
                  </FormLabel>
                  <Input
                    variant="filled"
                    fontSize="xs"
                    ms="4px"
                    type="text"
                    placeholder="Eg: Bottle, Box, Packet"
                    mb="4px"
                    size="lg"
                    id="pieceUnit"
                    {...register("pieceUnit")}
                    defaultValue={
                      state?.item?.pieceUnit ? state?.item?.pieceUnit : ""
                    }
                  />
                  <FormErrorMessage fontSize="xs">
                    {errors.pieceUnit && (errors.pieceUnit.message as any)}
                  </FormErrorMessage>
                </FormControl>
                <FormControl
                  as={GridItem}
                  colSpan={2}
                  mb={3}
                  isInvalid={errors.subPiece as any}
                >
                  <FormLabel htmlFor="subPieces" fontSize="xs">
                    Item Subparts
                  </FormLabel>
                  <Input
                    variant="filled"
                    fontSize="xs"
                    ms="4px"
                    type="number"
                    placeholder="Eg: 10 (subparts of item)"
                    mb="4px"
                    size="lg"
                    id="subPieces"
                    {...register("subPieces")}
                    defaultValue={
                      state?.item?.subPieces ? state?.item?.subPieces : 0
                    }
                  />
                  <FormErrorMessage fontSize="xs">
                    {errors.subPieces && (errors.subPieces.message as any)}
                  </FormErrorMessage>
                </FormControl>
                <FormControl
                  as={GridItem}
                  colSpan={2}
                  mb={3}
                  isInvalid={errors.subPieceUnit as any}
                >
                  <FormLabel htmlFor="subPieceUnit" fontSize="xs">
                    Subpart unit
                  </FormLabel>
                  <Input
                    variant="filled"
                    fontSize="xs"
                    ms="4px"
                    type="text"
                    placeholder="Eg: Vial, Bottle"
                    mb="4px"
                    size="lg"
                    id="subPieceUnit"
                    {...register("subPieceUnit")}
                    defaultValue={
                      state?.item?.subPieceUnit ? state?.item?.subPieceUnit : ""
                    }
                  />
                  <FormErrorMessage fontSize="xs">
                    {errors.subPieceUnit &&
                      (errors.subPieceUnit.message as any)}
                  </FormErrorMessage>
                </FormControl>

                <FormControl
                  as={GridItem}
                  colSpan={6}
                  alignItems="center"
                  mb={3}
                  fontSize="xs"
                  isInvalid={errors.type as any}
                >
                  <Select
                    variant="flushed"
                    isClearable
                    {...register("type")}
                    name="type"
                    defaultValue={{
                      value: state?.item?.type,
                      label:
                        state?.item?.type.charAt(0).toUpperCase() +
                        state?.item?.type.slice(1),
                    }}
                    id="type"
                    options={[
                      {
                        value: "consumable",
                        label:
                          "Consumable Eg: Pills, Syringe, Bandages, Soap, Drip",
                      },
                      {
                        value: "capital goods",
                        label:
                          "Capital Goods Eg: Scisssors, Trolley, Mopper, Screen",
                      },
                    ]}
                    size="sm"
                    placeholder="Select item type"
                    closeMenuOnSelect={true}
                    onChange={handleChange}
                    escapeClearsValue={true}
                    hideSelectedOptions={true}
                  />
                  <FormErrorMessage fontSize="xs">
                    {errors.type && (errors.type.message as any)}
                  </FormErrorMessage>
                </FormControl>
              </Grid>

              <Button
                fontSize="14px"
                variant="dark"
                fontWeight="bold"
                w="100%"
                h="45"
                mb="24px"
                mt={4}
                colorScheme="teal"
                isLoading={isSubmitting}
                type="submit"
              >
                {history.location.pathname.includes("edit-item")
                  ? "Edit"
                  : "Add"}{" "}
                Item
              </Button>
            </form>
          </Flex>
        </Flex>
      </Flex>
    </Flex>
  );
};

export default AddEditItem;
