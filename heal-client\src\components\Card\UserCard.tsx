import React from "react";
import { Avatar, Box, Flex, Text, Badge } from "@chakra-ui/react";

type IUserCardProps = {
  fullName: string;
  role: string;
  phone: string;
};

const UserListCard: React.FC<IUserCardProps> = ({ fullName, role, phone }) => {
  // const { colorMode } = useColorMode();
  // const textColor = useColorModeValue("gray.700", "white");
  // const bgColor = useColorModeValue("white", "gray.800");
  // const shadowColor = useColorModeValue("gray.200", "gray.700");

  return (
    <Flex direction="column">
      <Box
        maxW="sm"
        borderWidth="1px"
        borderRadius="lg"
        overflow="hidden"
        boxShadow="md"
      >
        <Flex align="center" justify="center" p={4} bg="gray.200">
          <Avatar size="xl" name="<PERSON>" src="https://bit.ly/broken-link" />
        </Flex>
        <Box p="6">
          <Text fontWeight="bold" fontSize="xl">
            {fullName}
          </Text>
          <Text mt={2} color="gray.500">
            {role}
          </Text>
          <Text mt={2} color="gray.500">
            {phone}
          </Text>
          <Flex mt={2} justify="center">
            <Badge variant="subtle" colorScheme="green" mr={5}>
              Active
            </Badge>
            <Badge variant="subtle" colorScheme="blue" mr={5}>
              {role}
            </Badge>
            <Badge variant="subtle" colorScheme="orange" mr={5}>
              {role}
            </Badge>
          </Flex>
        </Box>
      </Box>
    </Flex>
  );
};

export default UserListCard;
