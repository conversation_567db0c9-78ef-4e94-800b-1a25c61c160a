import { Column, <PERSON><PERSON><PERSON>, JoinT<PERSON>, ManyToMany, Unique } from "typeorm";
import { AuditBaseEntity } from "./AuditEntity";
import { Field, ObjectType } from "type-graphql";
import { Role } from "./Role";
import { User } from "./User";

@ObjectType()
@Entity()
@Unique(["companyId", "name"]) // same permission can not be created twice for the same company
export class Permission extends AuditBaseEntity {
  @Field(() => String)
  @Column({ type: "text" })
  name!: string;

  @Field(() => [Role], { nullable: true })
  @ManyToMany(() => Role, (role) => role.permissions)
  @JoinTable({
    name: "role_permission",
    joinColumns: [
      { name: "permissionId", referencedColumnName: "id" },
      { name: "companyId", referencedColumnName: "companyId" },
    ],
    inverseJoinColumns: [
      { name: "roleId", referencedColumnName: "id" },
      { name: "companyId", referencedColumnName: "companyId" },
    ],
  })
  roles: Role[];

  @Field(() => [User], { nullable: true })
  @ManyToMany(() => User, (user) => user.permissions)
  @JoinTable({
    name: "user_permission",
    joinColumns: [
      { name: "permissionId", referencedColumnName: "id" },
      { name: "companyId", referencedColumnName: "companyId" },
    ],
    inverseJoinColumns: [
      { name: "userId", referencedColumnName: "id" },
      { name: "companyId", referencedColumnName: "companyId" },
    ],
  })
  users: User[];
}
