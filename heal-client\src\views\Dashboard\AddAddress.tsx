import React, { useState } from "react";
import { useForm } from "react-hook-form";
// Chakra imports
import {
  Flex,
  Button,
  FormControl,
  //   FormLabel,
  //   Input,
  Text,
  useColorModeValue,
  //   FormErrorMessage,
  //   useToast,
} from "@chakra-ui/react";
import {
  useGetAllAddressQuery,
  //   useAddAddressMutation,
  //   useEditAddressMutation,
} from "../../generated/graphql";
// import { useHistory } from "react-router-dom";
import { AsyncCreatableSelect } from "chakra-react-select";

interface IAddAddressProps {}

const AddAddress: React.FC<IAddAddressProps> = () => {
  //   useEffect(()=>{
  //     setDefaultTarget
  //   },[]);
  //   const [defaultTarget, setDefaultTarget] = useState({
  //     target: "",
  //     companyId: undefined
  //   });
  //   const toast = useToast({
  //     position: "top",
  //   });
  //   const [, addAddress] = useAddAddressMutation();
  const [error, seterror] = useState("");
  //   const history = useHistory();
  const [{ data, fetching }, getAyncAddress] = useGetAllAddressQuery();
  //   const [country, setCountry] = useState("Tanzania, Republic of");
  //   const [city, setCity] = useState("");
  //   const [district, setDistrict] = useState("");
  //   const [ward, setWard] = useState("");
  //   const [street, setStreet] = useState("");
  //   const [zip, setZip] = useState(255);

  const handleChange: any = (value: any, field: any) => {
    if (field.name === "country") {
      return;
    }
    console.log(value.label);
  };

  //   const handleCreateOption = (inputValue: string) => {
  //     history.push({
  //       pathname: "/admin/add-category",
  //       state: {
  //         typeName: "Company type",
  //       },
  //     });
  //   };

  const {
    register,
    handleSubmit,
    // reset,
    formState: { isSubmitting },
  } = useForm();

  const fetchCountries: any = async (inputValue: string) => {
    if (!fetching && data) {
      console.log("inputValue is...", inputValue);

      if (inputValue) {
        return data.getAllAddress
          .filter(
            (t) =>
              t.country.toLowerCase().match(inputValue.toLowerCase()) !== null
          )
          .map((c) => {
            return {
              label: c.country,
              value: c.country,
            };
          });
      } else if (
        inputValue === "" ||
        inputValue === undefined ||
        inputValue === null ||
        inputValue === " "
      ) {
        return data.getAllAddress.map((t) => {
          return {
            label: t.country,
            value: t.country,
          };
        });
      }
    }
    await getAyncAddress({ requestPolicy: "network-only" });
    if (data) {
      return data?.getAllAddress.map((c) => {
        return {
          label: c.country,
          value: c.country,
        };
      });
    } else return [];
  };

  async function onSubmit(values: any) {
    console.log("WELL WE HAVE: ", values);
    seterror("");

    // const args = {
    //   name: values.name,
    //   tinNumber: values.tin,
    //   registrationNumber: values.registration,
    //   type: companyType,
    // };
    // const { data } = await addCompany({ params: args });
    // if (data?.registerCompany.error) {
    //   console.log("The error cam eback: ", data.registerCompany.error.message);
    //   return seterror(data?.registerCompany.error.message);
    // } else if (!data?.registerCompany.error && values.addCategories)
    //   return history.push({
    //     pathname: "/admin/categories",
    //     state: {
    //       typeName: values.name,
    //     },
    //   });
    // else {
    //   reset();

    //   toast({
    //     title: "Type added successful!",
    //     variant: "left-accent",
    //     status: "success",
    //     isClosable: true,
    //   });
    //   return history.push({
    //     pathname: "/admin/add-address",
    //     state: {
    //       target: "company",
    //       companyId: data?.registerCompany.id,
    //     },
    //   });
    // }
  }
  // Chakra color mode
  const textColor = useColorModeValue("gray.700", "white");
  const bgForm = useColorModeValue("white", "navy.800");
  // const titleColor = useColorModeValue("gray.700", "blue.500");
  // const colorIcons = useColorModeValue("gray.700", "white");
  // const bgIcons = useColorModeValue("trasnparent", "navy.700");
  // const bgIconsHover = useColorModeValue("gray.50", "whiteAlpha.100");

  return (
    <Flex position="relative" mb="40px">
      <Flex
        // minH={{ md: "1000px" }}
        h={{ sm: "initial", md: "75vh", lg: "85vh" }}
        w="100%"
        maxW="1044px"
        mx="auto"
        justifyContent="space-between"
        mb="30px"
        pt={{ md: "0px" }}
      >
        <Flex
          w="100%"
          h="100%"
          alignItems="center"
          justifyContent="center"
          mb="60px"
          mt={{ base: "50px", md: "20px" }}
        >
          <Flex
            zIndex="2"
            direction="column"
            w="445px"
            background="transparent"
            borderRadius="15px"
            p="40px"
            mx={{ base: "100px" }}
            m={{ base: "20px", md: "auto" }}
            bg={bgForm}
            boxShadow={useColorModeValue(
              "0px 5px 14px rgba(0, 0, 0, 0.05)",
              "unset"
            )}
          >
            <Text
              fontSize="xl"
              color={textColor}
              fontWeight="bold"
              textAlign="center"
              mb="22px"
            >
              Add Company
            </Text>
            {error && (
              <Text mb="10px" color="red.500" textColor="red.300">
                {error}
              </Text>
            )}
            <form onSubmit={handleSubmit(onSubmit)}>
              <FormControl display="flex" alignItems="center" mb="20px">
                <AsyncCreatableSelect
                  variant="flushed"
                  isClearable
                  // ml={3}
                  // width={100}
                  // width="full"
                  {...register("country")}
                  name="country"
                  id="country"
                  defaultOptions={true}
                  cacheOptions={false}
                  allowCreateWhileLoading={false}
                  //   onCreateOption={handleCreateOption}
                  isLoading={fetching}
                  loadOptions={fetchCountries}
                  placeholder="Select or create company ..."
                  closeMenuOnSelect={true}
                  onChange={handleChange}
                  escapeClearsValue={true}
                  hideSelectedOptions={true}
                  //   defaultInputValue="Tanzania, Republic of [object]"
                  // isMulti={true}
                />
                {/* <FormLabel htmlFor="type" mb="0" fontWeight="normal">
                  Select or add company type
                </FormLabel> */}
              </FormControl>
              <Button
                fontSize="14px"
                variant="dark"
                fontWeight="bold"
                w="100%"
                h="45"
                mb="24px"
                mt={4}
                colorScheme="teal"
                isLoading={isSubmitting}
                type="submit"
              >
                Add Address
              </Button>
            </form>
          </Flex>
        </Flex>
      </Flex>
    </Flex>
  );
};

export default AddAddress;
