import { Field, ObjectType } from "type-graphql";
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";

@ObjectType()
@Entity()
export class Message extends BaseEntity {
  @Field(() => String)
  @PrimaryGeneratedColumn("uuid")
  id!: string;

  @Field(() => Boolean)
  @Column({ type: "boolean", default: false })
  attended!: boolean;

  @Field(() => String)
  @CreateDateColumn({ type: "timestamp" })
  createdAt = new Date();

  @Field(() => String)
  @UpdateDateColumn({ type: "timestamp" })
  updatedAt = new Date();

  @Field(() => String)
  @Column({ type: "text" })
  senderName!: string;

  @Field(() => String)
  @Column({ type: "text" })
  senderEmail!: string;

  @Field(() => String, { nullable: true })
  @Column({ type: "text", nullable: true })
  senderPhone?: string;

  @Field(() => String)
  @Column({ type: "text" })
  subject!: string;

  @Field(() => String)
  @Column({ type: "text" })
  message!: string;
}
