import React from "react";
import Dashboard from "./views/Dashboard/Dashboard";
import Tables from "./views/Dashboard/Tables";
import Billing from "./views/Dashboard/Billing";
import Profile from "./views/Dashboard/Profile";

import {
  HomeIcon,
  StatsIcon,
  CreditIcon,
  PersonIcon,
  AccountDetailsOutlineIcon,
  IconUserDoctor,
  ListSettingsFillIcon,
  PathDivideIcon,
  IconCogPlay,
  IconBxsCategoryAlt,
  DivideOutlineIcon,
  HomeGroupPlusIcon,
  BookmarkAdd,
  IconDashboard,
  InventoryIcon,
  InternalStockIcon,
  StockIcon,
  StockLineIcon,
  PrimaryStoreIcon,
  PharmacyPillIcon,
  CashierIcon,
  ServicesIcon,
  CounterIcon,
  DepartmentsIcon,
  UserNurseIcon,
} from "./components/Icons/Icons";
import { ComponentWithAs } from "@chakra-ui/system";
import { IconProps } from "@chakra-ui/react";
import Types from "./views/Dashboard/Types";
import AddType from "./views/Dashboard/AddType";
import AddCategory from "./views/Dashboard/AddCategory";
import EditType from "./views/Dashboard/EditType";
import Companies from "./views/Dashboard/Companies";
import AddCompany from "./views/Dashboard/AddCompany";
import AddAddress from "./views/Dashboard/AddAddress";
import EditCountry from "./views/Dashboard/editCoutry";
import AddPermission from "./views/Dashboard/AddPermissions";
import AddFeatures from "./views/Dashboard/AddFeatures";
import Stock from "./views/Inventory/Stock";
import Users from "./views/User/Users";
import AddUser from "./views/User/AddUser";
import Roles from "./pages/roles";
import AddEditItem from "./views/Inventory/AddEditItem";
import ImportStock from "./views/Inventory/ImportStock";
import ImportFile from "./views/Inventory/ImportFile";
import WriteOff from "./views/Inventory/WriteOff";
import StoresPage from "./views/Inventory/Stores";
import AddStore from "./views/Inventory/AddStore";
import EditStore from "./views/Inventory/EditStore";
import Dispatch from "./views/Inventory/Dispatch";
import Transfer from "./views/Inventory/TransferItems";
import ViewItemPage from "./views/Inventory/ViewItem";
import Pharmacy from "./views/Pharmacy/Pharmacy";
import Cashier from "./views/Dashboard/Cashier/clearBills";
import Services from "./views/Inventory/Services";
import AddEditService from "./views/Inventory/ManageService";
import ViewStore from "./views/Inventory/ViewStore";
import AddPatient from "./views/Patient/AddPatient";
import EditPatient from "./views/Patient/EditPatient";
import ViewPatients from "./views/Patient/ViewPatients";
import AddVisit from "./views/Patient/AddVisit";
import ViewDepartments from "./views/Directory/Departments";
import AddDepartment from "./views/Directory/AddDepartment";
import ViewDepartment from "./views/Directory/ViewDepartment";
import AddClinic from "./views/Directory/AddClinic";
import ViewClinic from "./views/Directory/ViewClinic";
import EditClinic from "./views/Directory/EditClinic";
import EditDepartment from "./views/Directory/Components/EditDepartment";
import AddSchedules from "./views/Directory/AddSchedule";
import EditSchedules from "./views/Directory/EditSchedule";
import VisitingPatients from "./views/Patient/NursingStation";
import AddVitals from "./views/Patient/AddVitals";

interface DashRoute {
  path?: string;
  name: string;
  icon?: ComponentWithAs<"svg", IconProps> | string;
  component?: React.ComponentType;
  layout?: string;
  category?: string;
  state?: string;
  views?: Array<DashRoute>;
  secondaryNavbar?: boolean;
  depth: number;
  appear: boolean;
}

const routes: Array<DashRoute> = [
  {
    name: "Features",
    icon: HomeIcon,
    category: "collapse",
    state: "pageCollapse",
    appear: true,
    depth: 1,
    views: [
      {
        path: "/dashboard",
        name: "Dashboard",
        icon: IconDashboard,
        secondaryNavbar: true,
        component: Dashboard,
        layout: "/admin",
        depth: 2,
        appear: true,
      },
      {
        path: "/reception",
        name: "Reception",
        icon: CounterIcon,
        secondaryNavbar: true,
        component: AddPatient,
        layout: "/admin",
        depth: 2,
        appear: false,
      },
      {
        path: "/patients",
        name: "Patients",
        icon: CounterIcon,
        secondaryNavbar: true,
        component: ViewPatients,
        layout: "/admin",
        depth: 2,
        appear: true,
      },
      {
        path: "/patient-update",
        name: "Update Patient",
        icon: CounterIcon,
        secondaryNavbar: true,
        component: EditPatient,
        layout: "/admin",
        depth: 2,
        appear: false,
      },
      {
        path: "/nursing-station",
        name: "Nursing Station",
        icon: UserNurseIcon,
        secondaryNavbar: true,
        component: VisitingPatients,
        layout: "/admin",
        depth: 2,
        appear: true,
      },
      {
        path: "/visit",
        name: "Add Patient Visit",
        icon: CounterIcon,
        secondaryNavbar: true,
        component: AddVisit,
        layout: "/admin",
        depth: 2,
        appear: false,
      },
      {
        path: "/add-vitals",
        name: "Add Vitals",
        icon: CounterIcon,
        secondaryNavbar: true,
        component: AddVitals,
        layout: "/admin",
        depth: 2,
        appear: false,
      },
      {
        path: "/pharmacy",
        name: "Pharmacy",
        icon: PharmacyPillIcon,
        secondaryNavbar: true,
        component: Pharmacy,
        layout: "/admin",
        depth: 2,
        appear: true,
      },
      {
        path: "/cashier",
        name: "Cashier",
        icon: CashierIcon,
        secondaryNavbar: true,
        component: Cashier,
        layout: "/admin",
        depth: 2,
        appear: true,
      },
      {
        name: "Inventory",
        icon: InventoryIcon,
        category: "collapse",
        state: "pageCollapse",
        secondaryNavbar: true,
        depth: 2,
        appear: true,
        views: [
          {
            path: "/stores",
            name: "Stores",
            icon: PrimaryStoreIcon,
            secondaryNavbar: true,
            component: StoresPage,
            layout: "/admin",
            depth: 2,
            appear: true,
          },
          {
            path: "/view-store",
            name: "View Store",
            icon: StockIcon,
            secondaryNavbar: true,
            component: ViewStore,
            layout: "/admin",
            depth: 2,
            appear: false,
          },
          {
            path: "/add-store",
            name: "Add Store",
            icon: StockIcon,
            secondaryNavbar: true,
            component: AddStore,
            layout: "/admin",
            depth: 2,
            appear: false,
          },
          {
            path: "/view-item",
            name: "View Item",
            icon: StockIcon,
            secondaryNavbar: true,
            component: ViewItemPage,
            layout: "/admin",
            depth: 2,
            appear: false,
          },
          {
            path: "/dispatch",
            name: "Dispatch Items",
            icon: StockIcon,
            secondaryNavbar: true,
            component: Dispatch,
            layout: "/admin",
            depth: 2,
            appear: false,
          },
          {
            path: "/transfer-items",
            name: "Transfer Items",
            icon: StockIcon,
            secondaryNavbar: true,
            component: Transfer,
            layout: "/admin",
            depth: 2,
            appear: false,
          },
          {
            path: "/edit-store",
            name: "Edit Store",
            icon: StockIcon,
            secondaryNavbar: true,
            component: EditStore,
            layout: "/admin",
            depth: 2,
            appear: false,
          },
          {
            path: "/internal/stock",
            name: "Internal Stock",
            icon: InternalStockIcon,
            secondaryNavbar: true,
            component: Stock,
            layout: "/admin",
            depth: 3,
            appear: true,
          },
          {
            path: "/internal/add-item",
            name: "Add Item",
            icon: StockLineIcon,
            secondaryNavbar: true,
            component: AddEditItem,
            layout: "/admin",
            depth: 3,
            appear: false,
          },
          {
            path: "/internal/edit-item",
            name: "Edit Item",
            icon: StockLineIcon,
            secondaryNavbar: true,
            component: AddEditItem,
            layout: "/admin",
            depth: 3,
            appear: false,
          },
          {
            path: "/merch/stock",
            name: "Merchandise Stock",
            icon: StockIcon,
            secondaryNavbar: true,
            component: Stock,
            layout: "/admin",
            depth: 3,
            appear: true,
          },
          {
            path: "/merch/add-item",
            name: "Add Item",
            icon: StockLineIcon,
            secondaryNavbar: true,
            component: AddEditItem,
            layout: "/admin",
            depth: 3,
            appear: false,
          },
          {
            path: "/merch/edit-item",
            name: "Edit Item",
            icon: StockLineIcon,
            secondaryNavbar: true,
            component: AddEditItem,
            layout: "/admin",
            depth: 3,
            appear: false,
          },
          {
            path: "/import-stock",
            name: "Import Stock",
            icon: "UserAddIcon",
            secondaryNavbar: true,
            component: ImportStock,
            layout: "/admin",
            depth: 2,
            appear: false,
          },
          {
            path: "/bulk-import",
            name: "Import Stock",
            icon: "UserAddIcon",
            secondaryNavbar: true,
            component: ImportFile,
            layout: "/admin",
            depth: 2,
            appear: false,
          },
          {
            path: "/write-off",
            name: "Write Off Stock",
            icon: StockLineIcon,
            secondaryNavbar: true,
            component: WriteOff,
            layout: "/admin",
            depth: 2,
            appear: false,
          },
        ],
      },
      // {
      //   name: "Employee",
      //   icon: IconUserDoctor,
      //   category: "collapse",
      //   state: "pageCollapse",
      //   secondaryNavbar: true,
      //   depth: 2,
      //   appear: true,
      //   views: [
      //     {
      //       path: "/dashboard",
      //       name: "Dashboard",
      //       icon: IconDashboard,
      //       secondaryNavbar: true,
      //       component: Dashboard,
      //       layout: "/employee",
      //       depth: 3,
      //       appear: true,
      //     },
      //     {
      //       path: "/users",
      //       name: "Users",
      //       icon: IconUserDoctor,
      //       secondaryNavbar: true,
      //       component: Users,
      //       layout: "/employee",
      //       depth: 3,
      //       appear: true,
      //     },
      //     {
      //       path: "/add-user",
      //       name: "Add User",
      //       icon: "UserAddIcon",
      //       secondaryNavbar: true,
      //       component: AddUser,
      //       layout: "/employee",
      //       depth: 3,
      //       appear: false,
      //     },
      //     {
      //       name: "Inventory",
      //       icon: InventoryIcon,
      //       category: "collapse",
      //       state: "pageCollapse",
      //       secondaryNavbar: true,
      //       depth: 3,
      //       appear: true,
      //       views: [
      //         {
      //           name: "Internal",
      //           icon: InternalStockIcon,
      //           category: "collapse",
      //           state: "pageCollapse",
      //           secondaryNavbar: true,
      //           depth: 3,
      //           appear: true,
      //           views: [
      //             {
      //               path: "/internal/stock",
      //               name: "Stock",
      //               icon: StockLineIcon,
      //               secondaryNavbar: true,
      //               component: Stock,
      //               layout: "/employee",
      //               depth: 5,
      //               appear: true,
      //             },
      //             {
      //               path: "/internal/add-item",
      //               name: "Add Item",
      //               icon: StockLineIcon,
      //               secondaryNavbar: true,
      //               component: AddItem,
      //               layout: "/employee",
      //               depth: 5,
      //               appear: false,
      //             },
      //           ],
      //         },
      //         {
      //           name: "Merchandise",
      //           icon: StockIcon,
      //           category: "collapse",
      //           state: "pageCollapse",
      //           secondaryNavbar: true,
      //           depth: 3,
      //           appear: true,
      //           views: [
      //             {
      //               path: "/merch/stock",
      //               name: "Stock",
      //               icon: StockLineIcon,
      //               secondaryNavbar: true,
      //               component: Stock,
      //               layout: "/employee",
      //               depth: 5,
      //               appear: true,
      //             },
      //             {
      //               path: "/merch/add-item",
      //               name: "Add Item",
      //               icon: StockLineIcon,
      //               secondaryNavbar: true,
      //               component: AddItem,
      //               layout: "/employee",
      //               depth: 5,
      //               appear: false,
      //             },
      //           ],
      //         },
      //       ],
      //     },
      //   ],
      // },
    ],
  },
  {
    name: "Settings",
    icon: ListSettingsFillIcon,
    category: "collapse",
    state: "pageCollapse",
    depth: 1,
    appear: true,
    views: [
      {
        path: "/companies",
        name: "Companies",
        icon: HomeGroupPlusIcon,
        secondaryNavbar: true,
        component: Companies,
        layout: "/admin",
        depth: 2,
        appear: true,
      },
      {
        path: "/departments",
        name: "Departments",
        icon: DepartmentsIcon,
        secondaryNavbar: true,
        component: ViewDepartments,
        layout: "/admin",
        depth: 2,
        appear: true,
      },
      {
        path: "/users",
        name: "Users",
        icon: IconUserDoctor,
        secondaryNavbar: true,
        component: Users,
        layout: "/admin",
        depth: 2,
        appear: true,
      },
      {
        path: "/add-user",
        name: "Add User",
        icon: "UserAddIcon",
        secondaryNavbar: true,
        component: AddUser,
        layout: "/admin",
        depth: 2,
        appear: false,
      },
      {
        path: "/add-department",
        name: "Add Department",
        icon: "UserAddIcon",
        secondaryNavbar: true,
        component: AddDepartment,
        layout: "/admin",
        depth: 2,
        appear: false,
      },
      {
        path: "/edit-department",
        name: "Edit Department",
        icon: "UserAddIcon",
        secondaryNavbar: true,
        component: EditDepartment,
        layout: "/admin",
        depth: 2,
        appear: false,
      },
      // {
      //   path: "/register-vitals",
      //   name: "Register Vitals",
      //   icon: "UserAddIcon",
      //   secondaryNavbar: true,
      //   component: AddVitals,
      //   layout: "/admin",
      //   depth: 2,
      //   appear: false,
      // },
      {
        path: "/add-subdepartment",
        name: "Add Sub-Department",
        icon: "UserAddIcon",
        secondaryNavbar: true,
        component: AddDepartment,
        layout: "/admin",
        depth: 2,
        appear: false,
      },
      {
        path: "/add-clinic",
        name: "Add Clinic",
        icon: "UserAddIcon",
        secondaryNavbar: true,
        component: AddClinic,
        layout: "/admin",
        depth: 2,
        appear: false,
      },
      {
        path: "/edit-clinic",
        name: "Edit Clinic",
        icon: "UserAddIcon",
        secondaryNavbar: true,
        component: EditClinic,
        layout: "/admin",
        depth: 2,
        appear: false,
      },
      {
        path: "/add-schedule",
        name: "Add Schedule",
        icon: "UserAddIcon",
        secondaryNavbar: true,
        component: AddSchedules,
        layout: "/admin",
        depth: 2,
        appear: false,
      },
      {
        path: "/edit-schedule",
        name: "Update Schedule",
        icon: "UserAddIcon",
        secondaryNavbar: true,
        component: EditSchedules,
        layout: "/admin",
        depth: 2,
        appear: false,
      },
      {
        path: "/view-department",
        name: "View Department",
        icon: "UserViewIcon",
        secondaryNavbar: true,
        component: ViewDepartment,
        layout: "/admin",
        depth: 2,
        appear: false,
      },
      {
        path: "/view-clinic",
        name: "View Clinic",
        icon: "UserViewIcon",
        secondaryNavbar: true,
        component: ViewClinic,
        layout: "/admin",
        depth: 2,
        appear: false,
      },
      {
        path: "/roles",
        name: "Roles",
        icon: PathDivideIcon,
        secondaryNavbar: true,
        component: Roles,
        layout: "/admin",
        depth: 2,
        appear: true,
      },
      {
        path: "/permissions",
        name: "Permissions",
        icon: IconCogPlay,
        secondaryNavbar: true,
        component: AddPermission,
        layout: "/admin",
        depth: 2,
        appear: true,
      },
      {
        path: "/features",
        name: "Features",
        icon: BookmarkAdd,
        secondaryNavbar: true,
        component: AddFeatures,
        layout: "/admin",
        depth: 2,
        appear: true,
      },
      {
        path: "/types",
        name: "Types",
        icon: IconBxsCategoryAlt,
        secondaryNavbar: true,
        component: Types,
        layout: "/admin",
        depth: 2,
        appear: true,
      },
      {
        path: "/add-type",
        name: "Add Type",
        icon: "IconBxsCategoryAlt",
        secondaryNavbar: true,
        component: AddType,
        layout: "/admin",
        depth: 2,
        appear: false,
      },
      {
        path: "/categories",
        name: "Categories",
        icon: DivideOutlineIcon,
        secondaryNavbar: true,
        component: AddCategory,
        layout: "/admin",
        depth: 2,
        appear: true,
      },
      {
        path: "/services",
        name: "Services",
        icon: ServicesIcon,
        secondaryNavbar: true,
        component: Services,
        layout: "/admin",
        depth: 2,
        appear: true,
      },
      {
        path: "/manage-service",
        name: "Manage service",
        icon: "IconBxsCategoryAlt",
        secondaryNavbar: true,
        component: AddEditService,
        layout: "/admin",
        depth: 2,
        appear: false,
      },
      {
        path: "/edit-country",
        name: "Edit Country",
        icon: "IconBxsCategoryAlt",
        secondaryNavbar: true,
        component: EditCountry,
        layout: "/admin",
        depth: 2,
        appear: false,
      },
      {
        path: "/add-address",
        name: "Add Type",
        icon: "IconBxsCategoryAlt",
        secondaryNavbar: true,
        component: AddAddress,
        layout: "/admin",
        depth: 2,
        appear: false,
      },
      {
        path: "/edit-type",
        name: "Edit Type",
        icon: "IconBxsCategoryAlt",
        secondaryNavbar: true,
        component: EditType,
        layout: "/admin",
        depth: 2,
        appear: false,
      },
      {
        path: "/add-company",
        name: "Add Company",
        icon: "IconBxsCategoryAlt",
        secondaryNavbar: true,
        component: AddCompany,
        layout: "/admin",
        depth: 2,
        appear: false,
      },
    ],
  },
  {
    name: "ACCOUNT",
    icon: AccountDetailsOutlineIcon,
    category: "collapse",
    state: "pageCollapse",
    depth: 1,
    appear: true,
    views: [
      {
        path: "/profile",
        name: "Profile",
        icon: PersonIcon,
        secondaryNavbar: true,
        component: Profile,
        layout: "/admin",
        depth: 2,
        appear: true,
      },
      {
        path: "/tables",
        name: "Tables",
        icon: StatsIcon,
        component: Tables,
        layout: "/admin",
        depth: 2,
        appear: true,
      },
      {
        path: "/billing",
        name: "Billing",
        icon: CreditIcon,
        component: Billing,
        layout: "/admin",
        depth: 2,
        appear: true,
      },
    ],
  },
];

export default routes;
