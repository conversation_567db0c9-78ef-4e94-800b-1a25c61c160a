// Chakra imports
import {
  Flex,
  Table,
  Tbody,
  Text,
  Th,
  Thead,
  Tr,
  Td,
  Button,
  Input,
  InputGroup,
  InputRightElement,
  useColorModeValue,
} from "@chakra-ui/react";
// Custom components
import Card from "../../components/Card/Card.js";
import CardBody from "../../components/Card/CardBody.js";
import CardHeader from "../../components/Card/CardHeader.js";
import { useGetPatientsQuery } from "../../generated/graphql.js";
import { SetStateAction, useState } from "react";
import { SearchIcon } from "@chakra-ui/icons";
import { Link, useHistory } from "react-router-dom";

function ViewPatients() {
  const [{ data: patients }] = useGetPatientsQuery({
    requestPolicy: "network-only",
  }); // Fetch data using the hook
  const [searchQuery, setSearchQuery] = useState(""); // State to store the search query
  const textColor = useColorModeValue("gray.700", "white");
  const borderColor = useColorModeValue("gray.200", "gray.600");
  const buttonBg = useColorModeValue("teal.400", "teal.600");
  const buttonHoverBg = useColorModeValue("teal.600", "teal.700");
  const history = useHistory();

  const handleSearchChange = (e: {
    target: { value: SetStateAction<string> };
  }) => {
    setSearchQuery(e.target.value);
  };

  const filteredPatients = patients?.getPatients?.filter((patient) => {
    const fullName = `${patient.firstname} ${patient.lastname}`.toLowerCase();
    return (
      fullName.includes(searchQuery.toLowerCase()) ||
      patient.status.toLowerCase().includes(searchQuery.toLowerCase()) ||
      patient.phone.includes(searchQuery)
    );
  });

  return (
    <Flex direction="column" pt={{ base: "120px", md: "75px" }}>
      <Card overflowX={{ sm: "scroll", xl: "hidden" }} pb="0px">
        <CardHeader
          p="6px 0px 22px 0px"
          display="flex"
          justifyContent="space-between"
          alignItems="center"
        >
          <Text fontSize="xl" color={textColor} fontWeight="bold">
            Patients
          </Text>
          <Link to={"reception"}>
            <Button variant="outline">Add Patient</Button>
          </Link>
          <InputGroup w={{ base: "100%", md: "300px" }}>
            <Input
              placeholder="Search patients..."
              value={searchQuery}
              onChange={handleSearchChange}
              bg={useColorModeValue("white", "teal.800")}
              borderColor={borderColor}
              _placeholder={{ color: "gray.400" }}
            />
            <InputRightElement pointerEvents="none">
              <SearchIcon color="gray.300" />
            </InputRightElement>
          </InputGroup>
        </CardHeader>
        <CardBody>
          <Table variant="simple" color={textColor}>
            <Thead>
              <Tr my=".8rem" pl="0px" color="gray.400">
                <Th pl="0px" borderColor={borderColor} color="gray.400">
                  Name
                </Th>
                <Th borderColor={borderColor} color="gray.400">
                  Status
                </Th>
                <Th borderColor={borderColor} color="gray.400">
                  Phone
                </Th>
                <Th borderColor={borderColor} color="gray.400">
                  Current Location
                </Th>
                <Th borderColor={borderColor}></Th>
              </Tr>
            </Thead>
            <Tbody>
              {filteredPatients?.length && filteredPatients?.length > 0 ? (
                filteredPatients.map((patient) => (
                  <Tr key={patient.id}>
                    <Td pl="0px" borderColor={borderColor}>
                      {patient.firstname + " " + patient.lastname}
                    </Td>
                    <Td borderColor={borderColor}>
                      <Text
                        fontWeight="bold"
                        color={
                          patient.status === "normal"
                            ? "green.400"
                            : patient.status === "critical"
                            ? "yellow.400"
                            : "red.400"
                        }
                      >
                        {patient.status}
                      </Text>
                    </Td>
                    <Td borderColor={borderColor}>{patient.phone}</Td>
                    <Td borderColor={borderColor}>
                      <Button
                        as={Link}
                        to={{
                          pathname: "visit",
                          state: {
                            patient: patient,
                          },
                        }}
                        size="sm"
                        bg="blue.500"
                        color="white"
                        _hover={{ bg: "gray.600" }}
                      >
                        admit
                      </Button>
                    </Td>
                    <Td borderColor={borderColor} textAlign="right">
                      <Button
                        size="sm"
                        bg={buttonBg}
                        color="white"
                        _hover={{ bg: buttonHoverBg }}
                        onClick={() =>
                          console.log(
                            `View Patient ${
                              patient.firstname + " " + patient.lastname
                            }`
                          )
                        }
                        mr="2"
                      >
                        View Patient
                      </Button>
                      <Button
                        size="sm"
                        bg={buttonBg}
                        color="white"
                        _hover={{ bg: buttonHoverBg }}
                        onClick={() =>
                          history.push({
                            pathname: "patient-update",
                            state: {
                              patientId: patient.id,
                            },
                          })
                        }
                      >
                        Edit Patient
                      </Button>
                    </Td>
                  </Tr>
                ))
              ) : (
                <Tr>
                  <Td colSpan={5} textAlign="center" color="gray.500">
                    No patients found.
                  </Td>
                </Tr>
              )}
            </Tbody>
          </Table>
        </CardBody>
      </Card>
    </Flex>
  );
}

export default ViewPatients;
