import { isAuth } from "../middleware/isAuth";
import {
  Arg,
  Mutation,
  Resolver,
  UseMiddleware,
  Query,
  InputType,
  Field,
  ObjectType,
  Ctx,
} from "type-graphql";
import { Type } from "../entities/Type";
import { BooleanResponse } from "./user";
import { In } from "typeorm";
import { Category } from "../entities/Category";
import { MyContext } from "../types";
import { logError } from "../utils/utils";

@InputType()
class TypeArgs {
  @Field()
  name: string;
  @Field()
  description: string;
}

@InputType()
class TypeEditArgs {
  @Field()
  name: string;
  @Field()
  description: string;
  @Field(() => [String])
  categories: [string];
}

@ObjectType()
export class BooleanResponseWithType extends BooleanResponse {
  @Field(() => Type, { nullable: true })
  data?: Type;
}

@Resolver(Type)
export class TypeResolver {
  @Mutation(() => BooleanResponseWithType)
  @UseMiddleware(isAuth)
  async addType(
    @Arg("args") inputArgs: TypeArgs,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponseWithType> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    try {
      const newType = await Type.create({
        name: inputArgs.name,
        description: inputArgs.description,
        companyId,
      }).save();

      return { status: true, data: newType };
    } catch (error) {
      logError(
        companyId,
        error.message,
        "TYPE_ADD_ERROR",
        JSON.stringify({ error, inputArgs }),
        "high",
        `Failed to add type: ${inputArgs.name}`,
        userId
      );
      console.error("Error adding type: ", error.message);
      return {
        status: false,
        error: { target: "general", message: error.message },
      };
    }
  }

  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async editType(
    @Arg("id", () => String) id: string,
    @Arg("args") editArgs: TypeEditArgs,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    if (!editArgs.name || editArgs.name === "") {
      logError(
        companyId,
        "Empty type name",
        "TYPE_EDIT_EMPTY_NAME",
        JSON.stringify(editArgs),
        "medium",
        "Edit type failed - empty name provided",
        userId
      );
      return {
        status: false,
        error: { target: "general", message: "Name cannot be empty!" },
      };
    }

    const type = await Type.findOne({
      where: { id, companyId: req.session.companyId },
    });

    if (!type) {
      logError(
        companyId,
        "Type not found",
        "TYPE_EDIT_NOT_FOUND",
        JSON.stringify({ id, editArgs }),
        "medium",
        `Edit type failed - type not found: ${id}`,
        userId
      );
      return {
        status: false,
        error: { target: "general", message: "Type does not exist!" },
      };
    }

    try {
      const cats = await Category.find({
        where: {
          id: In(editArgs.categories),
          companyId: req.session.companyId,
        },
      });

      type.name = editArgs.name;
      type.description = editArgs.description;
      type.category = cats;
      await type.save();
    } catch (err) {
      logError(
        companyId,
        err.message,
        "TYPE_EDIT_ERROR",
        JSON.stringify({ error: err, id, editArgs }),
        "high",
        `Failed to edit type: ${id}`,
        userId
      );
      console.error("Error editing type: ", err.message);
      return {
        status: false,
        error: { target: "general", message: err.message },
      };
    }
    return { status: true };
  }

  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async deleteType(
    @Arg("id", () => String) id: string,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    try {
      // Ensure that categories belong to the companyId
      const categories = await Category.find({
        where: { type: id, companyId: req.session.companyId },
      });

      const categoryIds = categories.map((cat) => cat.id);

      if (categoryIds.length) {
        await Category.delete({
          id: In(categoryIds),
          companyId: req.session.companyId,
        });
      }

      await Type.delete({ id, companyId: req.session.companyId });
    } catch (err) {
      logError(
        companyId,
        err.message,
        "TYPE_DELETE_ERROR",
        JSON.stringify({ error: err, id }),
        "high",
        `Failed to delete type: ${id}`,
        userId
      );
      console.error("Error deleting type: ", err.message);
      return {
        status: false,
        error: { target: "general", message: err.message },
      };
    }

    return { status: true };
  }

  @Query(() => [Type])
  @UseMiddleware(isAuth)
  async getTypes(@Ctx() { req }: MyContext): Promise<Type[]> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    try {
      return Type.find({ where: { companyId }, relations: ["category"] });
    } catch (err) {
      logError(
        companyId,
        err.message,
        "TYPE_GET_ALL_ERROR",
        JSON.stringify(err),
        "high",
        "Failed to fetch types",
        userId
      );
      throw err;
    }
  }

  @Query(() => Type, { nullable: true })
  @UseMiddleware(isAuth)
  async getType(
    @Arg("id", () => String) id: string,
    @Ctx() { req }: MyContext
  ): Promise<Type | undefined> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    try {
      const type = await Type.findOne({
        where: { id, companyId: req.session.companyId },
        relations: ["category"],
      });

      if (!type) {
        logError(
          companyId,
          "Type not found",
          "TYPE_GET_NOT_FOUND",
          JSON.stringify({ id }),
          "medium",
          `Get type failed - type not found: ${id}`,
          userId
        );
      }

      return type;
    } catch (err) {
      logError(
        companyId,
        err.message,
        "TYPE_GET_ERROR",
        JSON.stringify({ error: err, id }),
        "high",
        `Failed to fetch type: ${id}`,
        userId
      );
      throw err;
    }
  }
}
