{"Dashboard": {"Super Admin": "Loads Admin Dashboard To Manage Everything, All Users and All Companies (Hospitals, Pharmacy)", "Company with Branches Employee HR": "Manages company and branches", "Company Employee": "Hospital functionalities", "Employee": "Someday will add a employee portal"}, "Super Admin Modules": {"Dashboard": {}, "Modules": {"Settings": {"Companies": [], "roles": [], "permissions": [], "features": [], "types": {"system-types": ["Not a punched type -> NOTE THAT THESE TYPES ARE SOME<PERSON><PERSON><PERSON><PERSON> HARDCODED IN THE SYSTEM, IF YOU DON'T ADD THEM THEN THE SYSTEM WILL MISS BEHAVE AND ARE CASE SENSITIVE", "User Roles", "Punched as categories -> List of hard coded user roles include: ['admin','owner','employee','client'] ", "Company type", "Punched as categories -> List of hard coded company types include: ['HOSPITAL','HEALTH CENTER','DISPENSARY','REFERRAL','PHARMACY'] ", "user permissions", "company features", "Punched as categories -> Examples of user permissions to show format include: ['company>create','company-address>edit','patient-in-registration>delete'] ", "Not a punched type -> system hard coded permissions", "Not a punched type -> Examples of hard coded user permissions include: ['none','view','read''create','edit','delete'] ", "Not a punched type -> system hard coded features", "Not a punched type -> Examples of hard coded company features include: [                   will come here                      ] "]}, "categories": {}}, "Inventory": {"types": {"system-types": ["Not a punched type -> NOTE THAT THESE TYPES ARE SOME<PERSON><PERSON><PERSON><PERSON> HARDCODED IN THE SYSTEM, IF YOU DON'T ADD THEM THEN THE SYSTEM WILL MISS BEHAVE AND ARE CASE SENSITIVE", "Inventory Permissions", "Punched as categories -> Permissions include: ['inventory-transfer>delete'] "]}, "Internal Stock": ["Stock [done]", "Transfer requests", "Purchase / Import", "Return notice", "Direct dispatch", "Write off", "Reports"], "Merchandise": ["Stock", "Transfer requests", "Purchase / Import", "Return notice", "Direct dispatch", "Write off", "Reports"]}}}}