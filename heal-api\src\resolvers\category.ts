import { isAuth } from "../middleware/isAuth";
import {
  Arg,
  Mutation,
  Resolver,
  UseMiddleware,
  Query,
  InputType,
  Field,
  Ctx,
  ObjectType,
} from "type-graphql";
import { Category } from "../entities/Category";
import { BooleanResponse, FieldError } from "./user";
import { Type } from "../entities/Type";
import { MyContext } from "../types";
import { logError } from "../utils/utils";

@InputType()
class CategoryArgs {
  @Field()
  name: string;
  @Field(() => Number)
  type: number;
}

@InputType()
class CategoryTypeArgs {
  @Field()
  name: string;
  @Field()
  typeName: string;
}

@ObjectType()
class CategoryResponse {
  @Field(() => FieldError, { nullable: true })
  error?: FieldError;
  @Field(() => Category, { nullable: true })
  category?: Category;
}

@Resolver(Category)
export class CategoryResolver {
  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async addCategory(
    @Arg("args") inputArgs: CategoryArgs,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    if (!inputArgs.type)
      return {
        status: false,
        error: { target: "general", message: "Category must have a type!" },
      };
    if (!inputArgs.name)
      return {
        status: false,
        error: { target: "name", message: "Name can not be empty!" },
      };
    try {
      const type = await Type.findOne({
        where: { id: inputArgs.type, companyId },
      });

      await Category.create({
        name: inputArgs.name,
        companyId,
        type: type,
        type_category: inputArgs.type + inputArgs.name + type?.companyId,
      }).save();

      return { status: true };
    } catch (err) {
      console.error(err.message);
      logError(
        companyId,
        err.message,
        "CATEGORY_ADD_ERROR",
        JSON.stringify(err),
        "medium",
        `Add category attempt: ${inputArgs.name}`,
        userId.toString()
      );
      return {
        status: false,
        error: { target: "general", message: err.message },
      };
    }
  }

  @Mutation(() => CategoryResponse)
  @UseMiddleware(isAuth)
  async addCategoryWithTypeName(
    @Arg("args") inputArgs: CategoryTypeArgs, // Assuming CategoryArgs includes name and typeName
    @Ctx() { req }: MyContext // Accessing companyId from session
  ): Promise<CategoryResponse> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    // Validation for input arguments
    if (!inputArgs.typeName)
      return {
        error: {
          target: "general",
          message: "Category must have a type name!",
        },
      };
    if (!inputArgs.name)
      return {
        error: { target: "name", message: "Name cannot be empty!" },
      };

    try {
      let type = await Type.findOne({
        where: { name: inputArgs.typeName, companyId },
      });

      if (!type) {
        type = await Type.create({
          name: inputArgs.typeName,
          companyId,
          description: `Auto-created for category: ${inputArgs.typeName}`,
        }).save();
      }

      const category = await Category.create({
        name: inputArgs.name,
        companyId,
        type: type,
        type_category: inputArgs.typeName + inputArgs.name + companyId,
      }).save();

      return { category };
    } catch (err) {
      console.error(err.message);
      logError(
        companyId,
        err.message,
        "CATEGORY_ADD_WITH_TYPE_ERROR",
        JSON.stringify(err),
        "medium",
        `Add category with type attempt: ${inputArgs.name} (Type: ${inputArgs.typeName})`,
        userId.toString()
      );
      return {
        error: { target: "general", message: err.message },
      };
    }
  }

  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async editCategory(
    @Arg("id") id: string,
    @Arg("args") editArgs: CategoryArgs,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    if (!editArgs.name || editArgs.name === "")
      return {
        status: false,
        error: { target: "general", message: "name can not be empty!" },
      };

    try {
      const category = await Category.findOne({
        id,
        companyId,
      });

      if (!category)
        return {
          status: false,
          error: { target: "general", message: "category does not exist!" },
        };

      category.name = editArgs.name;
      category.type_category = editArgs.type + editArgs.name;
      await category.save();

      return { status: true };
    } catch (err) {
      console.error(err.message);
      logError(
        companyId,
        err.message,
        "CATEGORY_EDIT_ERROR",
        JSON.stringify(err),
        "medium",
        `Edit category attempt: ID ${id}`,
        userId.toString()
      );
      return {
        status: false,
        error: { target: "general", message: err.message },
      };
    }
  }

  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async editCategoryByName(
    @Arg("name") name: string,
    @Arg("args") editArgs: CategoryArgs,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    if (!name || name === "")
      return {
        status: false,
        error: { target: "general", message: "Not a valid category!" },
      };
    if (!editArgs.name || editArgs.name === "")
      return {
        status: false,
        error: { target: "general", message: "name can not be empty!" },
      };
    const category = await Category.findOne({
      where: {
        type_category: editArgs.type + name,
        companyId: req.session.companyId,
      },
    });
    if (!category)
      return {
        status: false,
        error: { target: "general", message: "category does not exist!" },
      };
    try {
      category.name = editArgs.name;
      category.type_category = editArgs.type + editArgs.name;
      await category.save();
    } catch (err) {
      console.error(err.message);
      return {
        status: false,
        error: { target: "general", message: err.message },
      };
    }
    return { status: true };
  }

  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async deleteCategory(
    @Arg("id") id: string,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    try {
      await Category.delete({ id, companyId });
      return { status: true };
    } catch (err) {
      console.error(err.message);
      logError(
        companyId,
        err.message,
        "CATEGORY_DELETE_ERROR",
        JSON.stringify(err),
        "medium",
        `Delete category attempt: ID ${id}`,
        userId.toString()
      );
      return {
        status: false,
        error: { target: "general", message: err.message },
      };
    }
  }

  @Query(() => [Category])
  @UseMiddleware(isAuth)
  async getCategories(
    @Arg("type") type: string,
    @Ctx() { req }: MyContext
  ): Promise<Category[]> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    try {
      const dbType = await Type.findOne({
        where: { name: type, companyId },
      });

      if (dbType) {
        return Category.find({
          where: { type: dbType, companyId },
          order: { name: "ASC" },
        });
      }

      return [];
    } catch (err) {
      console.error(err.message);
      logError(
        companyId,
        err.message,
        "CATEGORY_GET_ERROR",
        JSON.stringify(err),
        "low",
        `Get categories attempt for type: ${type}`,
        userId.toString()
      );
      return [];
    }
  }

  @Query(() => [Category])
  @UseMiddleware(isAuth)
  getAllCategories(@Ctx() { req }: MyContext): Promise<Category[]> {
    return Category.find({
      where: { companyId: req.session.companyId },
      relations: ["type"],
    });
  }
}
