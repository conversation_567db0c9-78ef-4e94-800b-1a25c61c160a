{"$schema": "https://json.schemastore.org/package.json", "name": "talisia-api", "version": "1.2.4", "description": "", "type": "commonjs", "main": "./dist/index.js", "bin": "dist/index.js", "scripts": {"watch": "tsc -w", "dev": "nodemon dist/index.js", "dev2": "nodemon --exec dist/index.js", "start": "node dist/index.js", "start2": "ts-node src/index.ts", "clean": "<PERSON><PERSON><PERSON> dist", "build": "npm run clean && tsc -p tsconfig.json", "pkg": "pkg . --options max-old-space-size=4096 --targets node16-linux-x64 --out-path=bin", "typeorm": "node --require ts-node/register ./node_modules/typeorm/cli.js", "migration:create": "npm run typeorm migration:create -- -n", "migration:generate:init": "npx typeorm migration:generate -n InitSchema -d src/data-source.ts", "migration:run": "npm run typeorm migration:run", "migration:revert": "npm run typeorm migration:revert", "docker:build": "npm run build && npm run pkg && docker build -t samxtu/talisia-api:latest -f Dockerfile.pkg .", "docker:push": "docker push samxtu/talisia-api:latest", "docker:publish": "npm run docker:build && npm run docker:push", "docker:tag-version": "docker tag samxtu/talisia-api:latest samxtu/talisia-api:v%npm_package_version% && docker push samxtu/talisia-api:v%npm_package_version%"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@types/connect-redis": "^0.0.23", "@types/express": "^5.0.1", "@types/express-session": "1.17.0", "@types/lodash": "4.14.161", "@types/node": "14.11.1", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.0", "@types/pg": "^8.15.5", "@types/uuid": "^8.3.0", "class-validator": "0.12.2", "nodemon": "^2.0.4", "rimraf": "^6.0.1", "ts-node": "^10.9.2", "typescript": "^4.8.2"}, "dependencies": {"@types/jsonwebtoken": "^9.0.9", "apollo-server-express": "2.17.0", "bcryptjs": "^3.0.2", "class-transformer": "0.5.1", "connect-redis": "6.1.3", "cors": "^2.8.5", "date-fns": "^3.6.0", "dotenv": "^16.0.1", "express": "4.17.1", "express-session": "^1.17.1", "graphql": "^15.3.0", "graphql-type-json": "^0.3.2", "https": "^1.0.0", "ioredis": "^5.3.2", "jose": "2", "jsonwebtoken": "^9.0.0", "node-cron": "^3.0.3", "nodemailer": "^6.4.11", "pg": "^8.16.3", "reflect-metadata": "^0.1.13", "type-graphql": "^1.1.1", "typeorm": "^0.2.45", "uuid": "^8.3.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "resolutions": {"@types/express": "4.17.17"}, "pkg": {"targets": ["node16-linux-x64"], "outputPath": "bin", "assets": ["dist/**/*", "certificates/**/*", ".env"]}}