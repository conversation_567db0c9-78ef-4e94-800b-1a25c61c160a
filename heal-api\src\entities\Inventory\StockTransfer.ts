import { Field, ObjectType } from "type-graphql";
import { Column, Entity, Join<PERSON><PERSON>umn, ManyToOne } from "typeorm";
import { AuditBaseEntity } from "../AuditEntity";
import { Item } from "../Item";
import { Inventory } from "./Inventory";

@ObjectType()
@Entity()
export class Transfer extends AuditBaseEntity {
  @Field()
  @Column({ type: "uuid" })
  inventoryId!: string;

  @Field(() => Inventory)
  @ManyToOne(() => Inventory, (inventory) => inventory.transfers)
  @JoinColumn([
    { name: "inventoryId", referencedColumnName: "id" },
    { name: "companyId", referencedColumnName: "companyId" },
  ])
  inventoryTransfer!: Inventory;

  @Field()
  @Column({ type: "uuid" })
  itemId!: string;

  @Field()
  @Column({ type: "float", default: 0 })
  price: number;

  @Field(() => Item)
  @ManyToOne(() => Item, (item) => item.transfers)
  @JoinColumn([
    { name: "itemId", referencedColumnName: "id" },
    { name: "companyId", referencedColumnName: "companyId" },
  ])
  item!: Item;

  @Field()
  @Column({ default: true })
  granted: boolean;

  @Field()
  @Column({ default: true })
  received: boolean;

  @Field()
  @Column({ default: true })
  dispatched: boolean;

  @Field()
  @Column({ type: "float" })
  quantity!: number;

  @Field(() => String, { nullable: true })
  @Column({ type: "text", nullable: true })
  details: string; // Optional field to store reason for transfer OR transfer details

  @Field(() => String, { nullable: true })
  @Column({ type: "text", nullable: true })
  batch: string;
}
