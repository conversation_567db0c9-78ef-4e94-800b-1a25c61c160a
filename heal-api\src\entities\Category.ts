import { Field, ObjectType } from "type-graphql";
import {
  Column,
  <PERSON>tity,
  Index,
  JoinColumn,
  JoinTable,
  ManyToMany,
  ManyToOne,
} from "typeorm";
import { AuditBaseEntity } from "./AuditEntity";
import { Type } from "./Type";
import { User } from "./User";

@ObjectType()
@Entity()
export class Category extends AuditBaseEntity {
  @Field(() => String)
  @Column({ type: "text" })
  name!: string;

  @Field(() => String)
  @Column({ type: "uuid" })
  typeId!: string;

  @Field(() => Type)
  @ManyToOne(() => Type, (type) => type.category)
  @JoinColumn([
    { name: "typeId", referencedColumnName: "id" },
    { name: "companyId", referencedColumnName: "companyId" },
  ])
  type!: Type;

  @Field(() => String, { nullable: true })
  @Column({ type: "uuid", nullable: true })
  parentCategoryId: string;

  // A unique index on the name, type and company columns, this means I can't add the same name
  // for more than one category of the same type for the same company in SAAS (example, type "color" can not have two red categories)
  // but another type of lets say "pointer_color" can also have a red category

  @Index(["name", "typeId", "companyId"], { unique: true })
  @Column({ type: "text" })
  type_category!: string;

  @Field(() => [User], { nullable: true })
  @ManyToMany(() => User, (user) => user.status, { nullable: true })
  @JoinTable({
    name: "user_category",
    joinColumns: [
      { name: "categoryId", referencedColumnName: "id" },
      { name: "companyId", referencedColumnName: "companyId" },
    ],
    inverseJoinColumns: [
      { name: "userId", referencedColumnName: "id" },
      { name: "companyId", referencedColumnName: "companyId" },
    ],
  })
  user: User[];
}
