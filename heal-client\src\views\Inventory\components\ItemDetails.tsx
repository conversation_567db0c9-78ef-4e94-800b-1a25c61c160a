import { Box, Text, useColorModeValue, Grid, GridItem } from "@chakra-ui/react";
import { formatToMoney } from "../../../utils/Helpers";
import { Item } from "../../../generated/graphql";

const ItemDetails = ({ item }: { item: Item }) => {
  const textColor = useColorModeValue("gray.700", "white");
  const bgColor = useColorModeValue("white", "gray.700");

  return (
    <Box
      p={5}
      bg={bgColor}
      borderRadius="lg"
      boxShadow="md"
      border="1px solid"
      borderColor={useColorModeValue("gray.200", "gray.600")}
    >
      <Grid templateColumns="repeat(3, 1fr)" gap={6}>
        <GridItem>
          <Text fontSize="lg" fontWeight="bold" color={textColor}>
            <Box as="span" minWidth="120px" display="inline-block">
              Trade Name:
            </Box>
            {item.name}
          </Text>
          <Text fontSize="md" color={textColor}>
            <Box as="span" minWidth="120px" display="inline-block">
              Generic Name:
            </Box>
            {item.description}
          </Text>
          <Text fontSize="md" color={textColor}>
            <Box as="span" minWidth="120px" display="inline-block">
              Manufacturer:
            </Box>
            {item.reference}
          </Text>
          <Text fontSize="md" color={textColor}>
            <Box as="span" minWidth="120px" display="inline-block">
              Item Type:
            </Box>
            {item.type}
          </Text>
        </GridItem>

        <GridItem>
          <Text fontSize="md" color={textColor}>
            <Box as="span" minWidth="150px" display="inline-block">
              Reorder Level:
            </Box>
            {item.reorder}
          </Text>
          {item.pieces !== null &&
            item.pieces !== undefined &&
            item.pieces > 0 && (
              <Text fontSize="md" color={textColor}>
                <Box as="span" minWidth="150px" display="inline-block">
                  {item.pieceUnit} in 1 {item.unit}:
                </Box>
                {item.pieces}
              </Text>
            )}
          {item.subPieces !== null &&
            item.subPieces !== undefined &&
            item.subPieces > 0 && (
              <Text fontSize="md" color={textColor}>
                <Box as="span" minWidth="150px" display="inline-block">
                  {item.subPieceUnit} in 1 {item.pieceUnit}:
                </Box>
                {item.subPieces}
              </Text>
            )}
        </GridItem>

        <GridItem>
          <Text fontSize="md" color={textColor}>
            <Box as="span" minWidth="150px" display="inline-block">
              {item.unit} Stock:
            </Box>
            <Box as="span" minWidth="100px" display="inline-block">
              {item.stock} {item.unit}
            </Box>
            {item.sellingPrice && (
              <Box as="span" display="inline-block">
                ({formatToMoney(item.sellingPrice)} @1 {item.unit})
              </Box>
            )}
          </Text>
          {item.pieceUnit && (
            <Text fontSize="md" color={textColor}>
              <Box as="span" minWidth="150px" display="inline-block">
                + {item.pieceUnit} Stock:
              </Box>
              <Box as="span" minWidth="100px" display="inline-block">
                {item.pieceStock} {item.pieceUnit}
              </Box>
              <Box as="span" display="inline-block">
                (
                {formatToMoney(
                  item.pieceSellingPrice ? item.pieceSellingPrice : 0
                )}{" "}
                @1 {item.pieceUnit})
              </Box>
            </Text>
          )}
          {item.subPieceUnit && (
            <Text fontSize="md" color={textColor}>
              <Box as="span" minWidth="150px" display="inline-block">
                + {item.subPieceUnit} Stock:
              </Box>
              <Box as="span" minWidth="100px" display="inline-block">
                {item.subPieceStock} {item.subPieceUnit}
              </Box>
              <Box as="span" display="inline-block">
                (
                {formatToMoney(
                  item.subPieceSellingPrice ? item.subPieceSellingPrice : 0
                )}{" "}
                @1 {item.subPieceUnit})
              </Box>
            </Text>
          )}
        </GridItem>
      </Grid>
    </Box>
  );
};

export default ItemDetails;
