import {
  Box,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Button,
  useColorModeValue,
} from "@chakra-ui/react";
import {
  BatchStock,
  StoreItemStock,
  Transfer,
} from "../../../generated/graphql";
import { toDateTime } from "../../../utils/Helpers";

const RecentTransfersTable = ({
  transfers,
  batches,
  stores,
  onView,
}: {
  transfers: Transfer[];
  batches: BatchStock[];
  stores: StoreItemStock[];
  onView: (batch: BatchStock) => void;
}) => {
  const borderColor = useColorModeValue("gray.200", "gray.600");
  const textColor = useColorModeValue("gray.700", "white");

  const handleViewBatch = (batchNumber: string) => {
    const foundBatch = batches.find((batch) => batch.batch === batchNumber);
    if (foundBatch) {
      onView(foundBatch);
    }
  };

  const findStoreById = (storeId: number) => {
    return (
      stores.find((store) => store.storeId === storeId)?.store.name ||
      "Unknown Store"
    );
  };

  return (
    <Box>
      <Table variant="simple" color={textColor}>
        <Thead>
          <Tr>
            <Th borderColor={borderColor}>Date</Th>
            <Th borderColor={borderColor}>Source Store</Th>
            <Th borderColor={borderColor}>Destination Store</Th>
            <Th borderColor={borderColor}>Batch Number</Th>
            <Th borderColor={borderColor}>Quantity</Th>
          </Tr>
        </Thead>
        <Tbody>
          {transfers.map((transfer) => {
            const source = transfer.inventoryTransfer.sourceStoreId
              ? transfer.inventoryTransfer.sourceStoreId
              : 0;
            const destination = transfer.inventoryTransfer.destinationStoreId
              ? transfer.inventoryTransfer.destinationStoreId
              : 0;
            return (
              <Tr key={transfer.id}>
                <Td borderColor={borderColor}>
                  {toDateTime(Number(transfer.inventoryTransfer.updatedAt))}
                </Td>
                <Td borderColor={borderColor}>{findStoreById(source)}</Td>
                <Td borderColor={borderColor}>{findStoreById(destination)}</Td>
                <Td borderColor={borderColor}>
                  <Button onClick={() => handleViewBatch(transfer.batch)}>
                    {transfer.batch}
                  </Button>
                </Td>
                <Td borderColor={borderColor}>
                  {transfer.quantity > 0
                    ? transfer.quantity + " " + transfer.item.unit
                    : " "}
                  {transfer.pieceQuantity > 0
                    ? transfer.pieceQuantity + " " + transfer.item.pieceUnit
                    : ""}
                  {transfer.subPieceQuantity > 0
                    ? transfer.subPieceQuantity +
                      " " +
                      transfer.item.subPieceUnit
                    : ""}
                </Td>
              </Tr>
            );
          })}
        </Tbody>
      </Table>
    </Box>
  );
};

export default RecentTransfersTable;
