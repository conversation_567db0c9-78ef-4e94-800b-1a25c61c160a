# PowerSync Self-Hosting Configuration

This directory contains the configuration files for self-hosting PowerSync with the Talisia API.

## Files

- `config.yaml` - Main PowerSync configuration file
- `.env.example` - Example environment variables file
- `setup-storage.sql` - SQL script to set up PowerSync storage user
- `README.md` - This file

## Setup Instructions

### 1. Environment Variables

Copy the example environment file and update the values:

```bash
cp powersync/.env.example powersync/.env
```

Edit `powersync/.env` and update the following variables:

- `PS_POSTGRES_USER` - Your PostgreSQL username
- `PS_POSTGRES_PASSWORD` - Your PostgreSQL password
- `PS_POSTGRES_DB` - Your database name
- `PS_STORAGE_USER` - PowerSync storage user (default: powersync_storage_user)
- `PS_STORAGE_PASSWORD` - Password for PowerSync storage user
- `PS_JWT_SECRET` - Strong JWT secret for client authentication

### 2. Database Setup

Run the storage setup script to create the PowerSync storage user:

```bash
# Connect to your PostgreSQL database and run:
psql -U postgres -d your_database -f powersync/setup-storage.sql
```

Or manually create the user:

```sql
CREATE USER powersync_storage_user WITH PASSWORD 'your_secure_password';
GRANT CREATE ON DATABASE postgres TO powersync_storage_user;
```

### 3. Source Database Configuration

Ensure your source database (PostgreSQL) is configured for replication:

1. Enable logical replication in `postgresql.conf`:
   ```
   wal_level = logical
   max_replication_slots = 4
   max_wal_senders = 4
   ```

2. Update `pg_hba.conf` to allow replication connections:
   ```
   host replication postgres 0.0.0.0/0 md5
   ```

3. Restart PostgreSQL service

### 4. Start PowerSync

The PowerSync service is included in the main docker-compose.yml file. Start it with:

```bash
docker-compose up -d powersync
```

### 5. Verify Setup

Check PowerSync logs:

```bash
docker-compose logs powersync
```

Check health endpoint:

```bash
curl http://localhost:8080/api/health
```

## Configuration Details

### Sync Rules

The configuration includes multi-tenant sync rules that filter data by `companyId` from the JWT token. Each client only receives data for their company.

### Authentication

PowerSync is configured to use JWT authentication with HMAC signing. The JWT must include a `companyId` claim for multi-tenant data filtering.

### Storage

PowerSync uses PostgreSQL as the storage backend for sync buckets. For Postgres 14+, you can use the same server as your source database.

## Troubleshooting

### Common Issues

1. **Connection refused**: Check that PostgreSQL is running and accessible
2. **Authentication failed**: Verify database credentials in environment variables
3. **Permission denied**: Ensure the PowerSync storage user has proper permissions
4. **Replication issues**: Check PostgreSQL replication configuration

### Logs

PowerSync logs are available via Docker:

```bash
docker-compose logs -f powersync
```

### Health Checks

PowerSync includes health check endpoints:

- `/api/health` - General health status
- `/api/health/ready` - Readiness check

## Production Considerations

1. **Security**: Use strong, unique passwords and JWT secrets
2. **SSL/TLS**: Enable SSL for database connections in production
3. **Monitoring**: Set up monitoring for PowerSync service health
4. **Backup**: Ensure both source and storage databases are backed up
5. **Scaling**: Consider separate servers for source and storage databases

## Support

For PowerSync-specific issues, refer to:
- [PowerSync Documentation](https://docs.powersync.com/)
- [PowerSync GitHub](https://github.com/powersync-ja)
- [PowerSync Discord](https://discord.com/invite/powersync)
