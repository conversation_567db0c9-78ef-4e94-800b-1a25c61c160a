import { Field, ObjectType } from "type-graphql";
import { Column, Entity, OneToMany, Unique } from "typeorm";
import { AuditBaseEntity } from "./AuditEntity";
import { Category } from "./Category";

@ObjectType()
@Entity()
@Unique(["name", "companyId"]) // Ensure the combination of name and companyId is unique
export class Type extends AuditBaseEntity {
  @Field(() => String)
  @Column({ type: "text" })
  name!: string;

  @Field(() => String)
  @Column({ type: "text" })
  description: string;

  @Field(() => [Category], { nullable: true })
  @OneToMany(() => Category, (category) => category.type)
  category: Category[];
}
