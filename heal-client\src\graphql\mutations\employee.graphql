mutation addDepartment($params: DepartmentInputArgs!) {
  addDepartment(params: $params) {
    error {
      target
      message
    }
    status
  }
}

mutation editDepartment($id: Float!, $params: DepartmentInputArgs!) {
  editDepartment(id: $id, params: $params) {
    error {
      target
      message
    }
    status
  }
}
mutation addClinic($params: ClinicInputArgs!) {
  addClinic(params: $params) {
    error {
      target
      message
    }
    status
    clinic {
      id
      name
      description
      status
      clinicType
      size
    }
  }
}

mutation editClinic($id: Int!, $params: ClinicEditArgs!) {
  editClinic(id: $id, params: $params) {
    error {
      target
      message
    }
    status
    clinic {
      id
      name
      description
      status
      clinicType
      size
    }
  }
}

mutation deleteClinic($id: Int!) {
  deleteClinic(id: $id) {
    error {
      target
      message
    }
    status
  }
}

mutation SetHeadOfDepartment($departmentId: Int!, $employeeId: Int!) {
  setHeadOfDepartment(departmentId: $departmentId, employeeId: $employeeId) {
    status
    error {
      target
      message
    }
  }
}
