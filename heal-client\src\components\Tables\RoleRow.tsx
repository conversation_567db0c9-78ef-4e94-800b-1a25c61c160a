import {
  Box,
  Button,
  Flex,
  Icon,
  Text,
  useColorModeValue,
} from "@chakra-ui/react";
import React from "react";
import { FaPencilAlt, FaTrashAlt } from "react-icons/fa";

interface IRoleRowProps {
  name: string;
  key: number;
  editActivated: (name: string) => void;
  callOnClick: () => void;
}

const RoleRow: React.FC<IRoleRowProps> = (props: any) => {
  const textColor = useColorModeValue("gray.700", "white");
  const bgColor = useColorModeValue("#F8F9FA", "navy.900");
  const nameColor = useColorModeValue("gray.500", "white");
  const { name, editActivated, callOnClick } = props;

  return (
    <Box py="10px" px={20} bg={bgColor} my="10px" borderRadius="12px">
      <Flex justify="space-between" w="100%">
        <Flex maxWidth="70%">
          <Text color={nameColor} fontSize="xl" mt={7} fontWeight="bold">
            {name}
          </Text>
        </Flex>
        <Flex
          direction={{ sm: "column", md: "row" }}
          align="flex-start"
          p={{ md: "24px" }}
        >
          <Button
            p="0px"
            bg="transparent"
            variant="no-effects"
            mb={{ sm: "10px", md: "0px" }}
            me={{ md: "12px" }}
            onClick={callOnClick}
          >
            <Flex color="red.200" cursor="pointer" align="center" p="12px">
              <Icon as={FaTrashAlt} me="4px" />
              <Text fontSize="sm" fontWeight="semibold">
                DELETE
              </Text>
            </Flex>
          </Button>
          <Button
            p="0px"
            bg="transparent"
            variant="no-effects"
            onClick={() => editActivated(name)}
          >
            <Flex color={textColor} cursor="pointer" align="center" p="12px">
              <Icon as={FaPencilAlt} me="4px" />
              <Text fontSize="sm" fontWeight="semibold">
                EDIT
              </Text>
            </Flex>
          </Button>
        </Flex>
      </Flex>
    </Box>
  );
};

export default RoleRow;
