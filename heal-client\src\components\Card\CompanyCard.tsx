// Chakra imports
import {
  Avatar,
  Badge,
  Box,
  Flex,
  Icon,
  Image,
  Stack,
  Text,
  useColorMode,
  useColorModeValue,
} from "@chakra-ui/react";
// import { FaCube } from "react-icons/fa";
// // Custom components
// import { Link } from "react-router-dom";

import adminbg from "../../assets/img/admin-background.jpg";
import {
  DispensaryIcon,
  HealthCenterIcon,
  HospitalIcon,
  KitMedicalIcon,
  PharmacyIcon,
  ReferralHospitalIcon,
} from "../Icons/Icons";
import { Company } from "../../generated/graphql";
// import ImageArchitect3 from "../../assets/img/ImageArchitect3.png";

type ICompanyListCardProps = React.InputHTMLAttributes<HTMLInputElement> & {
  data: Company;
};

export const CompanyListCard: React.FC<ICompanyListCardProps> = ({ data }) => {
  console.log("data passed is: ", data);
  // const history = useHistory();
  const { colorMode } = useColorMode();
  const textColor = useColorModeValue("gray.700", "white");

  return (
    <Flex direction="column">
      <Box mb="20px" position="relative" borderRadius="15px">
        <Image src={adminbg} borderRadius="15px" />
        <Flex mt={-110} justify={"center"}>
          {/* <Avatar
            size={"xl"}
            src={ImageArchitect2}
            css={{
              border: "2px solid white",
            }}
          /> */}

          <Avatar
            size={"xl"}
            css={{
              border: "2px solid white",
            }}
            bg={colorMode === "dark" ? "navy.900" : "#fff"}
            icon={
              <Icon
                color={colorMode === "dark" ? "cyan" : "coral"}
                as={
                  data.type === "HOSPITAL"
                    ? HospitalIcon
                    : data.type === "DISPENSARY"
                    ? DispensaryIcon
                    : data.type === "HEALTH CENTER"
                    ? HealthCenterIcon
                    : data.type === "REFERRAL"
                    ? ReferralHospitalIcon
                    : data.type === "PHARMACY"
                    ? PharmacyIcon
                    : KitMedicalIcon
                }
                // me="6px"
                fontSize={60}
              />
            }
          />
        </Flex>

        <Box
          w="100%"
          h="100%"
          position="absolute"
          top="15px"
          borderRadius="15px"
          bg="linear-gradient(360deg, rgba(49, 56, 96, 0.16) 0%, rgba(21, 25, 40, 0.88) 100%)"
        ></Box>
      </Box>

      <Flex direction="column">
        <Flex
          align="center"
          w={{ sm: "100%", lg: "135px" }}
          bg={colorMode === "dark" ? "navy.900" : "#fff"}
          borderRadius="8px"
          justifyContent="center"
          py="10px"
          boxShadow="0px 2px 5.5px rgba(0, 0, 0, 0.06)"
        >
          <Icon
            color={colorMode === "dark" ? "cyan" : "coral"}
            as={
              data.type === "HOSPITAL"
                ? HospitalIcon
                : data.type === "DISPENSARY"
                ? DispensaryIcon
                : data.type === "HEALTH CENTER"
                ? HealthCenterIcon
                : data.type === "REFERRAL"
                ? ReferralHospitalIcon
                : data.type === "PHARMACY"
                ? PharmacyIcon
                : KitMedicalIcon
            }
            me="6px"
          />
          <Text
            fontSize="md"
            color={colorMode === "dark" ? "cyan" : "coral"}
            fontWeight="bold"
          >
            {data.type ? data.type : "Dispensary"}
          </Text>
        </Flex>
        <Text fontSize="xl" color={textColor} fontWeight="bold" mb="10px">
          {data.name ? data.name : "Name of company"}
        </Text>
        <Flex direction="column">
          <Flex align="center" mb="18px">
            <Text fontSize="md" color={textColor} fontWeight="bold" me="10px">
              Location:{" "}
            </Text>
            <Text fontSize="md" color="gray.400" fontWeight="400">
              {data.location
                ? data.location.city + ", " + data.location.district
                : "Location here"}
            </Text>
          </Flex>
          <Flex align="center" mb="18px">
            <Text fontSize="md" color={textColor} fontWeight="bold" me="10px">
              Status:{" "}
            </Text>
            <Stack direction="row">
              <Badge variant="outline" colorScheme="green">
                Active
              </Badge>
              <Badge variant="solid" colorScheme="green">
                Paid
              </Badge>
              <Badge variant="subtle" colorScheme="green">
                Online
              </Badge>
            </Stack>
          </Flex>
        </Flex>
      </Flex>
    </Flex>
  );
};
