import { Link, useHistory, useLocation } from "react-router-dom";
import { FaBuilding } from "react-icons/fa";
import {
  Clinic,
  useGetAllServicesQuery,
  useGetSchedulesQuery,
} from "../../generated/graphql";
import {
  Flex,
  Icon,
  Table,
  Tbody,
  Text,
  Th,
  Thead,
  Tr,
  Box,
  Grid,
  GridItem,
  Button,
  useColorModeValue,
  Td,
  VStack,
} from "@chakra-ui/react";
import { formatToMoney } from "../../utils/Helpers";
import ScheduleCalendar from "./Components/Schedule";

interface LocationState {
  clinic: Clinic;
}

function ViewClinic() {
  const [{ data, fetching }] = useGetAllServicesQuery({
    requestPolicy: "network-only",
  });

  // absorb the state from router and leave if the page comes from non assigned route
  const location = useLocation<{ clinic: Clinic }>();
  const history = useHistory();
  if (!location.state) history.push("departments");
  const { clinic } = location.state as LocationState;
  if (!clinic || !clinic.id || clinic.id <= 0) history.push("departments");

  const [{ data: schedules }] = useGetSchedulesQuery({
    variables: {
      owner: "clinic",
      ownerId: clinic.id,
    },
    requestPolicy: "network-only",
  });
  const textColor = useColorModeValue("gray.700", "white");
  const borderColor = useColorModeValue("gray.200", "none");
  const bgColor = useColorModeValue("white", "gray.600");

  return (
    <Flex direction="column" pt={{ base: "120px", md: "75px" }}>
      {clinic && clinic.id > 0 && (
        <Box
          p={5}
          bg={bgColor}
          borderRadius="lg"
          boxShadow="md"
          border="1px solid"
          borderColor={borderColor}
          mb={5}
        >
          <Grid templateColumns="repeat(3, 1fr)" gap={6}>
            <GridItem>
              <Text fontSize="2xl" fontWeight="bold" color={textColor}>
                <Icon as={FaBuilding} mr={2} />
                Clinic Details
              </Text>
            </GridItem>
            <GridItem>
              <Text fontSize="lg" fontWeight="bold" color={textColor}>
                <Box as="span" minWidth="120px" display="inline-block">
                  Name:
                </Box>
                {clinic.name}
              </Text>
              <Text fontSize="md" color={textColor}>
                <Box as="span" minWidth="120px" display="inline-block">
                  Description:
                </Box>
                {clinic.description}
              </Text>
              <Text fontSize="md" color={textColor}>
                <Box as="span" minWidth="120px" display="inline-block">
                  Clinic Type:
                </Box>
                {clinic.clinicType}
              </Text>
              <Text fontSize="md" color={textColor}>
                <Box as="span" minWidth="120px" display="inline-block">
                  Size:
                </Box>
                {clinic.size}
              </Text>
              <Text fontSize="md" color={textColor}>
                <Box as="span" minWidth="120px" display="inline-block">
                  Status:
                </Box>
                <Box
                  as="span"
                  fontWeight="bold"
                  color={clinic.status === "active" ? "green.400" : "red.400"}
                >
                  {clinic.status === "active" ? "Active" : "Inactive"}
                </Box>
              </Text>
            </GridItem>
            <GridItem textAlign="right">
              <VStack>
                <Button
                  colorScheme="teal"
                  as={Link}
                  to={{
                    pathname: "edit-clinic",
                    state: {
                      clinic,
                    },
                  }}
                >
                  Update Clinic
                </Button>
                {schedules &&
                schedules.getSchedules &&
                schedules.getSchedules.length > 0 ? (
                  <Button
                    colorScheme="blue"
                    as={Link}
                    to={{
                      pathname: "edit-schedule",
                      state: {
                        clinic,
                      },
                    }}
                  >
                    Update Schedule
                  </Button>
                ) : (
                  <Button
                    colorScheme="blue"
                    as={Link}
                    to={{
                      pathname: "add-schedule",
                      state: {
                        clinic,
                      },
                    }}
                  >
                    Add Schedule
                  </Button>
                )}
              </VStack>
            </GridItem>
          </Grid>
        </Box>
      )}
      {schedules && schedules.getSchedules && (
        <ScheduleCalendar schedules={schedules?.getSchedules} />
      )}
      <Table variant="simple" color={textColor} mt={3}>
        <Thead>
          <Tr>
            <Th
              colSpan={5} // Adjust colSpan to match the number of columns in your table
              fontSize="xl" // Reduced font size
              textAlign="center"
              color={textColor} // Dynamic text color based on the mode
              py={4} // Reduced padding
              bg={bgColor} // Dynamic background color based on the mode
              borderRadius="md"
              letterSpacing="widest"
              border={`1px solid ${borderColor}`} // Dynamic border color based on the mode
            >
              Services Provided
            </Th>
          </Tr>
        </Thead>

        <Thead>
          <Tr my=".8rem" pl="0px" color="gray.400">
            <Th pl="0px" borderColor={borderColor} color="gray.400">
              Name
            </Th>
            <Th pl="0px" borderColor={borderColor} color="gray.400">
              Details
            </Th>
            <Th pl="0px" borderColor={borderColor} color="gray.400">
              Reference
            </Th>
            <Th borderColor={borderColor} color="gray.400">
              Service charge
            </Th>
          </Tr>
        </Thead>
        <Tbody>
          {!fetching &&
            data?.getAllServices &&
            data?.getAllServices.map((service) => (
              <Tr>
                <Td borderColor={borderColor}>
                  <Text color={textColor} fontSize="sm" fontWeight="bold">
                    {service.name}
                  </Text>
                </Td>
                <Td borderColor={borderColor}>
                  <Text color={textColor} fontSize="sm">
                    {service.description}
                  </Text>
                </Td>
                <Td borderColor={borderColor}>
                  <Text color={textColor} fontSize="sm">
                    {service.reference}
                  </Text>
                </Td>
                <Td borderColor={borderColor}>
                  <Text color={textColor} fontSize="sm">
                    {formatToMoney(service.sellingPrice)}
                  </Text>
                </Td>
              </Tr>
            ))}
        </Tbody>
      </Table>
    </Flex>
  );
}

export default ViewClinic;
