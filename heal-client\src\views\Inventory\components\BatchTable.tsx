import {
  Box,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Button,
  useColorModeValue,
} from "@chakra-ui/react";
import { BatchStock, Item } from "../../../generated/graphql";

const BatchTable = ({
  item,
  onView,
  batches,
}: {
  item: Item;
  onView: any;
  batches: BatchStock[];
}) => {
  const borderColor = useColorModeValue("gray.200", "gray.600");
  const textColor = useColorModeValue("gray.700", "white");

  return (
    <Box>
      <Table variant="simple" color={textColor}>
        <Thead>
          <Tr>
            <Th borderColor={borderColor}>Batch Identifier</Th>
            <Th borderColor={borderColor}>Stock in {item.unit}</Th>
            {item.pieceUnit && (
              <Th borderColor={borderColor}>Stock in {item.pieceUnit}</Th>
            )}
            {item.subPieceUnit && (
              <Th borderColor={borderColor}>Stock in {item.subPieceUnit}</Th>
            )}
            <Th borderColor={borderColor}>
              {item.type === "consumable" ? "Expire" : "Maintenance"} Date
            </Th>
            <Th borderColor={borderColor}></Th>
          </Tr>
        </Thead>
        <Tbody>
          {batches.map((batch) => (
            <Tr key={batch.id}>
              <Td borderColor={borderColor}>{batch.batch}</Td>
              <Td borderColor={borderColor}>{batch.stock}</Td>
              {item.pieceUnit && (
                <Td borderColor={borderColor}>{batch.pieceStock}</Td>
              )}
              {item.subPieceUnit && (
                <Td borderColor={borderColor}>{batch.subPieceStock}</Td>
              )}
              <Td borderColor={borderColor}>{batch.expireDate}</Td>
              <Td borderColor={borderColor}>
                <Button onClick={() => onView(batch)}>View</Button>
              </Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
    </Box>
  );
};

export default BatchTable;
