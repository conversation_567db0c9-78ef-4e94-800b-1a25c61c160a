import { Field, ObjectType } from "type-graphql";
import { Column, Enti<PERSON>, Join<PERSON><PERSON>umn, ManyToOne, OneToMany } from "typeorm";
import { AuditBaseEntity } from "../AuditEntity";
import { Item } from "../Item";
import { StoreItemStock } from "./StoreItemStock";

@ObjectType()
@Entity()
export class BatchStock extends AuditBaseEntity {
  @Field()
  @Column({ type: "uuid" })
  itemId!: string;

  @Field(() => String)
  @Column({ type: "text" })
  batch!: string;

  @Field(() => String)
  @Column({ type: "text" })
  expireDate: String; // track expiration date for consumables and maintenance dates for capital goods

  @Field()
  @Column({ type: "float", default: 0 })
  stock!: number;

  @Field(() => Item)
  @ManyToOne(() => Item, (item) => item.batchStocks)
  @JoinColumn([
    { name: "itemId", referencedColumnName: "id" },
    { name: "companyId", referencedColumnName: "companyId" },
  ])
  item: Item;

  @Field(() => [StoreItemStock], { nullable: true })
  @OneToMany(
    () => StoreItemStock,
    (storeItemStock) => storeItemStock.batchStock
  )
  storeItemStocks: StoreItemStock[];
}
