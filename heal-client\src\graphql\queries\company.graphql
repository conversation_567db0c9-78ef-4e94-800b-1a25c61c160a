query getCompany($id: Float!) {
  getCompany(id: $id) {
    id
    name
    tinNumber
    registrationNumber
    type
    employees {
      id
      role {
        name
      }
    }
    features {
      id
      name
    }
    location {
      id
      city
      district
      ward
      street
      zip
    }
  }
}

query getCompanies($types: [String!]) {
  getCompanies(types: $types) {
    id
    name
    tinNumber
    registrationNumber
    type
    location {
      id
      city
      district
      ward
      street
      zip
    }
  }
}

query getSchedules($ownerId: Int!, $owner: String!) {
  getSchedules(ownerId: $ownerId, owner: $owner) {
    id
    onTime
    offTime
    day
    description
    clinicId
    employeeId
  }
}
