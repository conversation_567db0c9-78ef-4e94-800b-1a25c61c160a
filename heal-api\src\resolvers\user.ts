import { User } from "../entities/User";
import { signPowerSyncJwt } from "../powersync/jwks";
import { MyContext } from "../types";
import {
  Resolver,
  Mutation,
  Query,
  Ctx,
  Arg,
  InputType,
  Field,
  ObjectType,
  UseMiddleware,
  Float,
} from "type-graphql";
import {
  COOKIE_NAME,
  FORGET_PASSWORD_PREFIX,
  FRONT_END_ORIGIN,
} from "../constants.js";
import { sendEmail } from "../utils/sendEmail";
import { v4 } from "uuid";
import { isAuth } from "../middleware/isAuth";
import { getConnection, In } from "typeorm";
import { isAllowed } from "../middleware/isAllowed";
import { Permission } from "../entities/Permission";
import { Role } from "../entities/Role";
import { Employee } from "../entities/Employee";
import { Department } from "../entities/Department";
import { logError } from "../utils/utils";
import { generateToken } from "../utils/jwt";
import { Store } from "../entities/Inventory/Store";
import { dragon } from "../utils/bcrypt-wrapper.js";

@ObjectType()
export class FieldError {
  @Field(() => String)
  target?: string;
  @Field(() => String)
  message: string;
}

@InputType()
class AssignPasswordArgs {
  @Field(() => String)
  userId: string;
  @Field(() => String)
  companyId: string;
  @Field(() => String)
  newPassword: string;
}

@InputType()
class RegisterEmployeeArgs {
  @Field(() => String)
  firstname: string;
  @Field(() => String)
  middlename: string;
  @Field(() => String)
  lastname: string;
  @Field(() => String)
  email: string;
  @Field(() => String)
  phone: string;
  @Field(() => String)
  password: string;
  @Field(() => String, { nullable: true })
  companyRole: string;
  @Field(() => String, { nullable: true })
  store: string;
  @Field(() => String)
  designation: string;
  @Field(() => String)
  licenseNumber: string;
  @Field(() => String, { nullable: true })
  department: string;
}

@InputType()
class EmailPasswordArgs {
  @Field(() => String)
  email: string;
  @Field(() => String)
  password: string;
}

@ObjectType()
export class BooleanResponse {
  @Field()
  status: boolean;
  @Field(() => FieldError, { nullable: true })
  error?: FieldError;
}

@ObjectType()
class UndetailedUser {
  @Field(() => String)
  firstname: string;
  @Field(() => String)
  lastname: string;
  @Field(() => String)
  email: string;
  @Field(() => String, { nullable: true })
  image: string;
}

@InputType()
class RegisterUserArgs {
  @Field(() => String)
  firstname: string;
  @Field(() => String)
  middlename: string;
  @Field(() => String)
  lastname: string;
  @Field(() => String)
  email: string;
  @Field(() => String)
  phone: string;
  @Field(() => String)
  password: string;
  @Field(() => String)
  companyId: string;
}

@InputType()
class EditUserArgs {
  @Field(() => String)
  firstname: string;
  @Field(() => String)
  middlename: string;
  @Field(() => String)
  lastname: string;
  @Field(() => String)
  email: string;
  @Field(() => String)
  phone: string;
  @Field(() => String)
  image: string;
}

@ObjectType()
class UserResponse {
  @Field(() => User, { nullable: true })
  user?: User;

  @Field(() => String, { nullable: true })
  token?: string;

  @Field(() => FieldError, { nullable: true })
  error?: FieldError;
}

@Resolver(User)
export class UserResolver {
  @Mutation(() => BooleanResponse)
  async forgotPassword(
    @Arg("email") email: string,
    @Ctx() { redis }: MyContext
  ): Promise<BooleanResponse> {
    const user = await User.findOne({ where: { email } });
    if (user) {
      const token = v4();
      const key = FORGET_PASSWORD_PREFIX + token;
      try {
        redis.set(key, user.id, "EX", 1000 * 60 * 60 * 24 * 3); //3 days expiry time
        sendEmail(
          user.email,
          "Reset email here",
          `<a href='${FRONT_END_ORIGIN}/reset-email/${token}'>Click this link</a>`
        );
      } catch (err) {
        logError(
          user.companyId,
          err.message,
          "FORGOT_PASSWORD_ERROR",
          JSON.stringify(err),
          "high",
          `Failed to process forgot password for user: ${user.email}`,
          user.id
        );
        console.error(err.message);
        return {
          status: false,
          error: { target: "general", message: err.message },
        };
      }
    }
    return { status: true };
  }

  @Mutation(() => UserResponse)
  async resetPassword(
    @Arg("newPassword") newPassword: string,
    @Arg("token") token: string,
    @Ctx() { req, redis }: MyContext
  ): Promise<UserResponse> {
    const key = FORGET_PASSWORD_PREFIX + token;
    const userId = await redis.get(key);
    if (!userId) {
      logError(
        "",
        "Token expired or invalid",
        "RESET_PASSWORD_INVALID_TOKEN",
        JSON.stringify({ token }),
        "medium",
        "Reset password failed - invalid token"
      );
      return {
        error: {
          target: "Token",
          message: "Token expired, try forgot password again!",
        },
      };
    }

    let clumsyUser: User | undefined = undefined;
    const idNew: string = userId;
    try {
      clumsyUser = await User.findOne(idNew, { relations: ["role", "branch"] });
    } catch (err) {
      logError(
        "",
        err.message,
        "RESET_PASSWORD_USER_FETCH_ERROR",
        JSON.stringify({ userId: idNew, error: err }),
        "high",
        `Failed to fetch user for password reset: ${idNew}`
      );
      console.error(err);
    }
    await redis.del(key);

    if (!clumsyUser) {
      logError(
        "",
        "User not found",
        "RESET_PASSWORD_USER_NOT_FOUND",
        JSON.stringify({ userId: idNew }),
        "medium",
        `Reset password failed - user not found: ${idNew}`
      );
      return {
        error: {
          target: "general",
          message: "User not found!",
        },
      };
    }

    try {
      const hashedpass = await dragon.hash(newPassword);
      clumsyUser.password = hashedpass;
      await User.update({ id: idNew }, { password: hashedpass });
      req.session.userId = clumsyUser.id;
      req.session.companyId = clumsyUser.companyId;
      return { user: clumsyUser };
    } catch (err) {
      logError(
        clumsyUser.companyId,
        err.message,
        "RESET_PASSWORD_UPDATE_ERROR",
        JSON.stringify({ userId: idNew, error: err }),
        "high",
        `Failed to update password for user: ${idNew}`,
        idNew
      );
      return {
        error: {
          target: "general",
          message: "Failed to update password",
        },
      };
    }
  }

  @Mutation(() => BooleanResponse)
  async register(
    @Arg("params") params: RegisterUserArgs
  ): Promise<BooleanResponse> {
    try {
      const hashedPassword = await dragon.hash(params.password);
      const adminRole = await Role.create({
        name: "admin",
        companyId: params.companyId,
      }).save();
      if (!adminRole) {
        logError(
          params.companyId,
          "Failed to create admin role",
          "REGISTER_ADMIN_ROLE_ERROR",
          JSON.stringify({ companyId: params.companyId }),
          "high",
          `Failed to create admin role for company: ${params.companyId}`
        );
        throw new Error("Admin role not created");
      }
      await User.create({
        firstname: params.firstname,
        middlename: params.middlename ? params.middlename : "",
        lastname: params.lastname,
        email: params.email.toLowerCase(),
        phone: params.phone,
        companyId: params.companyId,
        password: hashedPassword,
        roleId: adminRole.id,
      }).save();
    } catch (err) {
      if (err.code === "23505") {
        logError(
          params.companyId,
          "Username already exists",
          "REGISTER_DUPLICATE_USERNAME",
          JSON.stringify({ email: params.email }),
          "medium",
          `Registration failed - duplicate username: ${params.email}`
        );
        return {
          status: false,
          error: {
            target: "username",
            message: "username already taken!",
          },
        };
      }
      logError(
        params.companyId,
        err.message,
        "REGISTER_ERROR",
        JSON.stringify({ error: err, params }),
        "high",
        `Registration failed for user: ${params.email}`
      );
      console.error("error message: ", err.message);
      return {
        status: false,
        error: {
          target: "general",
          message: "Something went wrong, try again!",
        },
      };
    }
    return { status: true };
  }

  // Mutation to change an employee's status.
  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  @UseMiddleware(isAllowed(["admin"]))
  async changeEmployeeStatus(
    @Arg("employeeId", () => String) employeeId: string,
    @Arg("status") status: string,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    try {
      const employee = await Employee.findOne({
        where: { id: employeeId, companyId: req.session.companyId },
      });
      if (!employee) {
        logError(
          req.session.companyId,
          "Employee not found",
          "CHANGE_EMPLOYEE_STATUS_NOT_FOUND",
          JSON.stringify({ employeeId }),
          "medium",
          `Change status failed - employee not found: ${employeeId}`,
          req.session.userId
        );
        return {
          status: false,
          error: { target: "employee", message: "Employee not found" },
        };
      }
      employee.status = status;
      await employee.save();
      return { status: true };
    } catch (err: any) {
      logError(
        req.session.companyId,
        err.message,
        "CHANGE_EMPLOYEE_STATUS_ERROR",
        JSON.stringify({ error: err, employeeId, status }),
        "high",
        `Failed to change employee status: ${employeeId}`,
        req.session.userId
      );
      return {
        status: false,
        error: { target: "employee", message: err.message },
      };
    }
  }

  // Mutation to change an employee's company role and department.
  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  @UseMiddleware(isAllowed(["admin"]))
  async changeEmployeeRole(
    @Arg("employeeId", () => String) employeeId: string,
    @Arg("companyRole", () => String) companyRole: string,
    @Arg("departmentId", () => String) departmentId: string,
    @Arg("designation") designation: string,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    try {
      const employee = await Employee.findOne({
        where: { id: employeeId, companyId: req.session.companyId },
      });
      if (!employee) {
        logError(
          req.session.companyId,
          "Employee not found",
          "CHANGE_EMPLOYEE_ROLE_NOT_FOUND",
          JSON.stringify({ employeeId }),
          "medium",
          `Change role failed - employee not found: ${employeeId}`,
          req.session.userId
        );
        return {
          status: false,
          error: { target: "employee", message: "Employee not found" },
        };
      }
      employee.roleId = companyRole;
      employee.departmentId = departmentId;
      employee.designation = designation;
      await employee.save();
      return { status: true };
    } catch (err: any) {
      logError(
        req.session.companyId,
        err.message,
        "CHANGE_EMPLOYEE_ROLE_ERROR",
        JSON.stringify({
          error: err,
          employeeId,
          companyRole,
          departmentId,
          designation,
        }),
        "high",
        `Failed to change employee role: ${employeeId}`,
        req.session.userId
      );
      return {
        status: false,
        error: { target: "employee", message: err.message },
      };
    }
  }

  // Mutation to change a user's password.
  // This mutation assumes the currently logged in user is changing their password.
  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async changePassword(
    @Arg("currentPassword") currentPassword: string,
    @Arg("newPassword") newPassword: string,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    try {
      const userId = req.session.userId;
      const user = await User.findOne({
        where: { id: userId, companyId: req.session.companyId },
      });
      if (!user) {
        logError(
          req.session.companyId,
          "User not found",
          "CHANGE_PASSWORD_USER_NOT_FOUND",
          JSON.stringify({ userId }),
          "medium",
          `Change password failed - user not found: ${userId}`,
          userId
        );
        return {
          status: false,
          error: { target: "user", message: "User not found" },
        };
      }
      const valid = await dragon.verify(user.password, currentPassword);
      if (!valid) {
        logError(
          req.session.companyId,
          "Invalid current password",
          "CHANGE_PASSWORD_INVALID_CURRENT",
          JSON.stringify({ userId }),
          "medium",
          `Change password failed - invalid current password: ${userId}`,
          userId
        );
        return {
          status: false,
          error: {
            target: "currentPassword",
            message: "Current password is incorrect",
          },
        };
      }
      user.password = await dragon.hash(newPassword);
      await user.save();
      return { status: true };
    } catch (err: any) {
      logError(
        req.session.companyId,
        err.message,
        "CHANGE_PASSWORD_ERROR",
        JSON.stringify({ userId: req.session.userId, error: err }),
        "high",
        `Failed to change password for user: ${req.session.userId}`,
        req.session.userId
      );
      return {
        status: false,
        error: { target: "password", message: err.message },
      };
    }
  }

  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  @UseMiddleware(isAllowed(["admin", "employee"]))
  async registerEmployee(
    @Arg("params") params: RegisterEmployeeArgs,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    try {
      const hashedPassword = await dragon.hash(params.password);
      const companyId: string = req.session.companyId;
      const roles = await Role.find({ where: { companyId } });
      let employeeRole = roles.find((role) => role.name === "employee");

      if (!employeeRole) {
        employeeRole = await Role.create({
          name: "employee",
          companyId: companyId,
        }).save();
        if (!employeeRole) {
          logError(
            companyId,
            "Failed to create employee role",
            "REGISTER_EMPLOYEE_ROLE_ERROR",
            JSON.stringify({ companyId }),
            "high",
            `Employee registration failed - role creation failed`,
            req.session.userId
          );
          throw new Error("Employee role not created");
        }
      }

      // check if store exists
      if (params.store) {
        const store = await Store.findOne({
          id: params.store,
          companyId: companyId,
        });
        if (!store) {
          logError(
            companyId,
            "Store not found",
            "REGISTER_EMPLOYEE_STORE_ERROR",
            JSON.stringify({ storeId: params.store }),
            "medium",
            `Employee registration failed - store not found: ${params.store}`,
            req.session.userId
          );
          throw new Error("Store not found");
        }
      }
      let department: Department | undefined;
      if (params.department) {
        department = await Department.findOne({
          id: params.department,
          companyId: companyId,
        });
        if (!department) {
          logError(
            companyId,
            "Department not found",
            "REGISTER_EMPLOYEE_DEPARTMENT_ERROR",
            JSON.stringify({ departmentId: params.department }),
            "medium",
            `Employee registration failed - department not found: ${params.department}`,
            req.session.userId
          );
          throw new Error("Department not found");
        }
      }

      await getConnection().transaction(async (transactionalEntityManager) => {
        const user = await transactionalEntityManager.save(User, {
          firstname: params.firstname,
          middlename: params.middlename || "",
          lastname: params.lastname,
          email: params.email.toLowerCase(),
          phone: params.phone,
          companyId: companyId,
          password: hashedPassword,
          roleId: employeeRole!.id,
        });

        await transactionalEntityManager.save(Employee, {
          companyId,
          userId: user.id,
          roleId: employeeRole!.id,
          designation: params.designation,
          licenceNumber: params.licenseNumber,
          department: department,
          storeId: params.store,
        });
      });
    } catch (err) {
      if (err.code === "23505") {
        logError(
          req.session.companyId,
          "Duplicate user credentials",
          "REGISTER_EMPLOYEE_DUPLICATE",
          JSON.stringify({ email: params.email }),
          "medium",
          `Employee registration failed - duplicate credentials: ${params.email}`,
          req.session.userId
        );
        return {
          status: false,
          error: {
            target: "general",
            message: "user credentials already in the system!",
          },
        };
      }

      console.log("error: ", err);

      logError(
        req.session.companyId,
        err.message,
        "REGISTER_EMPLOYEE_ERROR",
        JSON.stringify({ error: err, params }),
        "high",
        `Employee registration failed: ${params.email}`,
        req.session.userId
      );
      return {
        status: false,
        error: {
          target: "general",
          message: "Something went wrong, try again!",
        },
      };
    }

    return { status: true };
  }

  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async editUser(
    @Arg("id", () => String) id: string,
    @Arg("params") params: EditUserArgs,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const user = await User.findOne({
      where: { id, companyId: req.session.companyId },
    });
    if (!user) {
      logError(
        req.session.companyId,
        "User not found",
        "EDIT_USER_NOT_FOUND",
        JSON.stringify({ userId: id }),
        "medium",
        `Edit user failed - user not found: ${id}`,
        req.session.userId
      );
      return {
        status: false,
        error: { target: "general", message: "User does not exist!" },
      };
    }

    try {
      await User.update(
        { id, companyId: req.session.companyId },
        { ...params }
      );
    } catch (err) {
      logError(
        req.session.companyId,
        err.message,
        "EDIT_USER_ERROR",
        JSON.stringify({ userId: id, error: err, params }),
        "high",
        `Failed to edit user: ${id}`,
        req.session.userId
      );
      return {
        status: false,
        error: {
          target: "general",
          message: "Something went wrong, try again!",
        },
      };
    }
    return { status: true };
  }

  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async manageUserPermissions(
    @Arg("id") id: string,
    @Arg("permissions", () => [String]) perms: string[],
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const user = await User.findOne({
      where: { id, companyId: req.session.companyId },
    });
    if (!user) {
      logError(
        req.session.companyId,
        "User not found",
        "MANAGE_PERMISSIONS_USER_NOT_FOUND",
        JSON.stringify({ userId: id }),
        "medium",
        `Manage permissions failed - user not found: ${id}`,
        req.session.userId
      );
      return {
        status: false,
        error: { target: "general", message: "User does not exist!" },
      };
    }

    try {
      const newPerms = await Permission.find({
        where: { id: In(perms), companyId: req.session.companyId },
      });
      user.permissions = newPerms;
      await user.save();
    } catch (err) {
      logError(
        req.session.companyId,
        err.message,
        "MANAGE_PERMISSIONS_ERROR",
        JSON.stringify({ userId: id, permissions: perms, error: err }),
        "high",
        `Failed to update user permissions: ${id}`,
        req.session.userId
      );
      console.error("error message: ", err.message);
      return {
        status: false,
        error: {
          target: "general",
          message: "Something went wrong, try again!",
        },
      };
    }
    return { status: true };
  }

  // Mutation to assign a password to a user.
  // This mutation allows an admin to set a password for any user in any company.
  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  @UseMiddleware(isAllowed(["admin"]))
  async assignPassword(
    @Arg("params") params: AssignPasswordArgs,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    try {
      const targetUser = await User.findOne({
        where: { id: params.userId, companyId: params.companyId },
      });

      if (!targetUser) {
        logError(
          params.companyId,
          "Target user not found",
          "ASSIGN_PASSWORD_USER_NOT_FOUND",
          JSON.stringify({ targetUserId: params.userId }),
          "medium",
          `Assign password failed - user not found: ${params.userId}`,
          params.userId
        );
        return {
          status: false,
          error: { target: "user", message: "User not found" },
        };
      }

      const hashedPassword = await dragon.hash(params.newPassword);
      await User.update(
        { id: params.userId, companyId: params.companyId },
        { password: hashedPassword }
      );

      return { status: true };
    } catch (err: any) {
      logError(
        req.session.companyId,
        err.message,
        "ASSIGN_PASSWORD_ERROR",
        JSON.stringify({
          targetUserId: params.userId,
          adminUserId: req.session.userId,
          error: err,
        }),
        "high",
        `Failed to assign password for user: ${params.userId}`,
        req.session.userId
      );
      return {
        status: false,
        error: { target: "password", message: err.message },
      };
    }
  }

  @Mutation(() => UserResponse)
  async login(
    @Arg("params") params: EmailPasswordArgs,
    @Ctx() { req }: MyContext
  ): Promise<UserResponse> {
    try {
      const similarUser = await User.findOne({
        where: { email: params.email.toLowerCase() },
        relations: [
          "role",
          "employee",
          "employee.role",
          "employee.department",
          "permissions",
          "company",
        ],
      });

      if (!similarUser) {
        logError(
          "", // No company ID available yet
          "User not found",
          "LOGIN_USER_NOT_FOUND",
          JSON.stringify({ email: params.email.toLowerCase() }),
          "low",
          "Login attempt with non-existent email",
          undefined
        );
        return {
          error: {
            target: "general",
            message: "incorrect credentials!",
          },
        };
      }

      const valid = await dragon.verify(similarUser.password, params.password);
      if (!valid) {
        logError(
          similarUser.companyId,
          "Invalid password",
          "LOGIN_INVALID_PASSWORD",
          JSON.stringify({ userId: similarUser.id }),
          "low",
          `Failed login attempt for user: ${similarUser.id}`,
          undefined
        );
        return {
          error: {
            target: "general",
            message: "incorrect credentials!",
          },
        };
      }

      if (similarUser.deleted === true) {
        logError(
          similarUser.companyId,
          "Deleted user attempted login",
          "LOGIN_DELETED_USER",
          JSON.stringify({ userId: similarUser.id }),
          "medium",
          `Login attempt by deleted user: ${similarUser.id}`,
          undefined
        );
        return {
          error: {
            target: "general",
            message: "User is not active!",
          },
        };
      }

      // Set session data
      req.session.userId = similarUser.id;
      req.session.companyId = similarUser.companyId;
      req.session.role = similarUser.role?.name;

      // Generate JWT
      const token = generateToken(similarUser);

      return {
        user: similarUser,
        token,
      };
    } catch (err) {
      logError(
        "", // No company ID available yet
        err.message,
        "LOGIN_ERROR",
        JSON.stringify({ error: err, email: params.email }),
        "high",
        "Login process failed",
        undefined
      );
      return {
        error: {
          target: "general",
          message: "Something went wrong, try again!",
        },
      };
    }
  }

  @Query(() => User, { nullable: true })
  async me(@Ctx() { req }: MyContext): Promise<User | undefined> {
    try {
      const meUser = await User.findOne({
        where: {
          id: req.session.userId,
          companyId: req.session.companyId,
        },
        relations: [
          "role",
          "employee",
          "employee.role",
          "employee.department",
          "permissions",
          "company",
        ],
      });
      if (!meUser) {
        logError(
          req.session.companyId,
          "User not found",
          "ME_QUERY_USER_NOT_FOUND",
          JSON.stringify({ userId: req.session.userId }),
          "medium",
          `Me query failed - user not found: ${req.session.userId}`,
          req.session.userId
        );
      }
      return meUser;
    } catch (err) {
      logError(
        req.session.companyId,
        err.message,
        "ME_QUERY_ERROR",
        JSON.stringify({ userId: req.session.userId, error: err }),
        "high",
        `Me query failed for user: ${req.session.userId}`,
        req.session.userId
      );
      return undefined;
    }
  }

  @Mutation(() => Boolean)
  async logout(@Ctx() { req, res }: MyContext) {
    try {
      res.clearCookie(COOKIE_NAME);
      const sesh = await new Promise((resolve) => {
        req.session.destroy((err: any) => {
          console.log("we are destroying session");
          if (err) {
            console.log(err);
            return resolve(false);
          }
          return resolve(true);
        });
      });
      if (sesh) console.log(req.session);
      return sesh;
    } catch (err) {
      logError(
        req.session.companyId,
        err.message,
        "LOGOUT_ERROR",
        JSON.stringify({ userId: req.session.userId, error: err }),
        "medium",
        `Logout failed for user: ${req.session.userId}`,
        req.session.userId
      );
      return false;
    }
  }

  @Query(() => [User])
  @UseMiddleware(isAuth)
  @UseMiddleware(isAllowed(["admin", "employee"]))
  async getUsers(
    @Arg("roles", () => [String], { nullable: true }) roles: string[]
  ): Promise<User[]> {
    try {
      let reqRes: User[] = [];
      if (roles === null || roles === undefined || roles.length === 0)
        reqRes = await User.find({ relations: ["role", "company"] });
      else
        reqRes = await User.find({
          where: { roleId: In(roles) },
          relations: ["role", "company"],
        });
      return reqRes;
    } catch (err) {
      logError(
        "", // No company ID available in context
        err.message,
        "GET_USERS_ERROR",
        JSON.stringify({ roles, error: err }),
        "high",
        "Failed to fetch users list",
        undefined
      );
      return [];
    }
  }

  @Query(() => [User])
  @UseMiddleware(isAuth)
  async getEmployees(@Ctx() { req }: MyContext): Promise<User[]> {
    try {
      const employees = await User.find({
        where: { companyId: req.session.companyId },
        relations: [
          "role",
          "company",
          "permissions",
          "employee",
          "employee.department",
          "employee.role",
          "employee.store",
        ],
      });
      return employees.filter((user) => user.role.name === "employee");
    } catch (err) {
      logError(
        req.session.companyId,
        err.message,
        "GET_EMPLOYEES_ERROR",
        JSON.stringify({ error: err }),
        "high",
        "Failed to fetch employees list",
        req.session.userId
      );
      return [];
    }
  }

  @Query(() => [UndetailedUser])
  async getUndetailedEmployees(
    @Arg("companyId") companyId: string
  ): Promise<UndetailedUser[]> {
    try {
      const results = await User.find({
        where: { companyId: companyId },
        relations: ["employee", "role"],
      });
      return results
        .filter((user) => user.role.name === "employee")
        .map((user) => ({
          firstname: user.firstname,
          lastname: user.lastname,
          email: user.email,
          image: user.image,
        }));
    } catch (err) {
      logError(
        companyId,
        err.message,
        "GET_UNDETAILED_EMPLOYEES_ERROR",
        JSON.stringify({ companyId, error: err }),
        "high",
        `Failed to fetch undetailed employees for company: ${companyId}`,
        undefined
      );
      return [];
    }
  }

  @Query(() => User, { nullable: true })
  @UseMiddleware(isAuth)
  async getUser(
    @Arg("id") id: string,
    @Ctx() { req }: MyContext
  ): Promise<User | undefined> {
    try {
      const companyId = req.session.companyId;
      const user = await User.findOne({
        where: {
          id,
          companyId,
          deleted: false,
        },
        relations: [
          "role",
          "employee",
          "employee.role",
          "employee.department",
          "employee.store",
          "permissions",
          "company",
        ],
        cache: true,
      });

      if (user) {
        this.#logUserAccess({
          userId: id,
          companyId,
          action: "user_view",
          timestamp: new Date(),
        });
      } else {
        logError(
          companyId,
          "User not found",
          "GET_USER_NOT_FOUND",
          JSON.stringify({ userId: id }),
          "medium",
          `Get user failed - user not found: ${id}`,
          req.session.userId
        );
      }

      return user;
    } catch (err) {
      logError(
        req.session.companyId,
        err.message,
        "GET_USER_ERROR",
        JSON.stringify({ userId: id, error: err }),
        "high",
        `Failed to fetch user: ${id}`,
        req.session.userId
      );
      console.error(`Error fetching user with ID ${id}:`, err);
      return undefined;
    }
  }

  /**
   * Generate a PowerSync token for desktop API synchronization
   * This token is specifically for PowerSync authentication and contains
   * the companyId needed for multi-tenant data isolation
   */
  @Mutation(() => String)
  @UseMiddleware(isAuth)
  async getPowerSyncToken(@Ctx() { req }: MyContext): Promise<string> {
    try {
      const companyId = req.session.companyId;
      const userId = req.session.userId;

      if (!companyId || !userId) {
        throw new Error("User session not properly authenticated");
      }

      // Generate a JWKS-signed PowerSync token (1 hour lifetime)
      const powerSyncToken = await signPowerSyncJwt({
        userId: userId,
        companyId: companyId,
        role: req.session.role,
        email: req.session.email,
        sub: `user:${userId}`,
      });

      // Log the token generation for audit purposes
      logError(
        companyId,
        "PowerSync token generated",
        "POWERSYNC_TOKEN_GENERATED",
        JSON.stringify({ userId, companyId }),
        "low",
        `PowerSync token generated for user: ${userId}`,
        userId
      );

      return powerSyncToken;
    } catch (err) {
      logError(
        req.session.companyId,
        err.message,
        "POWERSYNC_TOKEN_ERROR",
        JSON.stringify({ userId: req.session.userId, error: err }),
        "high",
        `Failed to generate PowerSync token for user: ${req.session.userId}`,
        req.session.userId
      );
      throw new Error("Failed to generate PowerSync token");
    }
  }

  async #logUserAccess(log: {
    userId: string;
    companyId: string;
    action: string;
    timestamp: Date;
  }): Promise<void> {
    // Implementation of logging user access
    // You can store this in a database or logging service
    console.log(`User access logged: ${JSON.stringify(log)}`);
  }
}
