// Chakra imports
import {
  Box,
  Button,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Grid,
  GridItem,
  Input,
  Text,
  useColorModeValue,
  useToast,
} from "@chakra-ui/react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import {
  Role,
  useAddRoleMutation,
  useDeleteRoleMutation,
  useEditRoleMutation,
  useGetRolesQuery,
} from "../generated/graphql";
import DeleteConfirm from "../components/DeleteConfirmation";
import CardHeader from "../components/Card/CardHeader";
import Card from "../components/Card/Card";
import CardBody from "../components/Card/CardBody";
import RoleRow from "../components/Tables/RoleRow";

interface SubmitRole {
  name: string;
}

interface IFormInput {
  name: string;
}

const Roles = () => {
  const [{ data }, getRolesAsync] = useGetRolesQuery();

  const [error, setError] = useState("");

  const [openDelete, setopenDelete] = useState({ open: false, id: -1000000 });

  const [, addRole] = useAddRoleMutation();

  const [, editRole] = useEditRoleMutation();

  const [{ fetching: loadingDelete }, deleteRole] = useDeleteRoleMutation();

  const toast = useToast({
    position: "top",
  });
  const [isEdit, setIsEdit] = useState(false);
  const [roleToEdit, setRoleToEdit] = useState<Role>();

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<IFormInput>();

  const textColor = useColorModeValue("gray.700", "white");
  const bgForm = useColorModeValue("white", "navy.800");

  const editActivated = (name: string) => {
    const role = data?.getRoles.filter((c) => {
      return c.name === name;
    })[0];
    setIsEdit(true);
    setRoleToEdit(role as any);
  };

  async function onSubmit(values: SubmitRole) {
    console.log("submitted values: ", values);
    setError("");
    if (isEdit) {
      const { data } = await editRole({
        id: roleToEdit!.id,
        args: {
          name: values.name,
        },
      });
      if (data?.editRole.error) {
        console.log("The error came back: ", data.editRole.error.message);
        return setError(data?.editRole.error.message);
      } else {
        getRolesAsync({ requestPolicy: "network-only" });
        return toast({
          title: "Role edited successful!",
          variant: "left-accent",
          status: "success",
          isClosable: true,
        });
      }
    } else {
      const { data } = await addRole({
        name: values.name,
      });
      if (data?.addRole.error) {
        console.log("The error came back: ", data.addRole.error.message);
        return setError(data?.addRole.error.message);
      } else {
        reset();
        getRolesAsync({ requestPolicy: "network-only" });
        return toast({
          title: "Role added successful!",
          variant: "left-accent",
          status: "success",
          isClosable: true,
        });
      }
    }
  }

  const callDeleteRole: any = async (id: number) => {
    console.log("delete with id: ", id);
    if (id < 0) {
      console.log("we got id: ", id);
      let role = data?.getRoles.find((c) => {
        return c.id === id;
      });
      if (role) {
        console.log("we used id: ", role.id);
        const { error } = await deleteRole({ id: role.id });
        setopenDelete({ open: false, id: -1000000 });
        await getRolesAsync({ requestPolicy: "network-only" });
        if (error)
          toast({
            title: "Role delete failed!",
            variant: "left-accent",
            status: "error",
            isClosable: true,
          });
        else {
          toast({
            title: "Role delete successful!",
            variant: "left-accent",
            status: "success",
            isClosable: true,
          });
        }
        toast({
          title: "Role delete successful!",
          variant: "left-accent",
          status: "success",
          isClosable: true,
        });
      }
    } else {
      const { error } = await deleteRole({ id });
      setopenDelete({ open: false, id: -1000000 });
      if (error)
        toast({
          title: "Role delete failed!",
          variant: "left-accent",
          status: "error",
          isClosable: true,
        });
      else {
        await getRolesAsync({ requestPolicy: "network-only" });
        toast({
          title: "Role delete successful!",
          variant: "left-accent",
          status: "success",
          isClosable: true,
        });
      }
    }
  };

  return (
    <Flex direction="column" pt={{ base: "120px", md: "75px" }}>
      <DeleteConfirm
        loading={loadingDelete}
        item="Role"
        open={openDelete.open}
        id={openDelete.id!}
        feedback={callDeleteRole}
        nofeedback={() => setopenDelete({ open: false, id: -1000000 })}
      />

      <Grid templateColumns={{ sm: "1fr", lg: "2fr 1.2fr" }} templateRows="1fr">
        <Box>
          <Grid
            templateColumns={{
              sm: "1fr",
              md: "1fr 1fr",
              xl: "1fr 1fr 1fr 1fr",
            }}
            templateRows={{ sm: "auto auto auto", md: "1fr auto", xl: "1fr" }}
            gap="13px"
            mb={55}
          >
            <GridItem rowSpan={2} colSpan={3}></GridItem>
          </Grid>
          <Box>
            <Flex
              zIndex="2"
              direction="column"
              w="445px"
              background="transparent"
              borderRadius="15px"
              p="40px"
              mx={{ base: "100px" }}
              m={{ base: "20px", md: "auto" }}
              bg={bgForm}
            >
              <Text
                fontSize="xl"
                color={textColor}
                fontWeight="bold"
                textAlign="center"
                mb="22px"
              >
                {isEdit ? "EDIT ROLE" : "ADD NEW ROLE"}
              </Text>
              {error && (
                <Text mb="10px" color="red.500" textColor="red.300">
                  {error}
                </Text>
              )}
              <form onSubmit={handleSubmit(onSubmit as any)}>
                <FormControl mb={5} isInvalid={errors.name as any}>
                  <FormLabel htmlFor="name">Role name</FormLabel>
                  <Input
                    variant="filled"
                    fontSize="sm"
                    ms="4px"
                    type="text"
                    placeholder="Role Name"
                    mb="4px"
                    size="lg"
                    id="name"
                    {...register("name", {
                      required: "This is required",
                    })}
                    defaultValue={
                      isEdit ? (roleToEdit ? roleToEdit.name : "") : ""
                    }
                  />
                  <FormErrorMessage mb="10px">
                    {errors.name && (errors.name.message as any)}
                  </FormErrorMessage>
                </FormControl>
                <Button
                  fontSize="14px"
                  variant="dark"
                  fontWeight="bold"
                  w="100%"
                  h="45"
                  mb="24px"
                  mt={4}
                  colorScheme="teal"
                  isLoading={isSubmitting}
                  type="submit"
                >
                  {isEdit ? "Edit" : "Add"} Role
                </Button>
                {isEdit ? (
                  <Button
                    fontSize="14px"
                    // variant="dark"
                    fontWeight="bold"
                    w="100%"
                    h="45"
                    mb="24px"
                    mt={4}
                    backgroundColor={"darkkhaki"}
                    color={"white"}
                    type="reset"
                    onClick={() => {
                      setRoleToEdit(undefined);
                      setIsEdit(false);
                      reset();
                    }}
                  >
                    Cancel Edit
                  </Button>
                ) : null}
              </form>
            </Flex>
          </Box>
        </Box>

        <Card my={{ lg: "24px" }} me={{ lg: "24px" }}>
          <Flex direction="column">
            <CardHeader py="12px">
              <Text color={textColor} fontSize="lg" fontWeight="bold">
                Roles
              </Text>
            </CardHeader>
            <CardBody>
              <Flex direction="column" w="100%">
                {data?.getRoles && data?.getRoles.length > 0 ? (
                  data?.getRoles
                    .filter((r) => r.sys === false)
                    .map((row) => {
                      return (
                        <RoleRow
                          callOnClick={() =>
                            setopenDelete({ open: true, id: row.id })
                          }
                          editActivated={editActivated}
                          name={row.name}
                          key={row.id}
                        />
                      );
                    })
                ) : (
                  <Text>No roles in the DB!</Text>
                )}
              </Flex>
            </CardBody>
          </Flex>
        </Card>
      </Grid>
    </Flex>
  );
};

export default Roles;
