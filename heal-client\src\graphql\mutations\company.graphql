mutation registerCompany($params: RegisterCompanyArgs!) {
  registerCompany(params: $params) {
    id
    status
    error {
      target
      message
    }
  }
}

mutation addCompanyWithAddress($params: RegisterCompanyAddressedArgs!) {
  addCompanyWithAddress(params: $params) {
    status
    error {
      target
      message
    }
  }
}

mutation addStore($args: StoreInput!) {
  addStore(args: $args) {
    status
    error {
      target
      message
    }
  }
}

mutation editStore($args: StoreEditInput!) {
  editStore(args: $args) {
    status
    error {
      target
      message
    }
  }
}

mutation addSchedules($args: ScheduleBulkArgs!) {
  addSchedules(args: $args) {
    status
    error {
      target
      message
    }
    schedules {
      id
      day
      onTime
      offTime
      description
      clinicId
      employeeId
    }
  }
}

mutation editSchedules($args: ScheduleEditBulkArgs!) {
  editSchedules(args: $args) {
    status
    error {
      target
      message
    }
    schedules {
      id
      day
      onTime
      offTime
      description
      clinicId
      employeeId
    }
  }
}
