import { randomBytes } from "crypto";
import IORedis from "ioredis";
import { JWT, JWK } from "jose";

type LoadedPrivateKey = { alg: string; kid: string; key: any };
type LoadedPublicJwk = Record<string, any>;

let cachedPrivate: LoadedPrivateKey | null = null;
let cachedPublic: LoadedPublicJwk | null = null;
let redisClient: IORedis | null = null;

function getRedis(): IORedis {
  if (!redisClient) {
    // Align with existing server defaults
    const host = process.env.REDIS_HOST || "redis";
    const port = parseInt(process.env.REDIS_PORT || "6379", 10);
    console.log(`🔑 [JWKS] Connecting to Redis at ${host}:${port}`);
    redisClient = new IORedis({
      host,
      port,
      maxRetriesPerRequest: 3,
      lazyConnect: true,
    });

    // Add error handling
    redisClient.on("error", (err) => {
      console.error("❌ [JWKS] Redis connection error:", err);
    });

    redisClient.on("connect", () => {
      console.log("✅ [JWKS] Redis connected successfully");
    });
  }
  return redisClient;
}

async function generateEphemeralPair() {
  const alg = "RS256";
  const kid = `powersync-${randomBytes(5).toString("hex")}`;
  const key = await JWK.generate("RSA", 2048, { alg, use: "sig", kid });
  const privateJwk = key.toJWK(true);
  const publicJwk = key.toJWK(false);
  return { privateJwk, publicJwk };
}

export async function ensurePowerSyncKeys() {
  if (cachedPrivate && cachedPublic) {
    console.log("🔑 [JWKS] Using cached keys with KID:", cachedPrivate.kid);
    return;
  }

  console.log("🔑 [JWKS] Ensuring PowerSync keys are available...");

  const base64Private = process.env.POWERSYNC_PRIVATE_KEY;
  const base64Public = process.env.POWERSYNC_PUBLIC_KEY;

  let privateJwk: any;
  let publicJwk: any;

  if (base64Private && base64Public) {
    console.log("🔑 [JWKS] Using environment variable keys");
    privateJwk = JSON.parse(
      Buffer.from(base64Private, "base64").toString("utf-8")
    );
    publicJwk = JSON.parse(
      Buffer.from(base64Public, "base64").toString("utf-8")
    );
  } else {
    console.log("🔑 [JWKS] No environment keys, checking Redis...");
    // Try Redis first
    try {
      const redis = getRedis();
      const [storedPrivate, storedPublic] = await redis.mget(
        "POWERSYNC_JWK_PRIVATE",
        "POWERSYNC_JWK_PUBLIC"
      );

      if (storedPrivate && storedPublic) {
        console.log("🔑 [JWKS] Found keys in Redis");
        privateJwk = JSON.parse(storedPrivate);
        publicJwk = JSON.parse(storedPublic);
      } else {
        console.log("🔑 [JWKS] No keys in Redis, generating new ones...");
        // Generate a fresh keypair and persist to Redis with TTL matching token lifetime
        const generated = await generateEphemeralPair();
        privateJwk = generated.privateJwk;
        publicJwk = generated.publicJwk;

        const ttlSeconds = parseInt(
          process.env.POWERSYNC_JWKS_TTL_SECONDS || "3600",
          10
        ); // 1 hour default

        console.log(`🔑 [JWKS] Storing keys in Redis with TTL: ${ttlSeconds}s`);
        await redis.set(
          "POWERSYNC_JWK_PRIVATE",
          JSON.stringify(privateJwk),
          "EX",
          ttlSeconds
        );
        await redis.set(
          "POWERSYNC_JWK_PUBLIC",
          JSON.stringify(publicJwk),
          "EX",
          ttlSeconds
        );
        console.log("🔑 [JWKS] Keys stored in Redis successfully");
      }
    } catch (redisError) {
      console.error(
        "❌ [JWKS] Redis error, generating keys without storage:",
        redisError
      );
      // Fallback: generate keys without Redis storage
      const generated = await generateEphemeralPair();
      privateJwk = generated.privateJwk;
      publicJwk = generated.publicJwk;
    }
  }

  cachedPrivate = {
    alg: privateJwk.alg,
    kid: privateJwk.kid,
    key: await JWK.asKey(privateJwk),
  };
  cachedPublic = publicJwk;

  console.log("🔑 [JWKS] Keys ready with KID:", cachedPrivate.kid);
}

export async function getPublicJwks() {
  await ensurePowerSyncKeys();
  console.log(
    "🔑 [JWKS] Generated public JWKS:",
    JSON.stringify(cachedPublic, null, 2)
  );
  return { keys: [cachedPublic] };
}

export async function signPowerSyncJwt(claims: Record<string, any>) {
  await ensurePowerSyncKeys();
  const alg = cachedPrivate!.alg;
  const kid = cachedPrivate!.kid;
  const key = cachedPrivate!.key;

  console.log("🔑 [JWKS] Signing PowerSync JWT with KID:", kid);

  const audience =
    process.env.POWERSYNC_URL || "https://talisiasync.duckdns.org";
  const issuer = process.env.JWT_ISSUER || "talisia-api";

  const payload = {
    ...claims,
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + 60 * 60, // 1h
  };
  const header = { alg, kid, typ: "JWT" };
  const token = JWT.sign(payload, key, {
    header,
    issuer,
    audience,
  });
  return token;
}
