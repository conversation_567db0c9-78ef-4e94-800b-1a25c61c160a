import express, { Request, Response, Router } from "express";
import type { Connection, EntityManager } from "typeorm";
import { verifyToken } from "../utils/jwt";

interface SyncChange {
  id: string;
  table: string;
  operation: "INSERT" | "UPDATE" | "DELETE";
  data: any;
  timestamp: string;
  companyId?: string;
}

interface SyncResponse {
  success: boolean;
  processed: number;
  stats: {
    inserted: number;
    updated: number;
    deleted: number;
    skipped: number;
    errors: number;
  };
  processingTime: number;
  errors?: Array<{
    table: string;
    operation: string;
    id: string;
    error: string;
  }>;
}

export function createDataRouter(conn: Connection): Router {
  const router = express.Router();

  // Enhanced sync upload endpoint for transaction-based uploads
  router.post(
    "/api/sync/upload",
    async (req: Request, res: Response): Promise<any> => {
      const startTime = Date.now();

      try {
        // Authentication
        const token = req.headers.authorization?.split(" ")[1];
        if (!token) {
          return res
            .status(401)
            .json({ error: "No authorization token provided" });
        }

        const payload = verifyToken(token);
        if (!payload) {
          return res.status(401).json({ error: "Invalid authorization token" });
        }

        const companyId = payload.companyId;
        console.log(
          `🔐 [PowerSync] Processing sync request for company: ${companyId}`
        );

        // Handle both legacy single-table and new transaction-based uploads
        const { table, changes, transaction } = req.body;

        let syncChanges: SyncChange[] = [];
        let isTransactionUpload = false;

        if (transaction && Array.isArray(changes)) {
          // New transaction-based upload
          isTransactionUpload = true;
          syncChanges = changes.map((change) => ({
            ...change,
            companyId: change.companyId || companyId,
          }));

          console.log(
            `📦 [PowerSync] Processing transaction with ${syncChanges.length} changes across multiple tables`
          );
        } else if (table && Array.isArray(changes)) {
          // Legacy single-table upload (backward compatibility)
          syncChanges = changes.map((change) => ({
            ...change,
            table,
            companyId: change.companyId || companyId,
          }));

          console.log(
            `📋 [PowerSync] Processing single-table upload for ${table} with ${changes.length} changes`
          );
        } else {
          return res.status(400).json({
            error:
              "Invalid request body. Expected either 'table' and 'changes' array, or 'transaction' and 'changes' array.",
          });
        }

        // Validate all changes
        const validationResult = validateSyncChanges(syncChanges, companyId);
        if (!validationResult.valid) {
          return res.status(400).json({
            error: "Invalid sync changes",
            details: validationResult.errors,
          });
        }

        // Process the sync operation
        const result = await processSyncChanges(
          conn,
          syncChanges,
          companyId,
          isTransactionUpload
        );

        const processingTime = Date.now() - startTime;

        const response: SyncResponse = {
          success: result.success,
          processed: result.processed,
          stats: result.stats,
          processingTime,
          errors: result.errors,
        };

        console.log(`✅ [PowerSync] Sync completed in ${processingTime}ms:`, {
          processed: result.processed,
          inserted: result.stats.inserted,
          updated: result.stats.updated,
          deleted: result.stats.deleted,
          errors: result.stats.errors,
        });

        return res.json(response);
      } catch (error: any) {
        const processingTime = Date.now() - startTime;
        console.error("❌ [PowerSync] Sync upload failed:", error);

        return res.status(500).json({
          error: "Internal server error",
          message: error.message,
          processingTime,
        });
      }
    }
  );

  return router;
}

/**
 * Validate sync changes before processing
 */
function validateSyncChanges(
  changes: SyncChange[],
  companyId: string
): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  const excludedEntities = ["ErrorLog", "error_log", "errorlog"];

  for (const change of changes) {
    // Check required fields
    if (!change.id || !change.table || !change.operation || !change.data) {
      errors.push(
        `Missing required fields in change: ${JSON.stringify(change)}`
      );
      continue;
    }

    // Check operation type
    if (!["INSERT", "UPDATE", "DELETE"].includes(change.operation)) {
      errors.push(
        `Invalid operation '${change.operation}' for change ${change.id}`
      );
      continue;
    }

    // Check if entity is syncable
    if (excludedEntities.includes(change.table)) {
      errors.push(`Entity '${change.table}' is not syncable`);
      continue;
    }

    // Check company ID
    if (change.companyId && change.companyId !== companyId) {
      errors.push(
        `Company ID mismatch: expected ${companyId}, got ${change.companyId}`
      );
      continue;
    }
  }

  return {
    valid: errors.length === 0,
    errors,
  };
}

/**
 * Process sync changes with proper CRUD operations and constraint management
 */
async function processSyncChanges(
  conn: Connection,
  changes: SyncChange[],
  companyId: string,
  isTransactionUpload: boolean
): Promise<{
  success: boolean;
  processed: number;
  stats: {
    inserted: number;
    updated: number;
    deleted: number;
    skipped: number;
    errors: number;
  };
  errors?: Array<{
    table: string;
    operation: string;
    id: string;
    error: string;
  }>;
}> {
  const stats = {
    inserted: 0,
    updated: 0,
    deleted: 0,
    skipped: 0,
    errors: 0,
  };

  const errors: Array<{
    table: string;
    operation: string;
    id: string;
    error: string;
  }> = [];

  let processed = 0;

  try {
    // Group changes by table for efficient processing
    const changesByTable = groupChangesByTable(changes);

    if (isTransactionUpload) {
      // For transaction uploads, use database transaction with constraint management
      await conn.transaction(async (queryRunner) => {
        console.log(
          `🔄 [PowerSync] Starting database transaction with constraint management`
        );

        // Disable constraints and triggers for the session
        await queryRunner.query("SET session_replication_role = replica");

        try {
          // Process all changes in the transaction
          for (const [tableName, tableChanges] of Object.entries(
            changesByTable
          )) {
            const tableStats = await processTableChanges(
              queryRunner,
              tableName,
              tableChanges,
              companyId
            );

            // Aggregate stats
            stats.inserted += tableStats.inserted;
            stats.updated += tableStats.updated;
            stats.deleted += tableStats.deleted;
            stats.skipped += tableStats.skipped;
            stats.errors += tableStats.errors;
            processed += tableStats.processed;

            // Collect errors
            if (tableStats.errorDetails) {
              errors.push(...tableStats.errorDetails);
            }
          }
        } finally {
          // Re-enable constraints and triggers
          await queryRunner.query("SET session_replication_role = DEFAULT");
          console.log(`✅ [PowerSync] Re-enabled constraints and triggers`);
        }
      });
    } else {
      await conn.transaction(async (queryRunner) => {
        for (const [tableName, tableChanges] of Object.entries(
          changesByTable
        )) {
          const tableStats = await processTableChanges(
            queryRunner,
            tableName,
            tableChanges,
            companyId
          );

          // Aggregate stats
          stats.inserted += tableStats.inserted;
          stats.updated += tableStats.updated;
          stats.deleted += tableStats.deleted;
          stats.skipped += tableStats.skipped;
          stats.errors += tableStats.errors;
          processed += tableStats.processed;

          // Collect errors
          if (tableStats.errorDetails) {
            errors.push(...tableStats.errorDetails);
          }
        }
      });
    }

    return {
      success: stats.errors === 0,
      processed,
      stats,
      errors: errors.length > 0 ? errors : undefined,
    };
  } catch (error: any) {
    console.error("❌ [PowerSync] Error processing sync changes:", error);
    throw error;
  }
}

/**
 * Group changes by table for efficient processing
 */
function groupChangesByTable(
  changes: SyncChange[]
): Record<string, SyncChange[]> {
  const grouped: Record<string, SyncChange[]> = {};

  for (const change of changes) {
    if (!grouped[change.table]) {
      grouped[change.table] = [];
    }
    grouped[change.table].push(change);
  }

  return grouped;
}

/**
 * Process changes for a specific table using direct SQL queries for efficiency
 */
async function processTableChanges(
  queryRunner: EntityManager,
  tableName: string,
  changes: SyncChange[],
  companyId: string
): Promise<{
  inserted: number;
  updated: number;
  deleted: number;
  skipped: number;
  errors: number;
  processed: number;
  errorDetails?: Array<{
    table: string;
    operation: string;
    id: string;
    error: string;
  }>;
}> {
  const stats = {
    inserted: 0,
    updated: 0,
    deleted: 0,
    skipped: 0,
    errors: 0,
    processed: 0,
  };

  const errors: Array<{
    table: string;
    operation: string;
    id: string;
    error: string;
  }> = [];

  try {
    for (const change of changes) {
      try {
        switch (change.operation) {
          case "INSERT":
            await executeInsert(queryRunner, tableName, change, companyId);
            stats.inserted++;
            break;

          case "UPDATE":
            const updateResult = await executeUpdate(
              queryRunner,
              tableName,
              change,
              companyId
            );
            if (updateResult === "updated") {
              stats.updated++;
            } else if (updateResult === "inserted") {
              stats.inserted++;
            } else {
              stats.skipped++;
            }
            break;

          case "DELETE":
            const deleteResult = await executeDelete(
              queryRunner,
              tableName,
              change,
              companyId
            );
            if (deleteResult) {
              stats.deleted++;
            } else {
              stats.skipped++;
            }
            break;
        }

        stats.processed++;
      } catch (changeError: any) {
        console.error(
          `❌ [PowerSync] Error processing change for ${tableName}:`,
          changeError
        );

        errors.push({
          table: tableName,
          operation: change.operation,
          id: change.id,
          error: changeError.message,
        });

        stats.errors++;
      }
    }

    return {
      ...stats,
      errorDetails: errors.length > 0 ? errors : undefined,
    };
  } catch (error: any) {
    console.error(`❌ [PowerSync] Error processing table ${tableName}:`, error);
    throw error;
  }
}

/**
 * Execute INSERT operation using direct SQL
 */
async function executeInsert(
  queryRunner: any,
  tableName: string,
  change: SyncChange,
  companyId: string
): Promise<void> {
  const data = { ...change.data, companyId };

  // Build dynamic INSERT query
  const columns = Object.keys(data);
  const values = Object.values(data);
  const placeholders = values.map((_, index) => `$${index + 1}`).join(", ");

  const query = `INSERT INTO "${tableName}" (${columns
    .map((col) => `"${col}"`)
    .join(", ")}) VALUES (${placeholders})`;

  await queryRunner.query(query, values);
}

/**
 * Execute UPDATE operation using direct SQL with timestamp validation
 */
async function executeUpdate(
  queryRunner: any,
  tableName: string,
  change: SyncChange,
  companyId: string
): Promise<"updated" | "inserted" | "skipped"> {
  const data = { ...change.data, companyId };

  // First check if record exists and get current updatedAt
  const checkQuery = `SELECT "updatedAt" FROM "${tableName}" WHERE "id" = $1 AND "companyId" = $2`;
  const existing = await queryRunner.query(checkQuery, [change.id, companyId]);

  if (existing.length === 0) {
    // Record doesn't exist, treat as insert
    await executeInsert(queryRunner, tableName, change, companyId);
    return "inserted";
  }

  const existingUpdatedAt = new Date(existing[0].updatedAt);
  const changeUpdatedAt = new Date(change.timestamp);

  if (changeUpdatedAt > existingUpdatedAt) {
    // Build dynamic UPDATE query
    const updateFields = Object.keys(data).filter((key) => key !== "id");
    const setClause = updateFields
      .map((field, index) => `"${field}" = $${index + 3}`)
      .join(", ");

    const query = `UPDATE "${tableName}" SET ${setClause} WHERE "id" = $1 AND "companyId" = $2`;
    const values = [
      change.id,
      companyId,
      ...updateFields.map((field) => data[field]),
    ];

    await queryRunner.query(query, values);
    return "updated";
  } else {
    return "skipped";
  }
}

/**
 * Execute DELETE operation using direct SQL
 */
async function executeDelete(
  queryRunner: any,
  tableName: string,
  change: SyncChange,
  companyId: string
): Promise<boolean> {
  // Check if record exists before deleting
  const checkQuery = `SELECT "id" FROM "${tableName}" WHERE "id" = $1 AND "companyId" = $2`;
  const existing = await queryRunner.query(checkQuery, [change.id, companyId]);

  if (existing.length === 0) {
    return false; // Record doesn't exist
  }

  const deleteQuery = `DELETE FROM "${tableName}" WHERE "id" = $1 AND "companyId" = $2`;
  await queryRunner.query(deleteQuery, [change.id, companyId]);

  return true;
}
