import { Field, ObjectType } from "type-graphql";
import { Column, Entity, JoinColumn, ManyToOne } from "typeorm";
import { AuditBaseEntity } from "../AuditEntity";
import { Item } from "../Item";

@ObjectType()
@Entity()
export class Import extends AuditBaseEntity {
  @Field(() => String)
  @Column({ type: "timestamp" })
  importDate: Date;

  @Field(() => String, { nullable: true })
  @Column({ type: "timestamp", nullable: true })
  expireDate: Date; // track expiration date for consumables and maintenance dates for capital goods

  @Field()
  @Column({ type: "text" })
  supplier: string;

  @Field()
  @Column({ type: "float", default: 0 })
  quantity!: number;

  @Field()
  @Column({ type: "text" })
  unit: string;

  @Field({ nullable: true })
  @Column({ type: "bigint", nullable: true })
  importPrice: number;

  @Field()
  @Column({ type: "bigint" })
  sellingPrice: number;

  @Field({ nullable: true })
  @Column({ type: "text", nullable: true })
  receipt: string;

  @Field()
  @Column({ type: "uuid" })
  itemId: string;

  @Field(() => Item)
  @ManyToOne(() => Item, (item) => item.imports)
  @JoinColumn([
    { name: "itemId", referencedColumnName: "id" },
    { name: "companyId", referencedColumnName: "companyId" },
  ])
  item: Item;

  @Field({ nullable: true })
  @Column({ type: "text", nullable: true })
  batch: string;
}
